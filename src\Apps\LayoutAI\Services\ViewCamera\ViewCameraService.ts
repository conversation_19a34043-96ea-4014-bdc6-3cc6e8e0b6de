import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TRoomEntity } from "../../Layout/TLayoutEntities/TRoomEntity";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "../../Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { ViewCameraEntityRealType, ViewCameraRuler } from "./ViewCameraRuler";
import { Scene3D } from "../../Scene3D/Scene3D";
import { FigureViewControls } from "../../Scene3D/controls/FigureViewControls";
import { Scene3DManager } from "../../Scene3D/Scene3DManager";
import { PanoramaViewCameraConfigs } from "./configs/PanoramaViewCameraConfigs";
import { ViewCameraRulerFactory } from "./handles/ViewCameraRulerFactory";
import { TSubSpaceAreaEntity } from "../../Layout/TLayoutEntities/TSubSpaceAreaEntity";

/**
 * @description 视角生成服务
 */
export class ViewCameraService {
    private static _instance: ViewCameraService;

    constructor() {
        // 便于调试
        (globalThis as any).ViewCameraService = this;

        ViewCameraRulerFactory.init();
    }

    public static get instance(): ViewCameraService {
        if (!this._instance) {
            this._instance = new ViewCameraService();
        }
        return this._instance;
    }

    public rulers: ViewCameraRuler[] = PanoramaViewCameraConfigs;

    public setRulers(rulers: ViewCameraRuler[]) {
        this.rulers = rulers;
    }

    /**
     * 根据视角规则生成视角
     */
    public generateViewCamera(roomEntity: TRoomEntity, options: IViewCameraGenerateOptions, areaEntitys?: TSubSpaceAreaEntity[]): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        for (let ruler of this.rulers) {
            let handler = ViewCameraRulerFactory.getHandler(ruler.typeId);
            if (handler) { 
                let result = handler.handle(ruler, roomEntity, options, areaEntitys);
                entities.push(...result);
            } else {
                console.error(`未实现类型为${ruler.typeId}的视角规则`);
            }
        }
        return entities;
    }

    /**
     * 获取所有视角
     */
    public getViewCameraEntites() {
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let view_cameras = container._ext_drawing_entities.filter(
            entity => entity.realType === ViewCameraEntityRealType.ViewCamera
        ) as TViewCameraEntity[];
        return view_cameras;
    }

    /**
     * 获取指定视角
     * @param uuid 指定的uuid
     */
    public getViewCameraEntityByUuid(uuid: string) {
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let view_cameras = container._ext_drawing_entities.filter(
            entity => entity.realType === ViewCameraEntityRealType.ViewCamera && entity._uuid === uuid
        ) as TViewCameraEntity[];
        return view_cameras;
    }

    /**
     * 切换视角规则，并保持位置不变
     */
    public async handleViewCameraChange(options: IViewCameraGenerateOptions = {}) {
        // methods 0: 默认; 1、[公牛]模式 2、新视角规则 3、全景视角规则
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let scene3d = LayoutAI_App.instance.scene3D as Scene3D;
        const { methods } = options;
        // 更新规则
        if (TViewCameraEntity.currentMethods == methods) return;
        TViewCameraEntity.updateViewCameraEntities(container, null, options);

        const sceneManager = LayoutAI_App.instance.scene3DManager as Scene3DManager;
        // 使用sceneManager来统一更新cameraViews
        await sceneManager._updateCameraViews({ update_imgs: true, force: false });
    }

    /**
     * 获取当前视角所在/指向的房间
     */
    public getCurrentViewRoom(): TRoomEntity | null {
        const manager = LayoutAI_App.instance as TAppManagerBase;
        const scene3d = manager.scene3D;
        const controls = scene3d.active_controls as FigureViewControls;
        const position = controls.getViewPosition(1);

        // 1. 优先使用当前视角
        if (controls.target_view_entity && controls._checkViewCameraValid()) {
            return controls.target_view_entity._room_entity;
        }

        // 2. 否则检查相机位置是否在某个房间内
        for (let room of manager.layout_container._rooms) {
            if (room.room_shape._poly.containsPoint(position)) {
                return room._room_entity;
            }
        }
        // 3. 如果不在任何房间内，寻找最近的房间
        let nearestRoom = null;
        let minDistance = Infinity;
        for (let room of manager.layout_container._rooms) {
            const roomCenter = room._room_entity._main_rect.rect_center;
            const distance = position.distanceTo(roomCenter);
            if (distance < minDistance) {
                minDistance = distance;
                nearestRoom = room._room_entity;
            }
        }
        return nearestRoom;
    }
}

(globalThis as any).ViewCameraService = ViewCameraService;
