import React, { useEffect, useRef, useState, useContext } from 'react';
import { Tabs, Carousel, message } from '@svg/antd';
import useStyles from './style';
import style from './index.module.less';
import { LoadingOutlined } from '@ant-design/icons'
import { getKgSchemeList, getStyles } from "@/Apps/AI2Design/Services";
import { getaiCongig } from '@/services/user';
import { useStore } from '@/models';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { EventName } from '@/Apps/EventSystem';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { DesignContext } from '@/pages/Design/design';
import { useTranslation } from 'react-i18next'
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample";
import { checkIsMobile } from '@/config';
import { observer } from "mobx-react-lite";
import RoomAreaBtns from '../roomAreaBtns/roomAreaBtns';
import { PanelContainer } from '@svg/antd-cloud-design';
import type { TabsProps } from '@svg/antd'
import { MaterialService } from '@/Apps/LayoutAI/Services/MaterialMatching/MaterialService';
import { If,Then } from 'react-if';
import { PERMISSIONS } from '@/config/permissions';
import { Permissions } from '@/Apps/LayoutAI/setting/Permissions';
import { SdkService } from '@/services/SdkService';
/**
 * @description 筛选页
 */
const SeriesList: React.FC<any> = ({ type }) => {
  const { t } = useTranslation()
  interface Params {
    orderBy: string;
    ruleType: number;
    pageSize: number;
    pageIndex: number;
    schemeKeyWord: string;
    ruleKeyWord: string;
    spaceName: any;
    schemeStyleId: string;
    ruleStyleId: string;
    queryType: number;
  }
  interface ScreenMethods {
    reSetScreenOut: () => void;
    reSetTypeScreenOut: () => void;
    scrollTo: (x: number, y: number) => void;
  }
  let store = useStore();
  const { styles } = useStyles();

  const {
    waitingToFurnishRemaining,
    setWaitingToFurnishRemaining
  } = useContext(DesignContext);

  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />
  const [preparation, setPreparation] = useState<boolean>(false); //是否加载中
  const [scroll, setScroll] = useState<boolean>(false); //左侧列表数据加载中，不让滚动
  const [recordCount, setTotal] = useState<number>(0); //总数
  const [loading, setLoading] = useState(false);
  const roomRef = useRef(null);
  const styleRef = useRef<ScreenMethods>(null);
  const spaceRef = useRef<ScreenMethods>(null);
  const lineRef = useRef(null);
  const [recordList, setRecordList] = useState<any>([]);
  const [styleList, setStyleList] = useState<any>([]); // 风格列表
  const [spaceList, setSpaceList] = useState<any>([]); //空间列表
  const [options, setOptions] = useState(['全案风格']); /*[i18n:ignore]*/
  const [width, setWidth] = useState(checkIsMobile() ? 260 : 360);
  const [tabPosition, setTabPosition] = useState<string>(store.userStore.userInfo?.isFactory ? '2' : '1');
  const [isActive, setIsActive] = useState(1);
  const [showPopover, setShowPopover] = useState<any>({});
  const [seriesScopeAppliedMap, setSeriesScopeAppliedMap] = useState<{ [key: string]: { soft?: boolean, hard?: boolean, cabinet?: boolean } }>({});
  const [anotherAppearance, setAnotherAppearance] = useState<boolean>(waitingToFurnishRemaining);
  const [isShow, setIsShow] = useState(false);
  const [panelItem, setPanelItem] = useState<any>(null);
  const [customizedMaterialList, setCustomizedMaterialList] = useState<any>([]);
  const [softMaterialList, setSoftMaterialList] = useState<any>([]);
  const [hardMaterialList, setHardMaterialList] = useState<any>([]);
  const [materialList, setMaterialList] = useState<any>([]);
  const [activeKey, setActiveKey] = useState<string>('1');
  const modelFlagsMap = {
    成品: ['1', '2', '3', '4', '15', '37', '38'],
    定制: ['10', '11', '12', '13', '17', '19', '20', '21', '22', '23', '24', '28'],
    贴图: ['8', '14', '26', '18', '25', '36', '41'],
  };



  const [prams, setPrams] = useState<Params>({
    orderBy: 'sort asc',
    ruleType: store.userStore.userInfo?.isFactory ? 2 : 1,
    pageSize: 100,
    pageIndex: 1,
    schemeKeyWord: '',
    ruleKeyWord: '',
    spaceName: null,
    schemeStyleId: '',
    ruleStyleId: '',
    queryType: 2,
  });
  /**
  * @description 获取左侧列表数据
  */
  const getSchemeCandidateList = async () => {
    setScroll(true);
    setLoading(true);
    const params = prams;
    const res = await getKgSchemeList(params);
    let result = res?.result;
    if (result) {
      result.forEach((item: any) => {
        item.roomList = item?.ruleImageList?.map((imgPath: any) => ({ imgPath }));
      })
    }
    setLoading(false);
    setScroll(false);
    if (result) {
      const data = recordList?.length > 0 && (prams.pageIndex > 1) ? [...recordList, ...result] : result;
      setRecordList(data);
    }
    else {
      setRecordList([]);
    }

    setPreparation(false);
    setTotal(res?.recordCount);
  }
  /**
  * @description 滚动加载
  */
  const scrollHandle = async (even: any) => {
    let e = even.target;
    // +5 增大触发滚动加载的范围
    if (Math.ceil(e.scrollTop) + e.clientHeight + 5 >= e.scrollHeight) {
      // 添加loading，防止在+5的访问内多次请求 
      if (recordCount > recordList?.length && !loading) {
        setPrams({
          ...prams,
          pageIndex: prams.pageIndex + 1,
        })
      };
    }
  };
  /**
  * @description 获取空间列表
  */
  const getSpaceData = async () => {
    const res = await getaiCongig();
    if (!res) return;
    const list = Object?.keys(res).map((key: any, index: number) => ({
      id: index + 1,
      screenName: key
    }));
    setSpaceList(list);
  }
  /**
  * @description 获取风格列表
  */
  const getStyleList = async () => {
    const res = await getStyles();
    if (!res) return;

    const transformedData = res?.map((item: any) => ({
      value: item.key,
      screenName: item.label
    }));
    setStyleList(transformedData);
  }

  useEffect(() => {
    getSchemeCandidateList();
  }, [prams]);


  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key.toLowerCase() === 'q') {
        setOptions(options[0] === '全案风格' ? ['风格套系', '样板间'] : ['全案风格']); /*[i18n:ignore]*/
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [options]);

  useEffect(() => {
    setAnotherAppearance(waitingToFurnishRemaining);
  }, [waitingToFurnishRemaining])



  const applySeriesToRoom = (series: TSeriesSample, applySoft: boolean, applyHard: boolean, applyCabinet: boolean) => {


    store.schemeStatusStore.layoutSchemeSaved = false;
    store.schemeStatusStore.pendingOpenSchemeIn3D = false;

    LayoutAI_App.DispatchEvent(LayoutAI_Events.SeriesSampleSelected, { series: series, scope: { soft: applySoft, hard: applyHard, cabinet: applyCabinet, remaining: false } });
    if (store.homeStore.selectData) {
      if (store.homeStore.selectData.rooms) {
        updateSeriesScopeMap(store.homeStore.selectData.rooms);
      }
    }
  }

  const applySeriesToAllRemaining = (series: TSeriesSample) => {
    store.schemeStatusStore.layoutSchemeSaved = false;
    store.schemeStatusStore.pendingOpenSchemeIn3D = false;
    LayoutAI_App.DispatchEvent(LayoutAI_Events.SeriesSampleSelected, { series: series, scope: { remaining: true } });
  }

  const updateSeriesScopeMap = (rooms: TRoom[]) => {
    let dict = seriesScopeAppliedMap;
    dict = {};
    for (let room of rooms) {
      if (room._scope_series_map) {
        for (let apply_type in room._scope_series_map) {
          let series = room._scope_series_map[apply_type as keyof typeof room._scope_series_map] as TSeriesSample;
          if (series && series.ruleId) {
            if (!dict[series.ruleId]) dict[series.ruleId] = {};
            dict[series.ruleId][apply_type as keyof typeof seriesScopeAppliedMap.value] = true;
          }
        }
      }
    }
    setSeriesScopeAppliedMap(dict);
  }

  const isCheckAppliedScope = (serialItem: TSeriesSample, type: string) => {
    return (seriesScopeAppliedMap[serialItem.ruleId] && seriesScopeAppliedMap[serialItem.ruleId][type as keyof typeof seriesScopeAppliedMap.value]);
  }


  const getApplyBtn = (type: string, item: any) => {
    if (type == '软装')      /*[i18n:ignore]*/ {
      applySeriesToRoom(item, true, false, false);
    }
    else if (type == '硬装') /*[i18n:ignore]*/ {
      applySeriesToRoom(item, false, true, false);
    }
    else if (type == '定制')  /*[i18n:ignore]*/ {
      applySeriesToRoom(item, false, false, true);
    }
  }
  const showPanel = (item: any) => {
    setPanelItem(item);

    console.log('item', item);
    MaterialService.getSeriesAllMaterial(item.ruleId, (res: any) => {
      // console.log('modelFlagsMap', modelFlagsMap['定制']); 

      setCustomizedMaterialList(res.filter((item: any) => modelFlagsMap['定制'].includes(String(item.modelFlag)))); /*[i18n:ignore]*/
      setSoftMaterialList(res.filter((item: any) => modelFlagsMap['成品'].includes(String(item.modelFlag))));       /*[i18n:ignore]*/
      setHardMaterialList(res.filter((item: any) => modelFlagsMap['贴图'].includes(String(item.modelFlag))));       /*[i18n:ignore]*/
    }, () => {
      console.log('err');
    });
    setIsShow(true);
    setActiveKey('1');
  }
  const hidePanel = () => {
    setIsShow(false);
    cleanMaterialList();
  }
  useEffect(() => {
    setPreparation(true);
    getSpaceData();
    getStyleList();
  }, [])

  const object_id = "SeriesCandidateList";
  useEffect(() => {
    LayoutAI_App.on_M(EventName.SelectingRoom, object_id, (event: { current_rooms: TRoom[], event_param: any }) => {
      updateSeriesScopeMap(event.current_rooms || []);
      setTimeout(() => {
        store.homeStore.setSelectData({ rooms: event?.current_rooms, clickOnRoom: true });
      }, 20);
    });

    return () => { LayoutAI_App.off_M(EventName.SelectingRoom, object_id); }
  }, []);

  const MaterialList = ({ list }: { list: any[] }) => {
    return (
      <div className={styles.materialList}>
        {list.map((item: any, index: number) => (
          <img
            key={index}
            width={80}
            height={80}
            src={`${item.imagePath}?x-oss-process=image/resize,m_fixed,h_80,w_80`}
            alt=""
          />
        ))}
      </div>
    );
  };

  const cleanMaterialList = () => {
    setCustomizedMaterialList([]);
    setSoftMaterialList([]);
    setHardMaterialList([]);
    setMaterialList([]);
  }

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: t('定制模型'),
      children: <MaterialList list={customizedMaterialList} />,
    },
    {
      key: '2',
      label: t('软装模型'),
      children: <MaterialList list={softMaterialList} />,
    },
    {
      key: '3',
      label: t('硬装模型'),
      children: <MaterialList list={hardMaterialList} />,
    },
  ];
  return (
    <>
      <div className={styles.bottomPanel}>
        <div className={styles.topSelect}>
          <If condition={Permissions.instance.hasPermission(PERMISSIONS.SERIES.SHOW_PLATFORM_SERIES)}>
            <Then>
                <div className={`${prams.ruleType == 1 ? 'checked' : ''}`} style={{ color: `${prams.ruleType == 1 ? '#282828' : '#959598'}` }} onClick={() => {
                  setPrams((prevParams) => ({
                  ...prevParams,
                  ruleType: 1,
                  pageIndex: 1,
                }));
              }}>{t('平台套系')}</div>
                        
              </Then>
          </If>
          <div className={`${prams.ruleType == 2 ? 'checked' : ''}`} style={{ color: `${prams.ruleType == 2 ? '#282828' : '#959598'}` }} onClick={() => {
            setPrams((prevParams) => ({
              ...prevParams,
              ruleType: 2,
              pageIndex: 1,
            }));
          }}>{t('企业套系')}</div>
        </div>
        {/* <div className={styles.roomListBar}>
          <RoomAreaBtns></RoomAreaBtns>
        </div> */}

        <div className={`${styles.container_listInfo} ${scroll ? styles.noScroll : ''} ${type ? styles.type : ''}`} ref={roomRef}>
          {
            recordList && recordList.length > 0 ?
              <>
                {recordList?.map?.((item: TSeriesSample, id: any) => (
                  <div key={"record_" + id} id={"series_box" + id} className={styles.container_box}>
                    <div key={id} className={styles.container_data}
                      onMouseEnter={() => setShowPopover((prev: any) => ({ ...prev, [id]: true }))}
                      onMouseLeave={() => setShowPopover((prev: any) => ({ ...prev, [id]: false }))}
                    >

                      <div className={`${styles.Popover_hoverInfo} ${seriesScopeAppliedMap[item.ruleId] ? styles.Popover_hoverInfo_type : ''}`}>
                        <img onClick={() => {
                          getApplyBtn(type, item);
                          applySeriesToRoom(item, true, true, true);
                          // 海尔埋点
                          SdkService.clickStyleSeries(item);
                        }} src={`${item.thumbnail}?x-oss-process=image/resize,m_fixed,h_218,w_318`} alt="" />
                        <If condition={seriesScopeAppliedMap[item.ruleId]!==undefined}>
                          <Then>
                            <div className={styles.tag_label}>
                              {t('使用全部')}
                            </div>
                          </Then>
         
                        </If>
                        {/* {!type &&
                          <>
                            {!anotherAppearance && <button onClick={() => applySeriesToRoom(item, true, true, true)} className={style.apply_all_button}>{t('全部应用')}</button>}
                            {anotherAppearance && <button onClick={() => applySeriesToAllRemaining(item)} className={style.apply_all_button}>{t('以此补全')}</button>}
                          </>
                        } */}

                      </div>
                      {/* {!anotherAppearance && !type &&
                        <div className={style.apply_category_button_container}>
                          <div onClick={() => applySeriesToRoom(item, false, false, true)} className={style.apply_category_button + " " + (isCheckAppliedScope(item, "cabinet") ? style.checked : "")}>{t('定制')}</div>
                          <div onClick={() => applySeriesToRoom(item, true, false, false)} className={style.apply_category_button + " " + (isCheckAppliedScope(item, "soft") ? style.checked : "")}>{t('软装')}</div>
                          <div onClick={() => applySeriesToRoom(item, false, true, false)} className={style.apply_category_button + " " + (isCheckAppliedScope(item, "hard") ? style.checked : "")}>{t('硬装')}</div>
                        </div>} */}

                      <div className={styles.textInfo}>
                        <div className={styles.container_title} title={item.seedSchemeName || item.ruleName}>{item.seedSchemeName || item.ruleName}</div>
                        <div className={styles.container_desc} onClick={() => showPanel(item)}>
                          {t('详情')}
                        </div>
                      </div>

                    </div>
                  </div>
                ))}
              </>
              :
              <div className={styles.emptyInfo}>
                <div>
                  <img src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
                  <div className={'desc'}>{t('暂无数据')}</div>
                </div>
              </div>
          }


        </div>
      </div>

      {/* 弹窗 */}
      <div className={styles.panelContainer}>
        {
          isShow && <PanelContainer
            center={true}
            // height={800}
            // width={560}
            className={styles.panel}
            draggable={true}
            title={panelItem?.seedSchemeName || panelItem?.ruleName}
            onClose={hidePanel}
            mask={true}
          >
            <div className={styles.panelContent}>
              <div className={styles.panelContentLeft}>
                <Carousel effect="fade" autoplay>
                  {panelItem?.roomList?.map((item: any, index: number) => (
                    <div key={index}>
                      <img className={styles.roomImg} src={`${item.imgPath}?x-oss-process=image/resize,m_fixed,h_290,w_520`} alt="" />
                    </div>
                  ))}
                </Carousel>
                <span 
                  onDoubleClick={() => {
                    const text = panelItem.kgid || panelItem.ruleId;
                    if (text) {
                      navigator.clipboard.writeText(String(text));
                      message.success('已复制套系ID到剪贴板');
                    }
                  }}>
                    套系ID：{panelItem.kgid || panelItem.ruleId}
                </span>
              </div>

              <div className={styles.panelContentRight}>
                <Tabs defaultActiveKey="1" items={items} onChange={(item: any) => {
                  setMaterialList(item == '1' ? customizedMaterialList : item == '2' ? softMaterialList : hardMaterialList);
                  setActiveKey(item);
                }} />
                <div className={styles.applyBtnInfo}>
                  <button className='leftBtn' onClick={() => {
                    if (activeKey == '1') {
                      applySeriesToRoom(panelItem, false, false, true);
                    } else if (activeKey == '2') {
                      applySeriesToRoom(panelItem, true, false, false);
                    } else if (activeKey == '3') {
                      applySeriesToRoom(panelItem, false, true, false);
                    }
                    setIsShow(false);
                    cleanMaterialList();
                  }}>
                    应用{activeKey == '1' ? t('定制') : activeKey == '2' ? t('软装') : t('硬装')}
                  </button>
                  <button className='rightBtn' onClick={() => {
                    applySeriesToRoom(panelItem, true, true, true);
                    setIsShow(false);
                    cleanMaterialList();
                  }}>{t('应用全部')}</button>
                </div>
              </div>


            </div>
          </PanelContainer>
        }

      </div>

    </>

  );
};

export default observer(SeriesList);
