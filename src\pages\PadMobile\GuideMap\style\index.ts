import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
        root: css`
            position: fixed; /* 固定在右上角 */
            width: 200px; /* 窗口宽度 */
            height: 130px; /* 窗口高度 */
            background-color: rgba(127, 127, 127, 0.2); /* 半透明背景 */
            border: 1px solid rgba(0, 0, 0, 0.05); /* 边框 */
            border-radius: 8px; /* 圆角 */
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* 阴影 */
            transition: all 0.1s ease; /* 动画效果 */
            z-index: 99; /* 设置层级 */
        `,
        blackColor: css`
            background-color: rgba(0, 0, 0, 0.40) !important;
            backdrop-filter: blur(50px) !important;
        `,
        expanded: css`
            transform: translateX(0);
        `,
        collapsed: css`
            transform: translateX(calc(100% + 64px));
        `,
        collapseButton: css`
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            width: 15px;
            height: 40px;
            background-color: transparent;
            cursor: pointer;
            
            /* 梯形背景 */
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.6);
                clip-path: polygon(0 10%, 100% 0, 100% 100%, 0 90%);
                border-radius: 12px 0 0 12px;
                backdrop-filter: blur(4px);
                transition: background-color 0.3s ease;
            }
            
            /* 实心三角形图标 */
            &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 6px 0 6px 8px;  /* 朝左的三角形 */
                border-color: transparent transparent transparent rgba(102, 102, 102, 0.8);
                transform: translate(-25%, -50%);
                transition: all 0.3s ease;
            }
        `,
    }
});