
import { TGroupTemplate } from "../../../TGroupTemplate/TGroupTemplate";
import { FigureZValRangeType, I_Entity3D } from "../../../../IRoomInterface";
import { ZPolygon, compareNames } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { I_OnLineFigureInterval } from "../../TLayoutOptimizer/TLayoutOptimizer";
import { WPolygon } from "../../../../TFeatureShape/WPolygon";
import {  TGraphBasicConfigs } from "../../../TGraphBasicConfigs";
import { I_BackWallLayoutRule, KitchenAreaType } from "../../../TGraphConfigureInterface";

import { TSimpleRoomPartionGraph } from "../TSimpleRoomPartitionGraph";
import { TLayoutOptmizerOnWallRules } from "../../TLayoutOptimizer/TLayoutOptimizerOnWallRules";
import { range_substract } from "@layoutai/z_polygon";
import { TSimpleRoomPartRelation } from "./TSimpleRoomPartRelation";
import { LayoutAI_Configs } from "@/Apps/LayoutAI/Layout/TLayoutEntities/configures/LayoutAIConfigs";

interface I_MainRectSubEdges {
    /**
     *  主矩形的边id
     */
    main_rect_edge_id: number;

    /**
     *  主矩形上的边
     */
    r_edge: ZEdge;

    /**
     *   WPoly上的最长边
     */
    t_edge: ZEdge;
    /**
     *   裁剪后 剩下的边长
     */
    sub_edges: ZEdge[];
    /**
     *  裁剪后 的剩余长度
     */
    sum_length: number;
    /**
     *  是否有窗
     */
    has_window: boolean
};

export class TKitchenPartRelation extends TSimpleRoomPartRelation {

    constructor() {
        super();
    }

    get graph() {
        return this._graph as any as TSimpleRoomPartionGraph;
    }

    updateGroupLengthLevels(rule: I_BackWallLayoutRule) {
        if (rule.group_length_levels) return;
        let f: { [key: number]: number } = {};
        let g: { [key: number]: number } = { 0: 0 };


        for (let ele of rule.group_array) {
            let cate = ele.group_space_category;
            let config = TGraphBasicConfigs.MainGroupFigureConfigs["厨房"][cate];
            if (!config) continue;

            if (config.zval_range_type === FigureZValRangeType.OnHalfWall) continue;
            let group_levels = config.group_length_levels;

            if (!group_levels.values) {
                let min = group_levels.min || 0;
                let max = group_levels.max || 0;
                let step = group_levels.step || 100;

                group_levels.values = [];
                for (let val = min; val < max - 1; val += step) {
                    group_levels.values.push(val);
                }

                group_levels.values.push(group_levels.max);

            }

            let values = group_levels.values;

            let pri_count = 0;
            if ((config.priority || 0) <= 2) {
                // values.push(0);
            }
            else {
                pri_count = 1;
            }

            f = {};
            for (let key in g) {
                let key_val = ~~key;
                for (let val of values) {
                    f[val + key_val] = g[key] + pri_count;
                }
            }
            g = {};
            for (let key in f) {
                g[key] = f[key];
            }

        }

        rule.group_length_levels = {
            values: []
        }
        for (let key in f) {
            let key_val = ~~key;
            if (key_val > 0 && f[key] > 0) {
                rule.group_length_levels.values.push(key_val);
            }
        }

    }

    updateStructureRects() {

        let structures: I_Entity3D[] = [];
        this._room.pipes && structures.push(...this._room.pipes);
        this._room.flues && structures.push(...this._room.flues);
        this._room.pillars && structures.push(...this._room.pillars);

        let rects: ZRect[] = [];
        let poly = this._room.room_shape._poly;

        for (let structure of structures) {
            let s_rect = structure.rect;
            if (!poly.containsPoint(s_rect.rect_center, 10)) {
                continue;
            }
            let rect = s_rect.clone();

            let flue_length = 600;
            let flue_depth = 450;
            if (rect._w < rect._h) {
                rect.swapWidthAndHeight();
            }
            let target_edge: ZEdge = null;
            for (let edge of poly.edges) {
                if (Math.abs(edge.nor.dot(rect.nor)) < 0.5) {
                    continue;
                }
                let pp = edge.projectEdge2d(rect.rect_center);
                if (pp.x < 0 || pp.x > edge.length) continue;

                if (Math.abs(pp.y) < rect.h + 50) {
                    target_edge = edge;
                }
            }
            if (target_edge) {
                let pp = target_edge.projectEdge2d(rect.rect_center);
                rect._w = flue_length;
                rect._h = flue_depth;
                rect.nor = target_edge.nor.clone().negate();
                if (pp.x < target_edge.length / 2) {
                    let tx = flue_length / 2;
                    let t_pos = target_edge.unprojectEdge2d({ x: tx, y: 0 });

                    rect.back_center = t_pos;
                }
                else {
                    let tx = flue_length / 2;
                    let t_pos = target_edge.unprojectEdge2d({ x: target_edge.length - tx, y: 0 });

                    rect.back_center = t_pos;
                }

                rects.push(rect);
            }


        }

        return rects;

    }

    initGroupIntervalsInArea(area_name: KitchenAreaType) {
        let config = TGraphBasicConfigs.KitchenLayoutRules[area_name];

        let floor_cabinet_intervals: I_OnLineFigureInterval[] = [];
        let hangon_cabinet_intervals: I_OnLineFigureInterval[] = [];
        for (let data of config.initial_rules[0].group_array) {
            let name = data.group_space_category;
            let interval = TLayoutOptmizerOnWallRules.make_interval_of_group_category(name, "厨房");

            if (data.zero_differ_weight) {
                interval.zero_differ_weight = data.zero_differ_weight;
            }

            if (name.indexOf("冰箱") >= 0) {
                floor_cabinet_intervals.push(interval);
                hangon_cabinet_intervals.push(interval);

            }
            else if (name.indexOf("吊柜") >= 0) {
                hangon_cabinet_intervals.push(interval);
            }
            else {
                floor_cabinet_intervals.push(interval);
            }
        }
        return {
            floor_cabinet_intervals: floor_cabinet_intervals,
            hangon_cabinet_intervals: hangon_cabinet_intervals
        }
    }
    computeIntervalsOfArea(area_name: KitchenAreaType, target_length: number) {
        let config = TGraphBasicConfigs.KitchenLayoutRules[area_name];

        let floor_cabinet_intervals: I_OnLineFigureInterval[] = [];
        let hangon_cabinet_intervals: I_OnLineFigureInterval[] = [];
        for (let data of config.initial_rules[0].group_array) {
            let name = data.group_space_category;
            let interval = TLayoutOptmizerOnWallRules.make_interval_of_group_category(name, "厨房");

            if (data.zero_differ_weight) {
                interval.zero_differ_weight = data.zero_differ_weight;
            }
            if (name.indexOf("吊柜") >= 0) {
                hangon_cabinet_intervals.push(interval);
            }
            else {
                floor_cabinet_intervals.push(interval);
            }
        }

        floor_cabinet_intervals.length > 0 && TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(floor_cabinet_intervals, target_length, true, 10);
        hangon_cabinet_intervals.length > 0 && TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(hangon_cabinet_intervals, target_length, true, 10);





        return [...floor_cabinet_intervals, ...hangon_cabinet_intervals];

    }
    getIntervalsOfArea(area_name: KitchenAreaType) {
        let config = TGraphBasicConfigs.KitchenLayoutRules[area_name];
        let values = config.initial_rules[0].group_length_levels.values;
        let interval: I_OnLineFigureInterval = {
            name: area_name,
            length_values: values,
            target_length: values[values.length - 1],
            result_length: values[values.length - 1],
            center_x: 0,
            target_center_x: 0,
            length_differ_weight: 0.5,
            center_diff_weight: 0,  // 中心差值可以不用考虑
            zero_differ_weight: 10000, // 必须要有这个区

        }
        return interval;
    }
    precompute(): void {
        /**
         *  0. 先用默认的相似样板间匹配的手段
         */
        super.precompute();

        const KitchenAreas: KitchenAreaType[] = ["白电区", "水槽区", "主操作区", "烹饪区", "盲柜区", "补板区"]

        for (let key in TGraphBasicConfigs.KitchenLayoutRules) {
            let config = TGraphBasicConfigs.KitchenLayoutRules[key];
            if (config.initial_rules[0]) {
                this.updateGroupLengthLevels(config.initial_rules[0]);
            }
        }

        let feature_shape = this._room.room_shape._feature_shape;

        let main_rect = this._room.max_R_shape.getRect();

        /**
         *  如果包含最大L形, 则使用最大L形最大包围盒矩形
         */
        if (this._room.max_L_shape) {
            let bbox = this._room.max_L_shape._poly.computeBBox();

            let rect = ZRect.fromBox3(bbox, main_rect.nor);
            rect.u_dv = main_rect.dv;
            rect.updateRect();
            main_rect = rect;

        }
        let w_poly = feature_shape._w_poly;

        let length = main_rect.w;
        let depth = main_rect.h;
        let pre_length = w_poly.edges[w_poly.edges.length - 1].length;



        let t_edge_list: I_MainRectSubEdges[] = [];

        // 厨房的顺序, 一般0是最长边
        for (let i = 0; i <= 3; i++) {
            let ii = i;
            if (ii == 3) ii = -1;
            let t_edge = WPolygon.getTargetEdgeAlignOnMainRect(w_poly, main_rect, ii, this._room.roomname);
            t_edge_list.push({
                main_rect_edge_id: ii,
                r_edge: main_rect.edges[(ii + main_rect.edges.length - 1) % main_rect.edges.length],
                t_edge: t_edge,
                sub_edges: [],
                sum_length: 0,
                has_window: WPolygon.getWindowOnEdge(t_edge) !== null
            })
        }

        let structure_rects = this.updateStructureRects();

        let sub_rects: ZRect[] = [];
        if (structure_rects) sub_rects.push(...structure_rects);
        for (let data of t_edge_list) {
            let t_edge = data.t_edge;
            if (!t_edge) continue;
            let unvalid_pairs: number[][] = [];

            for (let structure_rect of sub_rects) {
                for (let r_edge of structure_rect.edges) {
                    if (t_edge && t_edge.islayOn(r_edge, 50, 0.1)) {
                        let px0 = t_edge.projectEdge2d(r_edge.v0.pos).x;
                        let px1 = t_edge.projectEdge2d(r_edge.v1.pos).x;

                        if (px0 > px1) {
                            let tmp = px0; px0 = px1; px1 = tmp;
                        }
                        unvalid_pairs.push([px0, px1])
                    }
                }
            }
            let valid_pairs = range_substract(t_edge.length, unvalid_pairs);
            data.sub_edges = [];
            for (let pair of valid_pairs) {
                if (pair[1] > pair[0] + 10) {
                    let p0 = t_edge.unprojectEdge2d({ x: pair[0], y: 0 });
                    let p1 = t_edge.unprojectEdge2d({ x: pair[1], y: 0 });

                    let edge = new ZEdge({ pos: p0 }, { pos: p1 });
                    edge._nor.copy(t_edge.nor);
                    data.sub_edges.push(edge);
                }
            }
        }

        for (let data of t_edge_list) {
            data.sum_length = 0;
            for (let edge of data.sub_edges) {
                data.sum_length += edge.length;
            }
            data.sum_length = Math.round(data.sum_length);
        }


        let strategies: {
            name: string;
            
            min_length?:number;
            min_depth?:number;
            init_rules: {
                area_array: KitchenAreaType[],
                wall_id?: number,
                floor_margin_end?: number,  // 末尾留白
                floor_margin_start?: number,  // 开头留白
                hangon_margin_start?: number,
                hangon_margin_end?: number,
                u_dv_flag ?: number;
            }[]
        }[] = [
                {
                    name: "U型-2-0",
                    min_depth : 1400,
                    min_length : 2200,
                    init_rules: [
                        {
                            area_array: ["补板区", "盲柜区", "水槽区", "主操作区", "补板区"],
                            wall_id: 0,
                            floor_margin_end: 560,
                            hangon_margin_end: 560,
                        },
                        {
                            area_array: ["补板区", "盲柜区", "烹饪区", "补板区"],
                            wall_id: 1,

                        },
                        {
                            area_array: ["白电区"],
                            wall_id: 3,
                            floor_margin_start: 5

                        },
                    ]
                },
                {
                    name: "U型-2-1",
                    min_depth : 1400,
                    min_length : 2200,
                    init_rules: [
                        {
                            area_array: ["补板区", "主操作区", "水槽区", "盲柜区", "补板区"],
                            wall_id: 0,
                            floor_margin_start: 560,
                            hangon_margin_start: 560,
                            u_dv_flag : 1
                        },
                        {
                            area_array: ["补板区", "烹饪区", "盲柜区", "补板区"],
                            wall_id: 3,
                            u_dv_flag : -1


                        },
                        {
                            area_array: ["白电区"],
                            wall_id: 1,
                            floor_margin_start: 620,
                            hangon_margin_start: 620
                        },
                    ]
                },
                {
                    name: "浅U型-1",
                    min_depth : 1400,
                    min_length : 2200,
                    init_rules: [
                        {
                            area_array: ["白电区", "补板区", "烹饪区", "补板区"],
                            wall_id: 0,
                            floor_margin_end: 560,
                            hangon_margin_end: 560
                        },
                        {
                            area_array: ["补板区", "盲柜区", "主操作区", "补板区"],
                            wall_id: 1,
                            floor_margin_start: 450,
                            hangon_margin_start: 450,
                            floor_margin_end: 560,
                            hangon_margin_end: 0
                        },
                        {
                            area_array: ["补板区", "盲柜区", "水槽区", "补板区"],
                            wall_id: 2,
                            floor_margin_end: 1000,
                            hangon_margin_end: 1000,
                            hangon_margin_start: 400,
                            floor_margin_start: 300
                        },
                    ]
                },
                {
                    name: "L型-1",
                    init_rules: [
                        {
                            area_array: ["白电区", "补板区", "烹饪区", "补板区"],
                            wall_id: 0,
                            floor_margin_end: 560,
                            hangon_margin_end: 300
                        },
                        {
                            area_array: ["补板区", "盲柜区", "水槽区", "补板区"],
                            wall_id: 1,
                            floor_margin_start: 300,
                            hangon_margin_start: 300
                        }
                    ]
                },
                {
                    name: "L型-2",
                    init_rules: [
                        {
                            area_array: ["补板区", "水槽区", "盲柜区", "补板区"],
                            wall_id: 0,
                            floor_margin_end: 300,
                            hangon_margin_end: 300
                        },
                        {
                            area_array: ["白电区", "补板区", "烹饪区", "补板区"],
                            wall_id: 1,
                            floor_margin_start: 560,
                            hangon_margin_start: 560
                        },
        
                    ]
                },
                {
                    name: "I型-1",
                    init_rules: [
                        {
                            area_array: ["补板区", "烹饪区", "主操作区","水槽区", "补板区"],
                            wall_id: 0,
                        }
        
                    ]
                },

            ];

        for (let strategy of strategies) {
            let strategy_name = strategy.name;

            if(strategy.min_depth)
            {
                if(main_rect.depth < strategy.min_depth - 1) continue;
            }
            if(strategy.min_length)
            {
                if(main_rect.length < strategy.min_length - 1) continue;
            }
            let group_templates: TGroupTemplate[] = [];
            let floor_templates : TGroupTemplate[] = [];
            let is_valid = true;
            for (let id in strategy.init_rules) {
                let init_rule = strategy.init_rules[id];
                let wall_id = init_rule.wall_id !== undefined ? init_rule.wall_id : ~~id;

                let floor_margin_end = init_rule.floor_margin_end || 0;
                let floor_margin_start = init_rule.floor_margin_start || 0;

                let hangon_margin_start = init_rule.hangon_margin_start || 0;
                let hangon_margin_end = init_rule.hangon_margin_end || 0;
                let l_cabinet_u_dv = init_rule.u_dv_flag || 1;
                let edge_data = t_edge_list[wall_id];
                if (!edge_data) continue;

                let s_edge = edge_data.sub_edges[0];

                if (!s_edge) continue;

                let t_edge = edge_data.r_edge;
                let ll = t_edge.projectEdge2d(s_edge.v0.pos).x;
                let rr = t_edge.projectEdge2d(s_edge.v1.pos).x;


                let floor_ll = Math.max(ll, floor_margin_start);
                let floor_rr = Math.min(rr, t_edge.length - floor_margin_end);
                let hangon_ll = Math.max(ll, hangon_margin_start);
                let hangon_rr = Math.min(rr, t_edge.length - hangon_margin_end);
                let t_floor_length = Math.round(floor_rr - floor_ll);
                let t_hangon_length = Math.round(hangon_rr - hangon_ll);

                if (t_hangon_length < 300 || t_floor_length < 600) {
                    // is_valid = false;
                    continue;
                }
                // console.log(strategy_name, Math.round(t_floor_length),floor_ll,hangon_ll);
                let area_intervals: I_OnLineFigureInterval[] = [];

                for (let area_name of init_rule.area_array) {
                    area_intervals.push(this.getIntervalsOfArea(area_name));
                }

                // let error = TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(area_intervals, t_length, true, 10);

                // console.log(area_intervals,error,t_length);

                let floor_cabinet_intervals: I_OnLineFigureInterval[] = [];
                let hangon_cabinet_intervals: I_OnLineFigureInterval[] = [];

                for (let area_name of init_rule.area_array) {
                    let name = area_name;

                    let sub_intervals = this.initGroupIntervalsInArea(name as KitchenAreaType);

                    floor_cabinet_intervals.push(...sub_intervals.floor_cabinet_intervals);
                    if (!edge_data.has_window) {
                        hangon_cabinet_intervals.push(...sub_intervals.hangon_cabinet_intervals);
                    }
                }

                let error0 = TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(floor_cabinet_intervals, t_floor_length, true, 10);


                let cooker_center_x: number = -1;
                for (let interval of floor_cabinet_intervals) {
                    if (interval.name.indexOf("冰箱") >= 0) {
                        interval.target_length = interval.result_length;
                        interval.target_center_x = floor_ll + interval.center_x - hangon_ll;

                        interval.length_differ_weight = 100000;
                        interval.center_diff_weight = 100000;
                        interval.zero_differ_weight = 100000;
                    }
                    if (interval.name.indexOf("炉灶地柜") >= 0) {
                        cooker_center_x = interval.center_x + floor_ll - hangon_ll;
                    }
                }


                for (let interval of hangon_cabinet_intervals) {
                    if (interval.name.indexOf("烟机吊柜") >= 0) {
                        interval.target_center_x = cooker_center_x;
                        interval.center_diff_weight = 10;
                    }
                }

                let error1 = TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(hangon_cabinet_intervals, t_hangon_length, true, 10);
                // console.log(hangon_cabinet_intervals,t_hangon_length);

                for (let res_interval of floor_cabinet_intervals) {
                    if (res_interval.result_length < 0.1) continue;


                    let target_rect = new ZRect(res_interval.result_length, 560);
                    let center_x = res_interval.center_x + floor_ll;
                    if (res_interval.name.indexOf("冰箱") >= 0) {
                        target_rect._h = 900;

                        center_x = center_x - hangon_ll + floor_ll;
                    }


                    let pos = t_edge.unprojectEdge2d({ x: center_x, y: 0 });
                    target_rect.back_center = pos;
                    target_rect.nor = t_edge.nor.clone().negate();
                    target_rect.u_dv = s_edge.dv.clone().multiplyScalar(l_cabinet_u_dv);
                    target_rect.updateRect();
                    let group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(res_interval.name, "厨房", target_rect);
                    group_templates.push(group_template);
                    floor_templates.push(group_template);
                }

                for (let res_interval of hangon_cabinet_intervals) {

                    if (res_interval.result_length < 0.1) continue;
                    if (res_interval.name.indexOf("冰箱") >= 0) {
                        continue;
                    }
                    let center_x = res_interval.center_x + hangon_ll;

                    let pos = t_edge.unprojectEdge2d({ x: center_x, y: 0 });

                    let target_rect = new ZRect(res_interval.result_length, 375);

                    target_rect.back_center = pos;
                    target_rect.nor = t_edge.nor.clone().negate();
                    // target_rect.u_dv = s_edge.dv;
                    target_rect.updateRect();

                    let group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(res_interval.name, "厨房", target_rect);
                    group_templates.push(group_template);
                }
            }

            if (!is_valid) continue;

            this.post_process_on_structures(main_rect,w_poly,group_templates);
            this._candidate_figure_list.push({
                group_templates: group_templates, debug_data: { rule: strategy, scheme_name: strategy.name }
            });



        }
        this._attempt_num = this._candidate_figure_list.length;





    }


    post_process_on_structures(main_rect:ZRect, w_poly:ZPolygon, group_templates:TGroupTemplate[])
    {
        let removed_board_templates : TGroupTemplate[] = [];
        for(let edge of w_poly.edges)
        {
            let win = WPolygon.getWindowOnEdge(edge);
            if(win) {
                continue;
            }

            // 主要是找到那些由烟道、包管产生的边
            // 所以一般不会超过600
            if(edge.length >600) continue;

            let check_infront_cabinet = false;
            for(let template of group_templates)
            {
                if(template.group_space_category.indexOf("吊柜")>=0) continue;

                let target_rect = template._target_rect;

                for(let t_edge of target_rect.edges)
                {
                    if(t_edge.islayOn(edge,600,0.5))
                    {
                        check_infront_cabinet = true;
                        break;
                    }
                }
                if(check_infront_cabinet) break;
            }
            // 如果前方有柜子, 那么就不用考虑补一块板
            if(check_infront_cabinet) continue;

            // 要去找到旁边最近的那个柜子
            let side_cabinet_template : TGroupTemplate = null;
            let side_min_dist = 300; // 距离小于30

            for(let template of group_templates)
            {
                if(template.group_space_category.indexOf("吊柜")>=0) continue;
                let target_rect = template._target_rect;
                if((target_rect.nor.dot(edge.nor))>-0.9) continue; // 法向相反

                let dist0 = Math.abs(target_rect.project(edge.v0.pos).x) - target_rect.w/2;
                let dist1 = Math.abs(target_rect.project(edge.v1.pos).x) - target_rect.w/2;
                dist0 = Math.min(Math.abs(dist0),Math.abs(dist1));

                if(dist0 < side_min_dist)
                {
                    side_cabinet_template = template;
                    side_min_dist = dist0;
                }
            }

            if(!side_cabinet_template) continue;

            // 补一个板件, 让它的前端齐平

            let front_center = side_cabinet_template._target_rect.front_center;
            let target_board_length = edge.length;
            let target_board_depth = -edge.projectEdge2d(front_center).y; // 负值向内

            if(target_board_depth < 100)
            {
                continue;
            }
            let back_center = edge.center;
            if(side_cabinet_template.group_space_category.indexOf("地柜收口板")>=0) // 如果是地柜收口板
            {
                let ll = 0;
                let rr = edge.length;

                let xx = edge.projectEdge2d(front_center).x;
                if(xx < 0) xx -= side_cabinet_template._target_rect.w/2;
                if(xx > 0) xx += side_cabinet_template._target_rect.w/2;

                if(xx < ll) ll = xx;
                if(xx > rr) rr = xx;

                target_board_length = rr - ll;
                back_center = edge.unprojectEdge2d({x:(ll+rr)/2,y:0});

                removed_board_templates.push(side_cabinet_template);
            }

            let target_rect = new ZRect(target_board_length,target_board_depth);
            target_rect.back_center = back_center;
            target_rect.nor = edge.nor.clone().negate();
            target_rect.updateRect();

       

            let group_template :TGroupTemplate = null;
            if(target_rect.w > 100)
            {
                 group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory("假门地柜", "厨房", target_rect);

            }
            else{
                 group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory("地柜收口板", "厨房", target_rect);
            }

            if(group_template)
            {
                group_templates.push(group_template);
            }




        }

        for(let template of removed_board_templates)
        {
            let id = group_templates.indexOf(template);
            if(id >= 0)
            {
                group_templates.splice(id,1);
            }
        }
    }

    /**
     *  后处理橱柜的通用 深度、高度、离地高
     */
    post_process_kitchen_cupboard_size()
    {
        this._candidate_figure_list.forEach((candidate)=>{
            let group_templates = candidate.group_templates;
            if(group_templates && group_templates.length > 0)
            {
                group_templates.forEach((group_template)=>{
                    if(group_template.current_s_group)
                    {
                        let figure_elements = group_template.current_s_group.figure_elements;
                        if(figure_elements)
                        {
                            figure_elements.forEach((ele)=>{
                                if(compareNames([ele.category],["地柜"]))
                                {
                                    ele.depth = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.floorCupboardDepth || ele.depth;
                                    ele.rect.zval = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.floorCupboardZVal || ele.rect.zval;
                                    ele.height = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.floorCupboardHeight || ele.height;
                                    ele.rect.updateRect();
                                }
                                if(compareNames([ele.category],["吊柜"]))
                                {
                                    ele.depth = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.wallCupboardDepth || ele.depth;
                                    ele.rect.zval = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.wallCupboardZVal || ele.rect.zval;
                                    ele.height = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.wallCupboardHeight || ele.height;
                                    ele.rect.updateRect();
                                }
                                if(compareNames([ele.category],["高柜"]))
                                {
                                    ele.depth = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.highCupboardDepth || ele.depth;
                                    ele.rect.zval = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.highCupboardZVal || ele.rect.zval;
                                    ele.height = LayoutAI_Configs.Configs.kitchen_layout_size_configs?.highCupboardHeight || ele.height;
                                    ele.rect.updateRect();
                                }
                            });
                        }
                    }
                });
            }
        })
    }
}
