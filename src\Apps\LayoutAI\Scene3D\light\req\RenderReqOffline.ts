import { OrthographicCamera, PerspectiveCamera, Camera } from "three";
import * as zlib from "zlib";

import { ServerRenderService } from "@/Apps/LayoutAI/Services/ServerRender/ServerRenderService";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { getUserInfo } from "@/services/user";
import { getCookie } from "@/utils";
import { createAxdRequest, createOpenRequest, getImgDomain } from "@svg/request";

import packageJson from "../../../../../../package.json";
import { FovUtils } from "./FovUtils";
import { ORenderConfig } from "./ORenderConfig";
import { EnvLightConfig, LightManager } from "../../LightManager";
import { RenderFlag } from "@/Apps/LayoutAI/Services/ServerRender/OfflineRenderType";
import { _magiccubeToken } from "@/config";
import { LightMode } from "@/pages/SdkFrame/MsgCenter/IMsgType";
import { LightingRuler } from "@/Apps/LayoutAI/Services/AutoLighting/AutoLightingRuler";


type JsonConsumerRecordForAPI = {
    TypeCode: string;
    RecordCurrency: number;
    ConsumerCate: number;
    UserId: string;
    DeptId: string | null;
    OrganId: string | null;
    UserType: number;
    RecordContent: string | null;
    Remark: string | null;
    IsReady: boolean;
    IsDeleted: boolean;
};

type RawRoyScene = {
    schemeId: string;
    renderFile: string;
    renderFileType: number;
    width: number;
    height: number;
    flag: number;
    queueType: number;
    isHasReplace: number;
    templateVersion: string;
    defaultName: string;
    upgradequeueid: string;
    resolution: string;
    belongType: number;
    jsonConsumerRecordForAPI: JsonConsumerRecordForAPI;
    countInfo: string;
    remark: string;
    roomId: string;
    isDelayed: number;
    clientVersion: string;
    nodes: any;
    queueId: string | null;
    title: string | null;
    couponId: string | null;
    grantRecordId: string | null;
    depthImageUrl: string | null;
    isRenderSeed: boolean;
    taskType: number;
    scale: string;
    resolutionTag: string;
    lightJson: any;
    coupons: any[];
    lightSchemeId: string;
    qualityId: number;
    authCode: string;
};

type AtlasData = {
    userId: string; // 用户ID
    flag: string; // 任务类型
    schemeId: string; // 方案ID
    pageIndex: number;
    pageSize: number;
    all: string; // 标志
    isRtx: number; // RTX 标识
    resolutionTag: string; // 分辨率标签
    type: number; // 标识类型
    exclusiveRealAdjustLight: boolean; // 排除实时调光数据
    authCode: string;
};

export type CouponData = {
    couponId: string;
    couponName?: string;
    grantRecordId: string;
    surplusUseCount: number;
    totalCount?: number;
    useCouponScenario?: number;
    useCouponType?: number;
    validBeginDate?: string;
    validEndDate?: string;
};

export type LightTemplate =  {
    lightMode: LightMode;
    autoLight: LightingRuler[];
    envLight: {
        ambient: { intensity: number, color: number };
        sunlight: { intensity: number, color: number };
        subSunLight: { intensity: number, color: number };
    };
};

/**
* @description 渲染请求
* <AUTHOR>
* @date 2025-01-04
* @lastEditTime 2025-01-04 14:05:04
* @lastEditors xuld
*/
export class RenderReqOffline {

    private static _instance: RenderReqOffline;

    public static get instance(): RenderReqOffline {
        if (!this._instance) {
            this._instance = new RenderReqOffline();
        }
        return this._instance;
    }

    // 后端限制32个字符，形如：ai_layout_1.0.79
    public static getClientVersion(): string {
        let fullVersion = packageJson.version;
        let version = fullVersion.split("-")[0];
        let prefix = "ai_layout_";
        return prefix + version;
        // return fullVersion;
    }

    public atlasQueueId: string = "";

    private rawRoyScene: RawRoyScene = {
        schemeId: "", //方案ID
        renderFile: "", // 使用 Base64 编码后的数据
        renderFileType: 0,
        width: 1920, //分辨率
        height: 1080, //分辨率
        flag: 0, //渲染类型 0:普通渲染图，1全景 2鸟瞰图 3神笔视频 4 自定义材质 5 照明模拟
        queueType: 0, //参加活动标识，0：普通扣费，没有参加限时免费活动 1：正常扣费 2：参加限时免费渲染活动
        isHasReplace: 1, //是否有替换
        templateVersion: "2", //模版版本
        defaultName: "",
        upgradequeueid: "",
        resolution: "",
        belongType: 2, //归属类型
        jsonConsumerRecordForAPI: { //消费信息
            TypeCode: "3DRender_L",
            RecordCurrency: 0,
            ConsumerCate: 0,
            UserId: "",
            DeptId: null,
            OrganId: null,
            UserType: 0,
            RecordContent: null,
            Remark: null,
            IsReady: true,
            IsDeleted: false
        },
        countInfo: "",
        remark: "remark",
        roomId: "10", //空间ID
        isDelayed: 0, // 是否闲时渲染1：是 0：否
        clientVersion: RenderReqOffline.getClientVersion(),
        nodes: null,
        queueId: null,
        title: null,
        couponId: null,
        grantRecordId: null,
        depthImageUrl: null,
        isRenderSeed: false,
        taskType: 0, //调光任务类型 2 实时调光 3 实时调光输入
        scale: "16:9", //缩放比例
        resolutionTag: "HD", //分辨率标签, SD 标清, HD 高清, FHD 超清,2K 2K,4K 4K,8K 8K
        lightJson: null,
        coupons: [], //渲染券列表
        lightSchemeId: "", //灯光方案id
        qualityId: 4, //渲染模版质量id 1、极速 2、标准V1.0 3、标准V1.1 4、标准V1.2 5、影视级
        authCode: ""
    };

    private atlasData: AtlasData = {
        userId: "", //用户ID
        flag: "normal_all", //任务类型 normal_all：全部普通效果图，normal_standard：标清，normal_high：高清，normal_hyper：超清，normal_4K：4K，panorama_all：全景，overlook_all：俯视，overlook_standard：俯视标清，overlook_high：俯视高清
        schemeId: "", //方案ID
        pageIndex: 1,
        pageSize: 72,
        all: "2", //标志 1：三个月前全部数据，其他：最近三个月数据
        isRtx: 0, //RTX 标识 0 否 1 是
        resolutionTag: "", //分辨率标签
        type: null, //标识类型 0 效果图 1 全景 2 鸟瞰图
        exclusiveRealAdjustLight: true, //排除实时调光数据，true = 1
        authCode: ""
    }

    private couponData: CouponData = {
        couponId: "",
        grantRecordId: "",
        surplusUseCount: 0,
    }

    // 套系关联的灯光模板数据
    public lightTemplateData: LightTemplate = null;
    public setLightTemplateData(data: LightTemplate) {
        this.lightTemplateData = data;
    }

    /**
     * @description 发送渲染请求
     * @param roySceneSchemeId 方案ID
     * @param roySceneId 场景对象ID
     * @param roySceneVersion 版本
     * @returns 
     */
    public async send(roySceneSchemeId: string, roySceneId: string, roySceneVersion: number, camera?: Camera, renderNum?: number): Promise<{ success: boolean, msg: string, reqRes: any }> {
        console.log("请求渲染", ServerRenderService.getRenderFlag() === RenderFlag.Panorama ? "全景" : "普通", "分辨率: ", ServerRenderService.getResolutionConfig().resolutionTag);

        let rsXml = this.getRoySceneXml(roySceneSchemeId, roySceneId, roySceneVersion, camera);
        console.log('rsXml', rsXml.toString());
        let rsBase64Str = this.getRoySceneBase64Str(rsXml);
        let rsData = await this.getRoySceneData(roySceneSchemeId, rsBase64Str);
        // 批量渲染的渲染券余额判断
        if (renderNum && this.couponData && renderNum > this.couponData.surplusUseCount) {
            return { success: false, msg: "批量渲染失败，渲染券余额不足", reqRes: this.couponData };
        }
        let reqRes = await this.requestRender(rsData);
        if (!reqRes.success) {
            return { success: false, msg: reqRes.msg, reqRes: null };
        }

        return { success: true, msg: "提交渲染任务成功", reqRes: reqRes.reqRes };
    }

    public async sendAtlas(schemeId: string, pageIndex?: number, pageSize?: number): Promise<{ success: boolean, msg: string, queueList?: any }> {
        let atlasData = await this.getAtlasData(schemeId, pageIndex, pageSize);
        let resList = await this.requestAtlas(atlasData);
        if (!resList) {
            return { success: false, msg: resList.msg }
        }

        return { success: true, msg: '请求图册成功', queueList: resList.res };
    }


    /**
     * @description 获取RoyScene的XML
     * 云设计数据参考
     * <RenderView 
            matrix3D="1,0,0,0,0,6.123233995736766e-17,1,0,0,-1,6.123233995736766e-17,0,2877.721895939396,-5429.075292156736,1400,1" 
            renderHeight="450" 
            renderWidth="800" 
            sceneMode="day" 
            lightSceneType="normal" 
            isPanorama="0" 
            near="100" 
            far="300000" 
            fov="1.5184364492350666" 
            rotationZ="0" 
            sceneName="dayScene" 
            sceneIntensity="2" 
            sceneRotationZ="0" 
            sceneId="SDLR6" 
            qualityId="4" 
            src_Height="959" 
            src_Width="1858" 
            region="327,148,1591,859" 
            brightness="正午/中" 
            lightdome_intensity="4" 
            lightdome_color="16776441" 
            isAdvanceLight="1" 
            auto_exposure="0" 
            exposure_compensation="0" 
            specify_focus="0" 
            focus_distance="3000" 
            aperture="30"/>
     * 其中 regin = [viewX, viewY, viewX + viewWidth, viewY + viewHeight]
     * viewX: 327
     * viewY: 148
     * viewWidth: 1264
     * viewHeight: 148
     * @param schemeId 方案ID
     * @param roySceneId 场景对象ID
     * @param roySceneVersion 版本
     * @returns 
     */
    private getRoySceneXml(schemeId: string, roySceneId: string, roySceneVersion: number, camera?: Camera): string {
        const scene3D = LayoutAI_App.instance.scene3D;
        camera = camera || scene3D.camera;
        let fovDegree = 0, near = 0, far = 0, aspect = 1;

        if (camera instanceof OrthographicCamera) {
            near = camera.near;
            far = camera.far;
        } else if (camera instanceof PerspectiveCamera) {
            near = camera.near;
            far = camera.far;
            fovDegree = camera.fov;
            aspect = camera.aspect;
        } else {
            console.error("相机类型错误", camera);
        }

        // three.js 的 fov 是垂直视场角，需要转换为水平视场角
        let fovDegreeHor = FovUtils.verticalToHorizontalFOV(fovDegree, aspect);
        let fovHor = fovDegreeHor / 180 * Math.PI;

        const doc = document.implementation.createDocument("", "", null);
        const domain = window.location.hostname;

        const root = doc.createElement("Root");
        doc.appendChild(root);

        const copyright = doc.createElement("Copyright");
        copyright.setAttribute("version", new Date().getFullYear().toString());
        copyright.setAttribute("version3d", RenderReqOffline.getClientVersion());
        copyright.setAttribute("type", "Scheme");
        copyright.setAttribute("schemeId", schemeId);
        root.appendChild(copyright);

        const client = doc.createElement("Client");
        // 固定3，后端会把这个自动转为整型
        client.setAttribute("version", "3");
        client.setAttribute("domain", domain);
        client.setAttribute("isbeta", "0");
        client.setAttribute("qualityType", "0");
        client.setAttribute("model", "0");
        client.setAttribute("units", "mm");
        root.appendChild(client);

        const lighttemplate = doc.createElement("Lighttemplate");
        lighttemplate.setAttribute("id", "1");
        root.appendChild(lighttemplate);

        const enhanceQuatily = doc.createElement("EnhanceQuatily");
        enhanceQuatily.setAttribute("isequatily", "2");
        root.appendChild(enhanceQuatily);

        let config = ORenderConfig.getRenderConfig(scene3D.isNightMode());
        let sceneIntensity = config.sceneIntensity;
        let sceneMode = config.sceneMode;
        let sceneId = config.sceneId;

        let ambientLight = scene3D.isNightMode() ?
            LightManager.getNightAmbientLight() :
            LightManager.getDayAmbientLight();

        // 要求前端不响应，但渲染生效
        let lightdome_intensity = this.lightTemplateData ? this.lightTemplateData.envLight.ambient.intensity : ambientLight.intensity;
        if (!scene3D.isNightMode()) {
            lightdome_intensity /= EnvLightConfig.DayAmbientIntensity;
        }
        let lightdome_color = this.lightTemplateData ? this.lightTemplateData.envLight.ambient.color : ambientLight.color.getHex();

        const renderView = doc.createElement("RenderView");
        if (ServerRenderService.getRenderFlag() === RenderFlag.Panorama) {
            let resolutionConfig = ServerRenderService.getResolutionConfig();
            renderView.setAttribute("isPanorama", "1");// 是否全景
            renderView.setAttribute("renderWidth", resolutionConfig.width.toString());// 出图分辨率 - 宽
            renderView.setAttribute("renderHeight", resolutionConfig.height.toString());// 出图分辨率 - 高

            // 全景图无 src_Width、src_Height、region
        } else {
            renderView.setAttribute("isPanorama", "0");// 是否全景
            let renderSize = ServerRenderService.getImageSize();
            renderView.setAttribute("renderWidth", renderSize.width.toString());// 出图分辨率 - 宽
            renderView.setAttribute("renderHeight", renderSize.height.toString());// 出图分辨率 - 高

            renderView.setAttribute("src_Width", scene3D.viewWidth.toString());// 画布大小-宽
            renderView.setAttribute("src_Height", scene3D.viewHeight.toString());// 画布大小-高
            renderView.setAttribute("region", ServerRenderService.getRenderRegion().join(','));// 裁剪区域
        }

        renderView.setAttribute("matrix3D", camera.matrix.elements.join(',')); // 相机矩阵
        renderView.setAttribute("sceneMode", sceneMode);// 外景模式，"night"，"day"
        renderView.setAttribute("lightSceneType", "normal");
        renderView.setAttribute("near", near.toString());// 近裁剪面
        renderView.setAttribute('far', far.toString());// 远裁剪面
        renderView.setAttribute("fov", fovHor.toString());// 视场角
        renderView.setAttribute("rotationZ", fovDegreeHor.toString());// 旋转Z轴
        renderView.setAttribute("sceneName", "dayScene");// 外景编号
        renderView.setAttribute("sceneIntensity", sceneIntensity.toString());
        renderView.setAttribute("sceneRotationZ", "0");
        renderView.setAttribute("sceneId", sceneId);
        renderView.setAttribute("qualityId", "4");// 4 标准 5 影视
        renderView.setAttribute("brightness", "正午/中");
        renderView.setAttribute("lightdome_intensity", lightdome_intensity.toString());// 户外亮度
        renderView.setAttribute("lightdome_color", lightdome_color.toString());// 户外颜色
        renderView.setAttribute("isAdvanceLight", "1");
        // 自动曝光
        renderView.setAttribute("auto_exposure", "0");
        renderView.setAttribute("exposure_compensation", "0");

        renderView.setAttribute("specify_focus", "0");
        renderView.setAttribute("focus_distance", "3000");
        renderView.setAttribute("aperture", "30");
        root.appendChild(renderView);

        const sendMessage = doc.createElement("SendMessage");
        sendMessage.setAttribute("isSend", "false");
        root.appendChild(sendMessage);

        const planFormPicture = doc.createElement("PlanFormPicture");
        planFormPicture.setAttribute("state", "0");
        root.appendChild(planFormPicture);

        const adjustGamma = doc.createElement("AdjustGamma");
        adjustGamma.setAttribute("isAdjust", "1");
        adjustGamma.setAttribute("isAdjust30", "1");
        adjustGamma.setAttribute("colorBleed", "0");
        root.appendChild(adjustGamma);

        const colorCorrection = doc.createElement("ColorCorrection");
        root.appendChild(colorCorrection);

        const royScene = doc.createElement("RoyScene");
        const storey = doc.createElement("Storey");
        storey.setAttribute("id", roySceneId);
        storey.setAttribute("version", roySceneVersion.toString());
        storey.setAttribute("schemeId", schemeId);
        storey.setAttribute("high", "0");
        storey.setAttribute("matrix", "1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1");
        royScene.appendChild(storey);
        root.appendChild(royScene);

        // 将修改后的 XML 转回字符串
        const serializer = new XMLSerializer();
        return serializer.serializeToString(doc);
    }

    private getRoySceneBase64Str(rsXml: string): string {
        const compressed = zlib.deflateSync(rsXml);
        const rsBase64Str = Buffer.from(compressed).toString('base64');
        return rsBase64Str;
    }

    private async getRoySceneData(schemeID: string, rsBase64Str: string): Promise<RawRoyScene> {
        this.rawRoyScene.authCode = getCookie("authCode");

        // 设置方案 ID 和 RoyScene
        this.rawRoyScene.schemeId = schemeID;
        this.rawRoyScene.renderFile = "{Base64}" + rsBase64Str;

        let ratio = ServerRenderService.ratio;
        this.rawRoyScene.scale = `${ratio.w}:${ratio.h}`;

        // 设置渲染类型
        this.rawRoyScene.flag = ServerRenderService.getRenderFlag();

        // 设置用户信息
        const userInfo = await getUserInfo();
        this.rawRoyScene.jsonConsumerRecordForAPI.UserId = userInfo?.userId;

        let resolutionType;
        let renderType: number;
        if (this.rawRoyScene.flag === RenderFlag.Panorama) { // 全景图
            this.rawRoyScene.isDelayed = 1;// 全景为0
            this.rawRoyScene.belongType = 10; // 10 全景
            renderType = 1;

            // 设置分辨率
            let resolutionConfig = ServerRenderService.getResolutionConfig();
            resolutionType = resolutionConfig.resolutionType;

            this.rawRoyScene.width = resolutionConfig.width;
            this.rawRoyScene.height = resolutionConfig.height;

            // 付费类型
            this.rawRoyScene.resolutionTag = null;
            this.rawRoyScene.jsonConsumerRecordForAPI.TypeCode = resolutionConfig.typeCode;

            // 灯光方案
            this.rawRoyScene.lightSchemeId = "LS202506260000000000000045010488";
        } else {
            //
            this.rawRoyScene.isDelayed = 0; // 普通为0
            this.rawRoyScene.belongType = 2; // 2 普通
            renderType = 0;

            // 设置分辨率
            let resolutionConfig = ServerRenderService.getResolutionConfig();
            resolutionType = resolutionConfig.resolutionType;

            this.rawRoyScene.width = resolutionConfig.width;
            this.rawRoyScene.height = resolutionConfig.height;

            // 付费类型
            this.rawRoyScene.jsonConsumerRecordForAPI.TypeCode = resolutionConfig.typeCode;
            this.rawRoyScene.resolutionTag = resolutionConfig.resolutionTag;

            // 灯光方案
            this.rawRoyScene.lightSchemeId = "";
        }

        let coupon = await this.getCoupon(renderType, resolutionType);
        this.couponData = coupon;
        if (coupon) {
            this.rawRoyScene.couponId = coupon.couponId;
            this.rawRoyScene.grantRecordId = coupon.grantRecordId;
        } else {
            this.rawRoyScene.couponId = null;
            this.rawRoyScene.grantRecordId = null;
        }
        return this.rawRoyScene;
    }

    private getAxdRequest(): any {
        if (_magiccubeToken) {
            let req = createAxdRequest({
                headers: {
                    "Joint-Token": _magiccubeToken,
                    "Magiccube-Token": _magiccubeToken
                }
            });
            return req;
        } else {
            return createAxdRequest();
        }
    }

    private async requestRender(rsData: RawRoyScene): Promise<any> {
        console.log("rsData", rsData);
        const cmd = "Queues/PaymentAndCreateQueue";
        let args = {
            method: 'post',
            url: cmd,
            timeout: 5000,
            data: rsData,
        };
        const axdRequest = this.getAxdRequest()
        let res = await axdRequest(args).catch((e: any): Promise<{ success: boolean; msg: string; reqRes: null }> => {
            console.error(e);
            return Promise.resolve({ success: false, msg: "提交渲染任务失败", reqRes: null });
        });
        console.log("requestRender", res);
        let obj: {
            cost: number;
            errorCode: string;
            njRenderId: string;
            result: string;
        } = JSON.parse(res.JSON);
        
        // 1|QO202508210000000000000934427210|消费:0|权限判断：32|写入redis：1|队列创建：44|
        // 0|亲，您已有12张效果图正在渲染，请稍后再提交！
        let objRes = obj.result.split("|");
        if (obj.errorCode) {
            return { success: false, msg: objRes[1], reqRes: null };
        } else {
            let isSuccess = objRes[0] === "1";
            if (isSuccess) {
                return { success: true, msg: "提交渲染任务成功", reqRes: res };
            } else {
                return { success: false, msg: objRes[1], reqRes: null };
            }
        }
    }

    private async getAtlasData(schemeId: string, pageIndex?: number, pageSize?: number): Promise<AtlasData> {
        await getUserInfo().then((userInfo) => {
            this.atlasData.userId = userInfo?.userId;
        });
        this.atlasData.authCode = getCookie("authCode");
        this.atlasData.schemeId = schemeId;
        this.atlasData.pageIndex = pageIndex || 1;
        this.atlasData.pageSize = pageSize || 72;
        this.atlasData.flag = ServerRenderService.getRenderFlag() === RenderFlag.Panorama ? "panorama_all" : "normal_all";
        return this.atlasData;
    }

    public async requestAtlas(atlasData: AtlasData): Promise<any> {      //请求图册的接口
        const cmd = "Queues/GetQueuesListByUserId";
        let args = {
            method: 'post',
            url: cmd,
            timeout: 500,
            data: atlasData,
        };
        const axdRequest = this.getAxdRequest()
        const queueList = await axdRequest(args).catch((e: any): Promise<any> => {
            console.log(e);
            return Promise.resolve({ success: false, msg: e.message });
        });
        return { success: true, msg: '请求图册成功', res: queueList };
    }

    async delTargetAtlas(queueId: string): Promise<any> {
        const cmd = "QueueOK/DeleteQueueOK";
        let args = {
            method: 'post',
            url: cmd,
            timeout: 500,
            data: { authCode: getCookie("authCode"), queueId: queueId },
        };
        const axdRequest = this.getAxdRequest();
        let res = await axdRequest(args).catch((e: any): void | null => {
            console.error(e);
        });
        console.log("deleteQueue", res);
        return res;
    }

    public static getQueueId(data: any): string {
        if (data && data.result.length > 0 && data.result.split("|")[0] === "1") {
            return data.result.split("|")[1];
        }
        return "";
    }

    /**
     * @description 获取图册列表中指定 queueId 的图册
     * @param atlasList 图册列表
     * @param queueId 队列ID
     * @returns 
     */
    public getTargetAtlas(atlasList: Array<any>, queueId: string): any {
        let targetAtlas = atlasList.find((atlas: any) => atlas.QueueId === queueId);
        if (targetAtlas) {
            return targetAtlas;
        }
        return null;
    }

    /**
     * @description 获取图册的url, 每隔30s请求一次，直到queueId对应的图册生成,但不能超过timeout
     * @param schemeId 方案ID
     * @param queueId 队列ID
     * @param timeout 超时时间
     * @returns 
     */
    public async getTargetAtlasUrl(schemeId: string, queueId: string, timeout: number = 120000): Promise<string> {
        const start = Date.now();
        console.log("queueId", queueId);

        return new Promise<string>((resolve, reject) => {
            const pollAtlas = async (): Promise<void> => {
                try {
                    // if (Date.now() - start > timeout) {
                    //     reject(new Error("渲染超时"));
                    //     return;
                    // }

                    const res = await this.sendAtlas(schemeId);
                    if (!res.success) {
                        reject(new Error("请求图册失败"));
                        return;
                    }

                    const atlasList = res.queueList?.data?.ReturnList || [];
                    if (atlasList.length === 0) {
                        console.log("排队中，请稍后...", res);
                    }

                    const target = this.getTargetAtlas(atlasList, queueId);
                    console.log("atlas", res);
                    if (target && target.Status) {
                        console.log("target", target);
                        console.log("target Status", target.Status);
                        if (target.Status == 0) {
                            console.log("排队中...");
                        }
                        else if (target.Status == 1) {
                            console.log("渲染中...");
                        }
                        else if (target.Status == 2) {
                            console.log("渲染失败...");
                        } else if (target.Status == 3) {
                            console.log("渲染成功...");
                        } else {
                            console.warn("未知状态...");
                        }
                    }

                    if (target && target.QueueId && target.FileIdOutPut2) {
                        const renderURL = `${getImgDomain()}/${target.FileIdOutPut2}`
                        resolve(renderURL);
                        return;
                    }
                } catch (error) {
                    reject(error);
                    return;
                }

                setTimeout(pollAtlas, 20000);
            };

            pollAtlas();
        });
    }

    // 获取可用券张数
    private async getCoupon(renderType: number, resolutionType: string): Promise<{ couponId: string, grantRecordId: string, surplusUseCount: number}> {
        // 标清不使用券，直接返回空字符串
        if (!resolutionType) {
            return null;
        }

        let data = {
            "couponType": 0,
            "pageIndex": 1,
            "pageSize": 10,
            "renderType": renderType,
            "resolutionType": resolutionType,
            // "timeType": 0
        }

        // true 用远鹏提供的接口，false 用3D云设计的接口
        let isCustom = true;

        let cmd = "api/couponcenterapi/couponGrantRecord/listMyCouponTypeByPage";
        if (isCustom) {
            cmd = 'api/njvr/layoutRenderCoupon/getCoupon';
        }
        let args = {
            method: 'post',
            url: cmd,
            timeout: 5000,
            data: data,
        };
        let req = createOpenRequest();
        req.defaults.headers['syscode'] = RenderReqOffline.getClientVersion();
        let res = await req(args).catch((e: any) => {
            console.error(e);
        });

        console.log("listMyCouponTypeByPage", res);

        if (!res?.success) {
            return null
        }

        let result = res.result;
        if (isCustom) {
            console.log("use coupon", result);
            return result;
        }
        else {
            let items = result.result;
            if (items.length > 0) {
                let item = items[0];
                console.log("use coupon", item);
                return item;
            }
        }

        return null;
    }
}

// 放入全局对象，方便调试
(globalThis as any).RenderReqOffline = RenderReqOffline;