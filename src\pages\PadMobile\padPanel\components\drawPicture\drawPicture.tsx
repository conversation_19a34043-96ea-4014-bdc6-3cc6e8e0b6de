import React from "react";
import { observer } from 'mobx-react-lite';
import useStyles from './style/index';
import { useRef, useEffect, useState } from "react";
import { useStore } from '@/models';
/**
 * 渲染出图模式下，截图区域的显示
 * @returns 
 */

// 截图中间区域占比
const SCREENSHOT_RATIO = 0.83;

const DrawPicture:React.FC = () => {
  const { styles } = useStyles();
  const store = useStore();
  const contentBoxRef = useRef<HTMLDivElement>(null);
  const [isPortrait, setIsPortrait] = useState(window.innerWidth < window.innerHeight); // 是否竖屏状态
  const [border, setBorder] = useState({
    borderWidth: '0px',     // 边框宽度
    borderHeight: '0px',    // 边框高度
    contentWidth: '100%',   // 内容区宽度
    contentHeight: '100%'   // 内容区高度
  });

  useEffect(() => {
    const handleResize = () => {
      const newIsPortrait = window.innerWidth < window.innerHeight;
      setIsPortrait(newIsPortrait);
      handleBorderWidth(store.homeStore.aspectRatioMode, newIsPortrait);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    handleBorderWidth(store.homeStore.aspectRatioMode, isPortrait);
  }, [store.homeStore.aspectRatioMode, isPortrait]);

  // 计算边框和内容区尺寸
  const handleBorderWidth = (radio: number, portraitMode = isPortrait) => {
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    const targetRatio = [
      { width: 4, height: 3 },    // 4:3
      { width: 16, height: 9 },   // 16:9
      { width: 3, height: 4 },    // 3:4
      { width: 9, height: 16 },   // 9:16
      { width: 0, height: 0 }     // 1:1
    ][radio - 1];
    let contentWidth, contentHeight;
    
    if(radio == 5)
    {
      contentWidth = screenWidth;
      contentHeight = screenHeight;
      setBorder({
        borderWidth: `${0}px`,
        borderHeight: `${0}px`,
        contentWidth: `${contentWidth}px`,
        contentHeight: `${contentHeight}px`
      });
      return;
    }

    if (portraitMode) {

      // 竖屏模式 - 以宽度为基准计算
      contentWidth = screenWidth * SCREENSHOT_RATIO;
      contentHeight = contentWidth * targetRatio.height / targetRatio.width;
      
      // 如果计算的高度超过屏幕高度，则以高度为基准重新计算
      if (contentHeight > screenHeight * SCREENSHOT_RATIO) {
        contentHeight = screenHeight * SCREENSHOT_RATIO;
        contentWidth = contentHeight * targetRatio.width / targetRatio.height;
      }
    } else {
      // 横屏模式 - 以高度为基准计算
      contentHeight = screenHeight * SCREENSHOT_RATIO;
      contentWidth = contentHeight * targetRatio.width / targetRatio.height;
      
      // 如果计算的宽度超过屏幕宽度，则以宽度为基准重新计算
      if (contentWidth > screenWidth * SCREENSHOT_RATIO) {
        contentWidth = screenWidth * SCREENSHOT_RATIO;
        contentHeight = contentWidth * targetRatio.height / targetRatio.width;
      }
    }
    // 更新状态
    setBorder({
      borderWidth: `${(screenWidth - contentWidth) / 2}px`,
      borderHeight: `${(screenHeight - contentHeight) / 2}px`,
      contentWidth: `${contentWidth}px`,
      contentHeight: `${contentHeight}px`
    });
  };

  // 应用边框和内容区样式
  useEffect(() => {
    if (contentBoxRef.current) {
      const contentBox = contentBoxRef.current;
      contentBox.style.borderLeftWidth = border.borderWidth;
      contentBox.style.borderRightWidth = border.borderWidth;
      contentBox.style.borderTopWidth = border.borderHeight;
      contentBox.style.borderBottomWidth = border.borderHeight;
      
      // 更新内容区尺寸
      const contentElement = contentBox.querySelector(`.${styles.contentBox}`) as HTMLElement;
      if (contentElement) {
        contentElement.style.width = border.contentWidth;
        contentElement.style.height = border.contentHeight;
      }
    }
  }, [border, styles.contentBox]);

  return (
    <div className={styles.box}  ref={contentBoxRef}>
      <div className={styles.contentBox}></div>
    </div>
  )
}

export default observer(DrawPicture);