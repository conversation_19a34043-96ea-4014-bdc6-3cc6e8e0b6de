import { ZRect } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomClassfierCheckRule } from "./TLivingRoomClassfierCheckRule";
import { Vector3 } from "three";

export class TSofaGroupSpaceAreaLenAndDepthCheckRule extends TLivingRoomClassfierCheckRule
{
    private minDepth: number = 1500;

    private maxLenOrDepth: number = 5000;
    
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    protected setParamConfig(): void {
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) {
        void(room);
        void(figure_element);
        let sofaFigure: TFigureElement = main_figures.find(figure => figure.sub_category.includes("沙发") && !figure.sub_category.includes("背景墙"));
        if(!sofaFigure)
        {
            return {score: 0};
        }
        let otherSofaGroupNames: string[] = ["脚踏", "茶几", "休闲椅", "边几", "落地灯"];
        let sofaGroupFigures: TFigureElement[] = [sofaFigure];
        for(let figure of main_figures)
        {
            if(isContainFigureNames(sofaFigure,figure, otherSofaGroupNames))
            {
                sofaGroupFigures.push(figure);
            }
            if(figure == sofaFigure)
            {
                continue;
            }
            if(sofaFigure._group_cate == figure._group_cate)
            {
                sofaGroupFigures.push(figure);
            }
        }
        let sofaGroupRange: any = TBaseRoomToolUtil.instance.getBoxRangByFigurs(sofaGroupFigures);
        if(sofaGroupRange)
        {
            let sofaGroupRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(sofaGroupRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(sofaGroupRange));
            if(Math.abs(sofaGroupRect.nor.dot(sofaFigure.rect.nor)) < 0.9)
            {
                let oldCentre: Vector3 = sofaGroupRect.rect_center.clone();
                let oldLen: number = sofaGroupRect.length;
                let oldWidth: number = sofaGroupRect.depth;
                sofaGroupRect.length = oldWidth;
                sofaGroupRect.depth = oldLen;
                sofaGroupRect.rect_center = oldCentre;
            }
            sofaGroupRect.nor = sofaFigure.rect.nor.clone();
            sofaGroupRect.updateRect();
            if(sofaGroupRect.max_hh > this.maxLenOrDepth || sofaGroupRect.depth < this.minDepth)
            {
                return {score: -30};
            }
        }
        return {score: 0};
    }
}

function isContainFigureNames(sourceFigure: TFigureElement, figure: TFigureElement, figureNames: string[]): boolean {
    let isGroup: boolean = false;
    for(let figureName of figureNames)
    {
        if(figure.sub_category.includes(figureName))
        {
            isGroup = true;
            break;
        }
    }
    if(isGroup)
    {
        let dist: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(sourceFigure.rect, figure.rect);
        if(dist > 2000)
        {
            return false;
        }
        let backEdgeNor: Vector3 = sourceFigure.rect.backEdge.nor.clone();
        let backEdgeCenter: Vector3 = sourceFigure.rect.backEdge.center.clone();
        let subVec: Vector3 = figure.rect.rect_center.clone().sub(backEdgeCenter).normalize();
        if(subVec.dot(backEdgeNor) > 0.9)
        {
            return false;
        }
    }
    return isGroup;
}