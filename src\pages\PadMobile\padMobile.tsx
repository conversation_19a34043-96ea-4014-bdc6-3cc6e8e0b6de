import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { Button, Modal, message } from "@svg/antd";
import { useEffect, useState, useRef } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { AI2DesignBasicModes, AI2DesignManager } from "@/Apps/AI2Design/AI2DesignManager";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import Scene3DDiv from "@/components/Scene3DDiv/scene3DDiv";
import MobileNavigation, { NavigationEvent, PageStates } from "./navigation/navigation";
import { _createHxId, _editHxId, cabinetReplaceUrl, checkIsMobile, is_debugmode_website, is_dreamer_mini_App, mini_APP, mode_type, scheme_Id, workDomainMap } from "@/config";
import { HomeProgress } from '@/components';
import HouseSearchPopup from './houseSearchPopup/houseSearchPopup';
import MyLayoutSchemeList from "@/components/MyLayoutSchemeList/MyLayoutSchemeList";
import SaveLayoutSchemeDialog from "@/components/SaveLayoutSchemeDialog/SaveLayoutSchemeDialog";
import DreamerPopup from "../Home/DreamerPopup/dreamerPopup";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample";
import { LayoutSchemeService } from "@/Apps/LayoutAI/Services/Basic/LayoutSchemeService";
import { loadFile } from "@/IndexDB";
import { getHouseScheme, getReplacementData } from "@/services/home/<USER>";
import CustomKeyboard from "@/components/CustomKeyBoard/customKeyBoard";
import RoomAreaBtns from "./roomAreaBtns/roomAreaBtns";
import MultiSchemeList from "./multiScheme/multiSchemeList/multiSchemeList";
import DreamerHxSearch from '@/components/HxSearch/dreamerHxSearch';
import RoomImagePredict from '@/components/RoomImagePredict/roomImagePredict';
import MobileHistoricalAtlas from "../AiDraw/components/MobileHistoricalAtlas";
import PadPanels from "./padPanel/padPanels";
import { Scene3DEvents } from "@/Apps/LayoutAI/Scene3D/Scene3DEvents";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { SdkService } from "@/services/SdkService";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import EnterPage from "./EnterPage/enterPage";
import { If, Then } from "react-if";
import CabinetCompute from "./CabinetCompute/cabinetCompute";
import { GuidanceChannel } from "@svg/antd-cloud-design";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { Mesh, Vector3 } from "three";
import PadHousePanel from "./padHousePanel/PadHousePanels";
import StartDesignPage from "./StartDesignPage/startDesignPage";
import { IFrameMsgCenter } from "../SdkFrame/MsgCenter/IFrameMsgCenter";
import { IFrameMsgServer } from "../SdkFrame/MsgCenter/IFrameMsgServer";
import { IFrameMsgClientExFuncs } from "../SdkFrame/MsgCenter/IFrameMsgClientExFuncs";
import { IFrameMsgCommnads } from "../SdkFrame/MsgCenter/IFrameMsgCommands";
import { IFrameMsgServerExFuncs } from "../SdkFrame/MsgCenter/IFrameMsgServerExFuncs";
import { IFrameMsgTestScripts } from "../SdkFrame/MsgCenter/IFrameMsgTestScripts";
import { getMyCaseById } from "@/services/home/<USER>";
import { LayoutAI_Configs } from "@/Apps/LayoutAI/Layout/TLayoutEntities/configures/LayoutAIConfigs";
import { Scene3DManager } from "@/Apps/LayoutAI/Scene3D/Scene3DManager";
import { FigureViewControls } from "@/Apps/LayoutAI/Scene3D/controls/FigureViewControls";
import { ZRect } from "@layoutai/z_polygon";
import Progress from "@/components/Progress/Progress";
import { getPollingService } from "@/services/RenderPollingService";
import { SvgGltfCabinetNode } from "@layoutai/model3d_api";
import { Model3dApi } from "@/Apps/LayoutAI/Api/Model3dApi";
import { MeshBuilder } from "@/Apps/LayoutAI/Scene3D/MeshBuilder";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";

/**
 * @description 主界面
 */
const PadMobile: React.FC = () => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [messageApi, contextHolder] = message.useMessage();

  return (
    <div className={styles.root} >
      12312
      {contextHolder}
    </div>
  );
};

PadMobile.displayName = 'PadMobile';

export default observer(PadMobile);
