import React, { useEffect, useRef, useState } from 'react';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { observer } from "mobx-react-lite";
import { useStore } from '@/models';
import { useTranslation } from 'react-i18next';
import { EventName } from '@/Apps/EventSystem';
import { TFigureElement } from '@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement';
import IconFont from '@/components/IconFont/iconFont';
import { Button, Segmented, Spin } from '@svg/antd';
import { I_MaterialMatchingItem } from '@/Apps/LayoutAI/Layout/IMaterialInterface';
import { getDesignMaterialByIdsWithOutPlaceheights, getReplaceListByCategoryIdApi } from '@/components/NewReplaceProduct/services/material';
import { imgHostUrl } from '@/config';
import { getMaterialTopViewImage } from '@/services/material';
import Aicabinet from '@/components/Aicabinet';
import { magiccubeDpAiWebRequest } from '@/utils';
import { TFurnitureEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity';
import { generateUUID } from 'three/src/math/MathUtils.js';
import { PERMISSIONS } from '@/config/permissions';
import { Permissions } from '@/Apps/LayoutAI/setting/Permissions';
import { If,Then } from 'react-if';
import defaultImg from '@/assets/images/material_replace_default.png'

const Replace: React.FC<{selectedFigureElement: TFigureElement}> = ({selectedFigureElement}) => {
  const store = useStore();
  const { t } = useTranslation();
  const { styles } = useStyles();
  const [materialList, setMaterialList] = useState<I_MaterialMatchingItem[]>(selectedFigureElement?._candidate_materials);
  const [currentTab, setCurrenTab] = useState<string>('套系素材');/*[i18n:ignore]*/
  const [loading, setLoading] = useState<boolean>(false); // 添加 loading 状态
  const aicabinetRef = useRef(null);

  console.log('selectedFigureElement', selectedFigureElement)

  const [kgId_cache, set_kgId_cache] = useState<string | number>('')
  const handleFetchMaterials = async (sfe: TFigureElement) => {
    if (!sfe) {
      // console.error("无效的图元实例");
      return;
    }
  
    if (sfe._candidate_materials) {
      setMaterialList(sfe._candidate_materials);
      return;
    }

    // 缓存 kgId
    sfe._room?._series_sample_info?.seriesKgId
      ? set_kgId_cache(sfe._room._series_sample_info.seriesKgId)
      : null
  
    try {
      setCurrenTab('套系素材'); /*[i18n:ignore]*/
      setMaterialList([]);
      setLoading(true);
  
      // 提取 kgId 和房间信息
      let seriesKgId = sfe._room?._series_sample_info?.seriesKgId || kgId_cache;
      const isDoor = sfe.modelLoc.includes('门');
  
      if (!seriesKgId) {
        if (isDoor && kgId_cache) {
          console.warn("门的图元实例中缺少 kgId, 使用缓存的 kgId 替代");
        } else {
          // throw new Error("图元实例的 kgId 缺失");
          seriesKgId = "";
        }
      }
    } catch (err) {
      console.error("处理素材请求时出错: ", err);
      setMaterialList([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleFetchMaterials(selectedFigureElement);
  }, [selectedFigureElement]);

  const getMaterialLists = async(currentTab: string) => {
    if(currentTab === '套系素材') /*[i18n:ignore]*/
    {
      if(selectedFigureElement?._candidate_materials && selectedFigureElement?._candidate_materials.length > 0)
      {
        setMaterialList(selectedFigureElement?._candidate_materials);
      } else {
        setMaterialList([]);
      }
      
    } else 
    {
      setLoading(true);
      const res = await getReplaceListByCategoryIdApi({
        categoryId: "",
        current: 1,
        designMaterialId: selectedFigureElement?._matched_material?.modelId,
        size: 50,
        tagIds: []
      })
      if(res.success && res.data)
      {
        const updatedMaterialList = res.data.materials.records.map((item: any) => ({
          imageUrl: imgHostUrl + item.imagePath + '?x-oss-process=image/resize,m_fixed,w_120,h_120',
          name: item.materialName,
          materialId: item.materialId,
        }));
      
        // 更新状态或执行其他操作
        setMaterialList(updatedMaterialList);
      } else {
        setMaterialList([]);
      }
      setLoading(false);
    }
  }

  const enterCabinetReplace = () => {
    console.log(selectedFigureElement);
    
    store.homeStore.setShowCabinetReplace(
    { show: true, 
      materialId: selectedFigureElement._matched_material.modelId, 
      length: selectedFigureElement.matched_rect._w, 
      width: selectedFigureElement.matched_rect._h, 
      height: selectedFigureElement._matched_material.targetSize.height
    });
    console.log('layout布局的svgCabinet', selectedFigureElement._solid_mesh3D);
    
  }

  useEffect(() => {
    getMaterialLists(currentTab)
  },[currentTab])

  return (
    <div className={styles.root}>
      {selectedFigureElement &&
        <>
          {selectedFigureElement && (
                <div className={styles.topInfo}>
                    <div className='info'>
                    <div>
                        <img src={selectedFigureElement._matched_material.imageUrl || selectedFigureElement.image_path || defaultImg} alt="" />
                    </div>
                    <div className='sizeInfo'>
                        <div>{selectedFigureElement._matched_material.name}</div>
                        <div className='size'>{t('图元尺寸')}：{Math.round(selectedFigureElement.rect._w)}*{Math.round(selectedFigureElement.rect._h)}</div>
                        <div className='size'>{t('模型尺寸')}：{Math.round(selectedFigureElement._matched_material.length)}*{Math.round(selectedFigureElement._matched_material.width)}*{Math.round(selectedFigureElement._matched_material.height)}</div>
                        {selectedFigureElement._matched_material.modelId && <div className='size'>{t('素材ID')}：{selectedFigureElement._matched_material.modelId}</div>}
                    </div>
                    </div>
                    {/* <IconFont style={{ fontSize: 20, color: '#5B5E60' }} type={selectedFigureElement?.locked ? "icon-suoding1":"icon-jiesuo1"} onClick={(ev) => {
                        if(selectedFigureElement._room && selectedFigureElement._room?.locked) return;
                        if(selectedFigureElement)
                        {
                        selectedFigureElement.locked = !selectedFigureElement.locked;
                            LayoutAI_App.emit(EventName.RoomMaterialsUpdated,true);
                            LayoutAI_App.instance.update();
                            ev.stopPropagation();
                        }
                    }}></IconFont> */}
                </div>
            )}
          <div className={styles.divider}>
            <div>{t('可用素材')}</div>

            <div>
            <If condition={store.userStore.showCabinetReplace}>
              <Then>
                <Button type="primary" size='small'  onClick={() => {
                  enterCabinetReplace();
                }}>{t('样式换搭')} </Button>
              </Then>

            </If>

            {
              ['衣柜', '玄关柜', '餐边柜'].some(category => selectedFigureElement?.sub_category?.includes(category)) && store.userStore.showAiCabinet &&
              <Button style={{ marginLeft: 10 }} type="primary" size='small' onClick={() =>{
                aicabinetRef.current.onModal();
              }}>
                {t('AI搭柜 ')}
              </Button>
            }</div>
          </div>

         

          <Segmented block value={currentTab} options={Permissions.instance.hasPermission(PERMISSIONS.SERIES.CLOUD_MATERIALS) ? [t('套系素材'), t('云素材')] : [t('套系素材')] } onChange={(item: any) => {
            setCurrenTab(item);
          }} />
          <div className={styles.goodsInfo}>
          {loading ? ( // 使用 Spin 组件作为加载指示器
              <div className={styles.loading}>
                <Spin size="large" /> {/* 大号加载指示器 */}
              </div>
            ) : materialList && materialList.length > 0 ? (
              materialList.map((item: I_MaterialMatchingItem, index: number) => {
                // console.log(index, item)
                return (
                  <div
                    className={styles.goodsItem}
                    key={index}
                    onClick={async () => {
                      if (selectedFigureElement.locked) return;
                      store.designStore.setSelectedIndex(index);

                      if(currentTab === '套系素材') /*[i18n:ignore]*/
                      {
                        LayoutAI_App.DispatchEvent(LayoutAI_Events.ReplaceMaterial, item);
                      } else {
                        let designMaterialInfo = null;
                        const res = await getDesignMaterialByIdsWithOutPlaceheights({
                          materialIds: item?.materialId
                        });
                        if(res?.result?.result?.[0])
                        {
                          designMaterialInfo = res?.result?.result?.[0];
                        }
                        const topViewImage: string = await getMaterialTopViewImage(item.materialId);
                        if(designMaterialInfo)
                        {
                          const mm: I_MaterialMatchingItem = {
                            modelId: designMaterialInfo.MaterialId,
                            imageUrl: item.imageUrl.startsWith('https://') 
                            ? item.imageUrl 
                            : imgHostUrl + item.imageUrl,
                            name: designMaterialInfo.MaterialName,
                            originalLength: designMaterialInfo.PICLength,
                            originalWidth: designMaterialInfo.PICWidth,
                            originalHeight: designMaterialInfo.PICHeight,
                            length: designMaterialInfo.PICLength,
                            width: designMaterialInfo.PICWidth,
                            height: designMaterialInfo.PICHeight,
                            modelLoc: selectedFigureElement.modelLoc,
                            modelFlag: designMaterialInfo.ModelFlag.toString(),
                            topViewImage: topViewImage,
                            figureElement: selectedFigureElement
                          } as I_MaterialMatchingItem;
                          let replaceItem = {...item, ...mm};
                          LayoutAI_App.DispatchEvent(LayoutAI_Events.ReplaceMaterial, replaceItem);
                        }

                      }
                    }}
                  >
                    {index === store.designStore.selectedIndex && (
                      <div className={styles.selectIcon}></div>
                    )}
                    <img 
                      src={item.imageUrl} 
                      className={selectedFigureElement?.params?.materialId === (item.modelId || item.materialId) ? styles.selected : null}
                    />

                    <div className={styles.sizeInfo}>{item.name}</div>
                    {item?.length ? (
                      <div className={styles.sizeInfo} style={{ color: '#959598' }}>
                        {Math.round(item?.length) + '*' + Math.round(item?.width) + '*' + Math.round(item?.height)}
                      </div>
                    ) : null}
                  </div>
                );
              })
            ) : (
              <div className={styles.noData}>
                <img className='emptyImg' src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
                <span>{t('暂无可用素材')}</span>
              </div>
            )}
          </div>
          <Aicabinet onParams={() =>{}} selectedFigureElement={selectedFigureElement} ref={aicabinetRef} />
        </>
      }

    </div>
  );
};


export default observer(Replace);
