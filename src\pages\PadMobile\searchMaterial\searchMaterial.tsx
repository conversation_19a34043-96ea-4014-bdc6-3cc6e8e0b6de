import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import useStyles from "./style/index";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { useStore } from "@/models";
import IconFont from "@/components/IconFont/iconFont";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { getPrefix } from "@/utils/common";
import { g_FigureImagePaths } from "@/Apps/LayoutAI/Drawing/FigureImagePaths";
import SeriesList from "../seriesList/seriesList";
import { message } from "@svg/antd";
import Replace from "../Replace/replace";
import { EventName } from "@/Apps/EventSystem";
/**
 * @description 查看素材
 */

interface RoomSeriesPlanProps {
    menuList: menuListProps[];
    multiExpand ?: boolean;
    showLayoutList: boolean;
  }
interface menuListProps {
    label: string;
    figureList: FigureItem[];
    init_expanded ?: boolean;
}
interface figure {
  img: string;
  title: string;
  category: string;
}
interface Module {
    image: string;
    title: string;
    label: string;
}
interface FigureItem{
  image_path:string,room:TRoom,
  isFloor?:boolean,label:string,
  title:string,title2:string,title3:string,
  img:string,img1:string,img2:string,
  centerTitle:string,bottomTitle:string,checked?:boolean, area:string, isLocked?:boolean,
  figure_element?:TFigureElement, imgTitle?:string};


const SearchMaterial: React.FC = () => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const store = useStore();
    const [menuList, setMenuList] = useState<any>([]);
    const [styleList, setStyleList] = useState<any>([]);
    const [materialList, setMaterialList] = useState<any>([]);
    const [type, setType] = useState<any>('');
    const [open,setOpen] = useState<boolean>(false);
    const [updated, setUpdate] = useState(0);

    const [selectedFigureElement, setSelectedFigureElement] = useState<TFigureElement>(null);  //当前选中的图元
    const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>({
        '定制素材': true, //[i18n:ignore]
        '软装素材': true, //[i18n:ignore]
        '硬装素材': true, //[i18n:ignore]
    }); // 用于跟踪每个 item 的展开状态
    const [visible, setVisble] = useState<boolean>(false);

    const toggleExpand = (itemId: string) => {
        setExpandedItems((prev) => ({
            ...prev,
            [itemId]: !prev[itemId], // 切换当前 item 的展开状态
        }));
    };


    interface FigureProps  { image_path: string, title: string, centerTitle: string, bottomTitle: string, checked?: boolean, figure_element: TFigureElement, room?:TRoom }

    let figure_list:FigureProps[] = [];
    let good_list:FigureProps[] = [];
    let hard_decoration_list : FigureProps[] = [];
    let custom_cabinet_list : FigureProps[] = [];

    const UpdateFigureList = () => {

        let styleList: any = [];
        let RoomEntity = store.homeStore.selectEntity as TRoomEntity;
        if(!RoomEntity) return;
        let roomInfo = store.homeStore.selectedRoom?._room;
        if(!roomInfo) return;
        let figure_ele_list = [...roomInfo._furniture_list];
        roomInfo._furniture_list.forEach(fe => figure_ele_list.push(...fe.getAlternativeFigureElements()));
        figure_ele_list.sort((a,b)=>{
          return b.default_drawing_order - a.default_drawing_order;
        })
        figure_ele_list.forEach((item) => {

          if(!LayoutAI_App.IsDebug && (item._is_decoration || item._is_sub_board )) return;
          if(!item.haveMatchedMaterial() && !item.haveDeletedMaterial() && !item.sub_category.includes('组合')) 
          {
            figure_list.push({
              image_path: g_FigureImagePaths[item.sub_category]?.png || "https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",
              title: t(item.modelLoc) + " | " + t(item.sub_category),
              centerTitle: t(item.modelLoc),
              bottomTitle: `${Math.round(item.length)}*${Math.round(item.depth)}*${Math.round(item.height)}`,
              figure_element: item,
              room: roomInfo
            })
            return;
          }
          if(item._decoration_type==="Electricity") return;
          if(!item.haveMatchedMaterial() && !item.haveDeletedMaterial() && item.sub_category.includes('组合')) return;
          let img_path = item._matched_material?.imageUrl || "https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg";
          let modelId = item._matched_material?.modelId || "无"; /*[i18n:ignore]*/
          const good:FigureProps = {
            image_path: img_path,
            title: t(item.sub_category) +" "+ t("素材ID:") + modelId,
            centerTitle: item._matched_material.name,
            bottomTitle: `${Math.round(item?._matched_material?.length)}*${Math.round(item?._matched_material?.width)}*${Math.round(item?._matched_material?.height)}`,
            figure_element: item,
            room:roomInfo
          };
          if (item.haveMatchedCustomCabinet()) {
            custom_cabinet_list.push(good);
          } else {
            good_list.push(good);
          }
        });
        
        good_list.unshift(...figure_list);
        // 更新硬装的内容
        let hard_figures = roomInfo.getHardDecorationList();

        hard_figures.forEach(item=>{
          if (!item.haveMatchedMaterial()) return;
          let img_path = item._matched_material?.imageUrl || "https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg";
          let modelId = item._matched_material?.modelId || "无"; /*[i18n:ignore]*/
          hard_decoration_list.push({
            image_path: img_path,
            title: t(item.sub_category) +" "+ t("素材ID:") + modelId,
            centerTitle: item._matched_material.name,
            bottomTitle: `${Math.round(item?._matched_material?.length)}*${Math.round(item?._matched_material?.width)}*${Math.round(item?._matched_material?.height)}`,
            figure_element: item
          })
        })

        styleList.push({
            title1: roomInfo._scope_series_map?.soft?.ruleName||null,
            title2: roomInfo._scope_series_map?.cabinet?.ruleName||null,
            title3: roomInfo._scope_series_map?.hard?.ruleName||null,
            img1: roomInfo._scope_series_map?.soft?.thumbnail||null,
            img2: roomInfo._scope_series_map?.cabinet?.thumbnail||null,
            img3: roomInfo._scope_series_map?.hard?.thumbnail || null,
            softseriesStyle: roomInfo._scope_series_map?.soft?.seriesName,
            cabinetseriesStyle: roomInfo._scope_series_map?.cabinet?.seriesName,
            hardseriesStyle: roomInfo._scope_series_map?.hard?.seriesName,
            bottomTitle: roomInfo.roomname,
                area: roomInfo.area?.toFixed(2),
                room: roomInfo
            })
    
        let menuList = [
            {   
              label: t('风格'),
              figureList: styleList,
            },
            {
              label: t('定制素材'),
              figureList: custom_cabinet_list
            },
            {
              label: t('软装素材'),
              figureList: good_list
            },
            {
              label: t('硬装素材'),
              figureList: hard_decoration_list,
            }
        ]
        setMenuList(menuList);
        setStyleList(menuList[0]?.figureList);
        setMaterialList(menuList.filter((_, index) => index !== 0));
    }
    
    // 隐藏蒙层
    const hideOverlay = () => {
        setVisble(false);
    };
      
    if (LayoutAI_App.instance) {
        LayoutAI_App.on(EventName.RoomMaterialsUpdated, () => {
          setUpdate(updated + 1);
        });
      }

    useEffect(() => {
        UpdateFigureList();
        
    },[store.homeStore.selectEntity, store.homeStore.room2SeriesSampleArray, updated, open])

    return <div className={styles.root} >
        <div className={styles.styleInfo}>
            <div className={styles.styleItem} onClick={() => {
                    setVisble(true);
                    setType('软装'); /*[i18n:ignore]*/
                }}>
                {styleList[0]?.title1 && styleList[0]?.img1 ? 
                <div className="item">
                    <img src={styleList[0]?.img1} alt="" />
                    <div className="title">{t("软装")}</div>
                    <div className="rightitem">
                        <span>{styleList[0]?.title1}</span>
                        <span className="seriesStyle">{styleList[0]?.softseriesStyle}</span>
                        {/* <IconFont className="icon" type="icon-suoding1"></IconFont> */}
                    </div>

                </div>
                : 
                <span className="add"><IconFont style={{color: '#5B5E60', fontSize: '14px', marginRight: '6px'}} type="icon-anzhuangInstall"></IconFont>{t('添加软装风格')}</span>
                }
            </div>
            <div className={styles.styleItem} onClick={() => {
                    setVisble(true);
                    setType('定制'); /*[i18n:ignore]*/
                }}>
                {styleList[0]?.title2 && styleList[0]?.img2 ? 
                <div className="item">
                    <img src={styleList[0]?.img2} alt="" />
                    <div className="title">{t("定制")}</div>
                    <div className="rightitem">
                        <span>{styleList[0]?.title2}</span>
                        <span className="seriesStyle">{styleList[0]?.cabinetseriesStyle}</span>
                        {/* <IconFont className="icon" type="icon-suoding1"></IconFont> */}
                    </div>
                </div>
                : 
                <span className="add"><IconFont style={{color: '#5B5E60', fontSize: '14px', marginRight: '6px'}} type="icon-anzhuangInstall"></IconFont>{t('添加定制风格')}</span>
                }
            </div>
            <div className={styles.styleItem} onClick={() => {
                    setVisble(true);
                    setType('硬装'); /*[i18n:ignore]*/
                }}>
                {styleList[0]?.title3 && styleList[0]?.img3 ? 
                <div className="item">
                    <img src={styleList[0]?.img3} alt="" />
                    <div className="title">{t("硬装")}</div> 
                    <div className="rightitem">
                        <span>{styleList[0]?.title3}</span>
                        <span className="seriesStyle">{styleList[0]?.hardseriesStyle}</span>
                        {/* <IconFont className="icon" type="icon-suoding1"></IconFont> */}
                    </div>
                </div>
                : 
                <span className="add"><IconFont style={{color: '#5B5E60', fontSize: '14px', marginRight: '6px'}} type="icon-anzhuangInstall"></IconFont>{t('添加硬装风格')}</span>
                }
            </div>
        </div>
        <div className={styles.materialInfo}>
            {materialList.map((item: menuListProps) => (
                <div className='itemInfo' key={item.label}>
                    <div className="header">
                        <span className="title" onClick={() => toggleExpand(item.label)}>{item.label}{expandedItems[item.label]}</span>
                        {
                            expandedItems[item.label] ? 
                            <IconFont 
                                type="icon-a-fangxiangxia" 
                                style={{fontSize: 16, color:'#959598'}}
                                onClick={() => toggleExpand(item.label)}/>
                            :
                            <IconFont 
                                type="icon-a-fangxiangyou" 
                                style={{fontSize: 16, color:'#959598'}}
                                onClick={() => toggleExpand(item.label)}/>
                        }
                    </div>
                    {expandedItems[item.label] && ( // 仅在展开时渲染素材列表
                        <div className='itemList'>
                            {item.figureList.map((subItem: FigureItem, index:number) => (
                                <div className='item' key={index} onClick={() => {
                                    setOpen(true);
                                    store.homeStore.setSelectEntity(subItem.figure_element.furnitureEntity);
                                    setSelectedFigureElement(subItem.figure_element);
                                    if(!subItem?.figure_element?.haveMatchedMaterial())
                                    {
                                        message.error(t(`当前套系缺失${subItem?.figure_element.sub_category}素材，请联系管理员补全`));
                                    }
                                    if(subItem?.figure_element.haveMatchedMaterial() && !subItem?.figure_element.checkIsMatchedSizeSuitable())
                                    {
                                        message.warning(t(`超出目标尺寸范围`));
                                    }
                                }}>
                                    {subItem?.figure_element.haveMatchedMaterial() && !subItem?.figure_element.checkIsMatchedSizeSuitable() && 
                                    <IconFont
                                        className={styles.warn_icon} 
                                        type="icon-a-tianchongFace"
                                        
                                    />}
                                    {!subItem?.figure_element?.haveMatchedMaterial() && <IconFont type="icon-a-tianchongFace-1" className="redIcon"></IconFont>}
                                    <img src={subItem.image_path} alt="" />
                                    <div>{subItem.centerTitle}</div>
                                    <div style={{color: '#959598', marginTop: '4px'}}>{subItem.bottomTitle}</div>

                                    {subItem?.figure_element?.haveMatchedMaterial() && 
                                        <IconFont className={styles.lock_icon} type={subItem.figure_element?.locked ? "icon-suoding1":"icon-jiesuo1"} onClick={(ev) => {
                                            if(subItem.room && subItem.room?.locked) return;
                                            if(subItem.figure_element)
                                            {
                                                subItem.figure_element.locked = !subItem.figure_element.locked;
                                                
                                                LayoutAI_App.emit(EventName.RoomMaterialsUpdated,true);
                                                LayoutAI_App.instance.update();
                                                ev.stopPropagation();
                                                setSelectedFigureElement(subItem.figure_element);
                                            }
                                        }}></IconFont>
                                    }   
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            ))}
        </div>


        {visible && (
            <div className={styles.visible} onClick={hideOverlay}>
                <div className={`${styles.serialsInfo} ${visible ? 'show' : ''}`} onClick={(e) => e.stopPropagation()}>
                    <SeriesList type={type}></SeriesList>
                </div>
            </div>
        )}
       <div className={`${styles.sideVisible} ${open ? styles.slideIn : styles.slideOut}`}>
            <div className={'sideTopInfo'}>
                <div>{t('模型位信息')}</div>
                <div>
                    <IconFont type="icon-icon" style={{color: '#5B5E60'}} onClick={() => {
                        setOpen(false);
                    }} />
                </div>
            </div>
            
            <Replace selectedFigureElement={selectedFigureElement}></Replace>
        </div>
    </div>
};

export default observer(SearchMaterial);
