import { workDomainMap } from "@/config";

const useUploader = () => {
  const openDB = (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('LayoutAI_DB', 1);

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('files')) {
          db.createObjectStore('files', { keyPath: 'id' });
        }
      };

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        reject(request.error);
      };
    });
  };

  const saveImageToDB = async (base64: string) => {
    try {
      const db = await openDB();
      const transaction = db.transaction('files', 'readwrite');
      const store = transaction.objectStore('files');

      const putRequest = store.put({ id: 'CopyingBase64', data: base64, type: 'png' });
      putRequest.onsuccess = () => {
        console.log('House ID saved to IndexedDB');
      };
      putRequest.onerror = (event) => {
        console.error('Error saving House ID to IndexedDB:', event);
      };
    } catch (error) {
      console.error('Error opening IndexedDB:', error);
    }
  };

  const saveDwgToDB = async (base64: string) => {
    try {
      const db = await openDB();
      const transaction = db.transaction('files', 'readwrite');
      const store = transaction.objectStore('files');

      const putRequest = store.put({ id: 'DwgBase64', data: base64, type: 'dwg' });
      putRequest.onsuccess = () => {
        console.log('House ID saved to IndexedDB');
      };
      putRequest.onerror = (event) => {
        console.error('Error saving House ID to IndexedDB:', event);
      };
    } catch (error) {
      console.error('Error opening IndexedDB:', error);
    }
  };

  const saveHouseIdToDB = async (id: string) => {
    try {
      const db = await openDB();
      const transaction = db.transaction('files', 'readwrite');
      const store = transaction.objectStore('files');

      // 使用固定 id 保存
      const putRequest = store.put({ id: 'HouseId', data: id });
      putRequest.onsuccess = () => {
        console.log('House ID saved to IndexedDB');
      };
      putRequest.onerror = (event) => {
        console.error('Error saving House ID to IndexedDB:', event);
      };

    } catch (error) {
      console.error('Error opening IndexedDB:', error);
    }
  };

  const saveInfoToDB = async (data: {
    houseTypeName: string;
    bathroom?: string;
    hall?: string;
    kitchen?: string;
    room?: string;
    towards?: string;
    roomTypeName?: string;
    area?: number;
    province?: string;
    city?: string;
    district?: string;
    imageUrl?: string;
    source: string;
    from: string;
  }) => {
    try {
      const db = await openDB();
      const transaction = db.transaction('files', 'readwrite');
      const store = transaction.objectStore('files');

      const putRequest = store.put({ id: 'HxInfo', data });
      putRequest.onsuccess = () => {
        console.log('House information saved to IndexedDB');
        // 保存后跳转
        if (data.source === '2') {
          window.location.href = `${workDomainMap}/editor/padmobile?mode=DwgBase64&from=local`;
        } else if (data.source === '3') {
          window.location.href = `${workDomainMap}/editor/padmobile?mode=CopyingBase64&from=local`;
          // window.location.href = `https://hws-zoucanxing.3weijia.com:8082/padmobile?mode=CopyingBase64&from=local`;
        } else if (data.source === '1') {
          window.location.href = `${workDomainMap}/editor/padmobile?mode=HouseId&from=local`;
        }
      };
      putRequest.onerror = (event) => {
        console.error('Error saving house information to IndexedDB:', event);
      };

    } catch (error) {
      console.error('Error opening IndexedDB:', error);
    }
  };

  return { saveImageToDB, saveDwgToDB, saveHouseIdToDB, saveInfoToDB };
};

export default useUploader;
