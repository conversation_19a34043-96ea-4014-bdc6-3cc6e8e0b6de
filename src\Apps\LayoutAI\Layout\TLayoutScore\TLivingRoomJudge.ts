import { compareNames } from "@layoutai/z_polygon";
import { UI_FormatType, UI_Grade } from "../IUIInterface";
import { WPolygon } from "../TFeatureShape/WPolygon";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TLayoutLivingRoomRuleParamConfiguration } from "../TLayoutScoreConfigurationTool/TLayoutLivingRoomRuleParamConfiguration";
import { TLayoutScoreParamName } from "../TLayoutScoreConfigurationTool/TLayoutScoreParamName";
import { TRoom } from "../TRoom";
import { TElementAreaCheckRule } from "./CheckRules/AreaCheckRules/TElementAreaCheckRule";
import { TBaseRoomToolUtil } from "./CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TBaseWindowOrDoorOcclusionCheckRule } from "./CheckRules/BasicCheckRules/TBaseWindowOrDoorOcclusionCheckRule";
import { TDiningFunctionCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningFunctionCheckRule";
import { TDiningSpaceLenAndWidthRatioCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningSpaceLenAndWidthRatioCheckRule";
import { TDiningSpaceMinLenAndDepthCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningSpaceMinLenAndDepthCheckRule";
import { TDiningSpaceUsageRatioCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningSpaceUsageRatioCheckRule";
import { TDiningTabelCenterCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningTabelCenterCheckRule";
import { TDiningTableChairSideMinDistanceCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningTableChairSideMinDistanceCheckRule";
import { TDiningTableToCabinetSubFlowLineCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningTableToCabinetSubFlowLineCheckRule";
import { TLivingAndDiningAreaRatioCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingAndDiningAreaRatioCheckRule";
import { TLivingAndDiningFlowCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingAndDiningFlowCheckRule";
import { TLivingEntranceCabinetCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingEntranceCabinetCheckRule";
import { TLivingFunctionCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingFunctionCheckRule";
import { TLivingNecessaryCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingNecessaryCheckRule";
import { TLivingRoomCabinetFrontAreaCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomCabinetFrontAreaCheckRule";
import { TLivingRoomFiguresOverlayCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomFiguresOverlayCheckRule";
import { TLivingRoomFlowLineCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomFlowLineCheckRule";
import { TLivingRoomSofaAndBalconySubFlowLineCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomSofaAndBalconySubFlowLineCheckRule";
import { TLivingRoomSofaAndTVSubFlowLineCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomSofaAndTVSubFlowLineCheckRule";
import { TLivingRoomSpacePositionCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomSpacePositionCheckRule";
import { TLivingRoomSpaceUsageCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomSpaceUsageCheckRule";
import { TLivingRoomToolUtil } from "./CheckRules/LivingRoomCheckRules/TLivingRoomToolUtil";
import { TLivingSofaAndTVMinDistanceCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingSofaAndTVMinDistanceCheckRule";
import { TLivingSpaceLenAndDepthCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingSpaceLenAndDepthCheckRule";
import { TLivingSpaceLenAndWidthRatioCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingSpaceLenAndWidthRatioCheckRule";
import { TSofaCenterCheckRule } from "./CheckRules/LivingRoomCheckRules/TSofaCenterCheckRule";
import { TSofaFaceEntranceDoorCheckRule } from "./CheckRules/LivingRoomCheckRules/TSofaFaceEntranceDoorCheckRule";
import { TGroupCheckRule } from "./CheckRules/TGroupCheckRule";
import { I_LayoutScore, TLayoutJudge } from "./TLayoutJudge";
import { TLayoutRuleNameUtil } from "./TLayoutRuleNameUtil";
import { TDiningTableSpaceAreaLenAndDepthCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningTableSpaceAreaLenAndDepthCheckRule";
import { TSofaGroupSpaceAreaLenAndDepthCheckRule } from "./CheckRules/LivingRoomCheckRules/TSofaGroupSpaceAreaLenAndDepthCheckRule";
import { TLivingRoomFunctionRichnessCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomFunctionRichnessCheckRule";
import { TDiningTableNearKitchenCheckRule } from "./CheckRules/LivingRoomCheckRules/TDiningTableNearKitchenCheckRule";
// import { TLivingRoomSubSpaceFlowCheckRule } from "./CheckRules/LivingRoomCheckRules/TLivingRoomSubSpaceFlowCheckRule";

export class TLivingRoomJudge extends TLayoutJudge
{
    constructor()
    {
        super();
        this.name = "TLivingRoomJudge";
        this.ruleParamConfig = new TLayoutLivingRoomRuleParamConfiguration();
        let intakeRule = new TElementAreaCheckRule(TLivingRoomToolUtil.instance.cabinetFigureNames, {
            ruleName: TLayoutJudge.StorageScore,
            ui_format: [UI_FormatType.Name, UI_FormatType.Percentage, UI_FormatType.Grade],
            parentJudge: this,
            roomScoreFunc: (score: number, room: TRoom) => {
                let ratio: number = score / room.area;
                let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(TLayoutScoreParamName.storageGroup, [TLayoutScoreParamName.livingRoomStorageRatioRule], ratio);
                return resultScore;
            },
            gradeFunc: (score, value) => {
                if (score < 0) {
                    return UI_Grade.GradeE;
                }
                else if (score < 30 && score > 0) {
                    return UI_Grade.GradeB;
                }
                else {
                    return UI_Grade.GradeAp;
                }
            },
        });


        // 空间利用率
        let spaceUsageRule =  new TGroupCheckRule(TLayoutJudge.SpaceUsedScore, [
            new TDiningSpaceUsageRatioCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.diningSpaceUsageRatio,
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                computedTotalScore: true,
                parentJudge: this,
                roomScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc(indexValue: number) {
                    return  0;
                },
            }),
        ], {
            ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
            gradeFunc: (score, value) => {
                if (score < 0) {
                    return UI_Grade.GradeE;
                }
                else if (score < 70 && score >= 0) {
                    return UI_Grade.GradeB;
                }
                else {
                    return UI_Grade.GradeAp;
                }
            },
        });
        
        // 分区合理性
        let splitSpaceRule = new TGroupCheckRule(TLayoutJudge.SplitSpaceScore, 
            [
                // 必备区域: (客厅区, 餐厅区)
                new TLivingNecessaryCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.necessaryArea,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: true,
                    isSortAndFilter: true,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    continuityFunc(indexValue: number) {
                        return  0;
                    },
                }),

                // 客厅区与餐厅区面积比
                new TLivingAndDiningAreaRatioCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.livingAndDiningAreaRatio,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: true,
                    isSortAndFilter: true,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    continuityFunc(indexValue: number) {
                        return  0;
                    },
                }),
                
                // 【客厅】区域长宽比
                new TLivingSpaceLenAndWidthRatioCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.livingLenAndWidthRatio,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: true,
                    isSortAndFilter: true,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    continuityFunc(indexValue: number) {
                        return  0;
                    },
                }),

                // 【餐厅】区域长宽比
                new TDiningSpaceLenAndWidthRatioCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.diningLenAndWidthRatio,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: true,
                    isSortAndFilter: true,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    continuityFunc(indexValue: number) {
                        return  0;
                    },
                }),

                // 分区相对位置正确
                new TLivingRoomSpacePositionCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.spacePosition,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: true,
                    isSortAndFilter: true,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    continuityFunc(indexValue: number) {
                        return  0;
                    },
                }),

                // 餐厅区最小面积
                new TDiningSpaceMinLenAndDepthCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.minDiningLenAndDepth,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: true,
                    isSortAndFilter: true,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    continuityFunc(indexValue: number) {
                        return  0;
                    },
                }),

                // 客厅区最小面积
                new TLivingSpaceLenAndDepthCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.minLivingLenAndDepth,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: true,
                    isSortAndFilter: true,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    continuityFunc(indexValue: number) {
                        return  0;
                    },
                }),

                // 空间利用率
                new TLivingRoomSpaceUsageCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.livingRoomSpaceUsage,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: true,
                    isSortAndFilter: true,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    continuityFunc(indexValue: number) {
                        return  0;
                    },
                }),
            ],
            {
                // TODO这个参数后续要进行调整
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                gradeFunc: (score: any, value: any) => {
                    if (score < 0) {
                        return UI_Grade.GradeE;
                    }
                    else if (score < 70 && score >= 0) {
                        return UI_Grade.GradeB;
                    }
                    else {
                        return UI_Grade.GradeAp;
                    }
                },
            }
        );

        //  子区域家具评分
        let subSpaceValidRule = new TGroupCheckRule(TLayoutJudge.SubSpaceValidScore, [
            // 就餐区最小最大值尺寸检查
            new TDiningTableSpaceAreaLenAndDepthCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.diningTableSpaceAreaLenAndDepth,
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck: false,
                isSortAndFilter: false,
                roomScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc(indexValue: number) {
                    return  0;
                },
            }),
            // 沙发组合的最小最大值检查
            new TSofaGroupSpaceAreaLenAndDepthCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.livingSofaSpaceAreaLenAndDepth,
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck: false,
                isSortAndFilter: false,
                roomScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc(indexValue: number) {
                    return  0;
                },

            }),

        ],
        {
            ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
            gradeFunc: (score: any, value: any) => {
                if (score < 0) {
                    return UI_Grade.GradeE;
                }
                else if (score < 70 && score >= 0) {
                    return UI_Grade.GradeB;
                }
                else {
                    return UI_Grade.GradeAp;
                }
            },
        });

        // 动线检查
        // 主次动线， 主动线入户门到其他区域的路径， 次动线房间内部的路径区
        // TODO 还应当判断主动线内是否有其他家具 如果有的话-10（这里的家具应该就是固定家具），这个目前暂时还没想到好的方式
        let trafficRule =  new TGroupCheckRule(TLayoutJudge.Flowline, [
            new TLivingRoomFlowLineCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.flowMainFluency,
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck : true,
                roomScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc(indexValue: number) {
                    return  0;
                },
            }),

            // new TLivingRoomSubSpaceFlowCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
            //     ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.subFlowToMain,
            //     computedTotalScore: true,
            //     parentJudge: this,
            //     isBasicCheck: false,
            //     isSortAndFilter: false,
            //     roomScoreFunc: (score: number) => {
            //         return score;
            //     },
            //     continuityFunc(indexValue: number) {
            //         return  0;
            //     },
            // }),

            // 次动线这个会有点麻烦，这个最好也是直接看外轮廓是否会在同一片区域
            new TLivingRoomSofaAndTVSubFlowLineCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.flowSubSofaAndTVMinDistance,
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck: true,
                roomScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc(indexValue: number) {
                    return  0;
                },
            }),

            // 沙发到阳台的次动线
            new TLivingRoomSofaAndBalconySubFlowLineCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.flowSubSofaAndBalconyMinDistance,
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck: true,
                roomScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc(indexValue: number) {
                    return  0;
                },
            }),

            // 餐桌到餐边柜的次动线
            new TDiningTableToCabinetSubFlowLineCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.flowSubDiningTableAndCabinetMinDistance,
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                computedTotalScore: true,
                parentJudge: this,
                roomScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc(indexValue: number) {
                    return  0;
                },
            }),
        ], {
            ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
            gradeFunc: (score, value) => {
                if (score < 0) {
                    return UI_Grade.GradeE;
                }
                else if (score < 70 && score >= 0) {
                    return UI_Grade.GradeB;
                }
                else {
                    return UI_Grade.GradeAp;
                }
            },
        });

        // 布局合理性
        let layOutRule = new TGroupCheckRule(TLayoutJudge.BasicScore, [
            // 家具丰富度
            new TLivingRoomFunctionRichnessCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomSplitByPathRuleNames.livingRoomFunctionRichness,
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck: false,
                isSortAndFilter: false,
                roomScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc(indexValue: number) {
                    return  0;
                },
            }),

            // 玄关柜布置
            new TLivingEntranceCabinetCheckRule(TLivingRoomToolUtil.instance.entranceCabinetFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.entranceCabinetLayout,
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc: (value: number) => {
                    return 0;
                },
            }),

            // 餐厅区布局
            // 餐厅功能丰富度
            new TDiningFunctionCheckRule(TLivingRoomToolUtil.instance.diningFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.diningFunction,
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc: (value: number) => {
                    return 0;
                },
            }),

            // 餐桌中心点在餐边柜中垂线， 或者分区的中心点（500mm的容差） +10 如果不在则-10
            new TDiningTabelCenterCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.diningTabelCenter,
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc: (value: number) => {
                    return 0;
                },
            }),

            // 客厅区布局
            new TLivingFunctionCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.livingFunction,
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc: (value: number) => {
                    return 0;
                },
            }),

            // 沙发中心点不在电视柜或者书柜的中垂线上 -20（容差500mm)
            new TSofaCenterCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.sofaCenter,
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc: (value: number) => {
                    return 0;
                },
            }),

            // 沙发直对入户门
            new TSofaFaceEntranceDoorCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.sofaFaceentranceDoor,
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                },
                continuityFunc: (value: number) => {
                    return 0;
                },
            }),

            // 素材干涉
            new TLivingRoomFiguresOverlayCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.figureOverlay,
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck : true,
                figureScoreFunc: (score: number) => {
                    return score > 0 ? -100 : 0;
                },
                continuityFunc: (value: number) => {
                    return (1 - value) * 100;
                },
            }),

            // 家具挡门
            new TBaseWindowOrDoorOcclusionCheckRule(TLivingRoomToolUtil.instance.fixedFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.figureLayonDoor,
                computedTotalScore: true,
                parentJudge: this,
                otherExtendInfo: {isWindow: false, isGetScore: true},
                isBasicCheck : true,
                figureScoreFunc: (score) => {
                    return score;
                },
                // 计算家具与门的重叠度，重叠长度/家具重叠的边的边长
                continuityFunc: (value: any) => {
                    return -value * 100;
                },
            }),

            // 家具挡窗，这里暂时不管参数设置
            new TBaseWindowOrDoorOcclusionCheckRule(TLivingRoomToolUtil.instance._livingCabinetFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.figureLayonWindow,
                computedTotalScore: true,
                parentJudge: this,
                otherExtendInfo: {isWindow: true, isGetScore: false},
                figureScoreFunc: (score) => {
                    if (score > 0.5) {
                        return -100;
                    }
                    else if (score > 0.2) {
                        return -30;
                    }
                    else if (score > 0.01) {
                        return -10;
                    }
                    return 0;
                },
                // 计算家具与门的重叠度，重叠长度/家具重叠的边的边长
                continuityFunc: (value: any) => {
                    return 0;
                },
            }),

            new TLivingRoomCabinetFrontAreaCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.cabinetFrontAreaLen,
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck:true,
                figureScoreFunc: (score) => {
                    let resultScore: number = 0;
                    let cabinetFrontDist: number = this.ruleParamConfig.getValueByParamConfig(TLayoutScoreParamName.basicGroup, [TLayoutScoreParamName.diningCabinetFrontDistanceRule, TLayoutScoreParamName.diningCabinetFrontDistance]);
                    let errScore: number = this.ruleParamConfig.getValueByParamConfig(TLayoutScoreParamName.basicGroup, [TLayoutScoreParamName.diningCabinetFrontDistanceRule, TLayoutScoreParamName.diningCabinetFrontDistScore]);               
                    if(score < cabinetFrontDist && score >= 0)
                    {
                        resultScore += errScore;
                    }
                    return resultScore;
                },
                continuityFunc: (value: any) => {
                    return 0;
                },
            }),

            // 餐桌背面离障碍物后的距离判断，这个不是餐桌椅
            new TDiningTableChairSideMinDistanceCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.diningTableFlowMinDistance,
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck: true,
                figureScoreFunc: (score) => {
                    let resultScore: number = 0;
                    if(score < 300 && score >= 0)
                    {
                        resultScore -= 100;
                    }
                    return resultScore;
                },
                continuityFunc: (value: any) => {
                    return 0;
                },
            }),

            // // 沙发与茶几的距离
            // new TLivingSofaAndTableMinDistanceCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
            //     ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.sofaAndTableMinDistance,
            //     computedTotalScore: true,
            //     parentJudge: this,
            //     figureScoreFunc: (score) => {
            //         let resultScore: number = 0;
            //         if(score < 250 && score >= 0)
            //         {
            //             resultScore -= 100;
            //         }
            //         else if(score >= 400 && score > 600)
            //         {
            //             resultScore += 10;
            //         }
            //         return resultScore;
            //     },
            //     continuityFunc: (value: any) => {
            //         return 0;
            //     },
            // }),

            new TLivingSofaAndTVMinDistanceCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.sofaAndTVMinDistance,
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck: true,
                figureScoreFunc: (score) => {
                    let resultScore: number = 0;
                    if(score < 1500 && score >= 0)
                    {
                        resultScore -= 60;
                    }
                    else if(score > 5550)
                    {
                        resultScore -= 60;
                    }
                    return resultScore;
                },
                continuityFunc: (value: any) => {
                    return 0;
                },
            }),

            new TLivingAndDiningFlowCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
                ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.livingDiningFlowDistance,
                computedTotalScore: true,
                parentJudge: this,
                isBasicCheck: true,
                figureScoreFunc: (score) => {
                    return score;
                },
                continuityFunc: (value: any) => {
                    return 0;
                },
            }),

            new TDiningTableNearKitchenCheckRule(TLivingRoomToolUtil.instance.allFigureNames, 
                {
                    ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.diningTableNearKitchen,
                    computedTotalScore: true,
                    parentJudge: this,
                    isBasicCheck: false,
                    isSortAndFilter: true,
                    figureScoreFunc: (score) => {
                        return score;
                    },
                    continuityFunc: (value: any) => {
                        return 0;
                    },
                }
            ),

            // // 沙发靠墙检查
            // new TSofaFlowCheckRule(TLivingRoomToolUtil.instance.allFigureNames, {
            //     ruleName: TLayoutRuleNameUtil.instance.livingRoomRuleNames.sofaLayonOtherDistance,
            //     computedTotalScore: true,
            //     parentJudge: this,
            //     figureScoreFunc: (score) => {
            //         return score;
            //     },
            //     continuityFunc: (value: any) => {
            //         return 0;
            //     },
            // }),
        ], {
            ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
            gradeFunc: (score, value) => {
                if (score < 0) {
                    return UI_Grade.GradeE;
                }
                else if (score < 70 && score >= 0) {
                    return UI_Grade.GradeB;
                }
                else {
                    return UI_Grade.GradeAp;
                }
            },
        });

        this._check_rules = [
            intakeRule,
            // spaceUsageRule,
            splitSpaceRule,
            subSpaceValidRule,
            trafficRule,
            layOutRule
        ]
    }

    computeScoreInRoom(room: TRoom, figure_elements:TFigureElement[]): I_LayoutScore[] {
        if (!room) return [];
        if (compareNames([room.roomname], ["客餐厅"]) == 0) return [];

        figure_elements = figure_elements || room._furniture_list;
        let result: I_LayoutScore[] = [];

        // console.time("checkInRoom");
        if(room._room_entity)
        {
            WPolygon.getOutterWallRects(room._room_entity._room_poly, 120, true);
        }
        WPolygon.getOutterWallRects(room.room_shape._poly, 120, true);
        this._check_rules.forEach((rule, index) => {
            result.push(rule.computeLayoutScore(room, figure_elements, index));
        });
        // console.timeEnd("checkInRoom");
        if(room._room_entity)
        {
            WPolygon.cleanOutterWallRects(room._room_entity._room_poly);
        }
        WPolygon.cleanOutterWallRects(room.room_shape._poly);
        // console.log(result);

        let allSingleFigures: TFigureElement[] = TBaseRoomToolUtil.instance.getAllSingleFigureFromGroup(figure_elements);
        TBaseRoomToolUtil.instance.checkAbnormalFigures(room, result, allSingleFigures, [TLayoutRuleNameUtil.instance.livingRoomRuleNames.figureOverlay]);
        
        return result;

    }


}