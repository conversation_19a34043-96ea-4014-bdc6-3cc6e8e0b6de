


import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
    padHousePanelRoot: css`
        `,
    sideToolbarContainer: css`  
        position:fixed;
        right:0;
        z-index:9;
        top:0px;
        transition: all 0.2s ease;
    `,  
    topBar: css`
        position:fixed;
        top:0;
        z-index:9;
        display:flex;
        justify-content:center;
        align-items:center;
        height:34px;
        background:#fff;
        width: 140px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 6px;
        border: 2px solid #E5E5E5;
        font-size: 14px;
    `,
    bottomButtons: css`
        position:fixed;
        bottom:13px;
        z-index:9;
        display:flex;
        justify-content:center;
        align-items:center;
        left: 50%;
        transform: translate(-50%, 0);
        transition: all 0.5s ease;
        .btn {
            border-radius: 50px;
            background: #FFFFFF;
            box-shadow: 0px 6px 20px 0px #00000014;
            width : 140px;
            border: none;
            height : 48px;
            color: #282828;
            font-size: 14px;
            line-height: 48px;
            letter-spacing: 0px;
            text-align: center;
            margin-left:12px;
            margin-right:12px;
        }

        @media screen and (orientation: landscape){
            display:flex;
            justify-content:center;
            align-items:center;
            left: 50%;
            transform: translate(-50%, 0);
        }
        `
    }

}); 