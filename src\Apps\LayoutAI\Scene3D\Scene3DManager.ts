import { Color, FloatType, Group, Mesh, MeshBasicMaterial, MeshStandardMaterial, Vector3, WebGLRenderTarget, WebGLRenderer } from "three";
import { TFigureElement } from "../Layout/TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../Layout/TLayoutEntities/TBaseGroupEntity";
import { TLayoutEntityContainer } from "../Layout/TLayoutEntities/TLayoutEntityContainter";
import { ZRect, compareNames } from "@layoutai/z_polygon";
import { Scene3D } from "./Scene3D";
import { UserDataKey } from "./NodeName";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { I_Scene3DManager, LayoutAI_App, Scene3DRenderPerformanceMode } from "@/Apps/LayoutAI_App";
import { TextureManager } from "@layoutai/model3d_api";
import { EventName } from "@/Apps/EventSystem";
import { TDesignMaterialUpdater } from "../Services/MaterialMatching/TDesignMaterialUpdater";
import { Utils3D } from "./Utils3D";
import { TRoomEntity } from "../Layout/TLayoutEntities/TRoomEntity";
import { TWindowDoorEntity } from "../Layout/TLayoutEntities/TWinDoorEntity";
import { LayoutAI_Configs } from "../Layout/TLayoutEntities/configures/LayoutAIConfigs";
import { FigureViewControls } from "./controls/FigureViewControls";
import { TViewCameraEntity } from "../Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";


/**
 *   3D场景管理器---用它跟LayoutContainer 与 Scene3D 进行关联管理
 */
export class Scene3DManager implements I_Scene3DManager {
    protected _manager: TAppManagerBase;
    protected _scene3D: Scene3D;

    protected _currentRoomEntity: TRoomEntity;
    protected _updatedRoomUuid : string;
    protected _updatingRoomUuids : string[];
    protected static ObjectId = "Scene3DManager";
    constructor(manager: TAppManagerBase) {
        this._manager = manager;
        this._currentRoomEntity = null;
        this._updatingRoomUuids = [];
        LayoutAI_App.on_M(EventName.Scene3DViewUpdate,Scene3DManager.ObjectId, ()=>{
            this.onCameraViewChanged();
        });
    }

    initScene3D(force: boolean): void {
        this._scene3D = new Scene3D();
    }
    get container() {
        return this._manager.layout_container;
    }

    get scene3D() {
        return this._scene3D as Scene3D;
    }

    get renderPerformanceMode(): Scene3DRenderPerformanceMode {
        return LayoutAI_Configs.Configs.renderPerformanceMode || "Default";
    }
    set renderPerformanceMode(value: Scene3DRenderPerformanceMode) {
        LayoutAI_Configs.Configs.renderPerformanceMode = value;
    }

    public get currentRoomEntity(): TRoomEntity {
        return this._currentRoomEntity;
    }
    public set currentRoomEntity(value: TRoomEntity) {
        this._currentRoomEntity = value;
    }
    bindMainDiv(div: HTMLDivElement) {
        if (this.scene3D) {
            this.scene3D.bindMainDiv(div);
        }
    }
    onElementUpdate(ele: any, options: { isWhite?: boolean, attached_mesh?: Mesh, [key: string]: any } = {}) {
        if (ele instanceof TFigureElement) {
            if (compareNames([ele.category, ele.sub_category], ["墙面", "地面"])) {
                let matched_info = ele._matched_material;
                let mesh = ele._solid_mesh3D as Mesh;
                if (mesh && mesh instanceof Mesh) {
                    if (matched_info.imageUrl) {
                        TextureManager.updateMeshTextureWithImg(mesh, matched_info.imageUrl, matched_info.modelId);
                    }
                    if (compareNames([ele.sub_category, ele.category], ["地面"])) {
                        let fill_color = ele.fill_color || "#aaa";
                        (mesh.material as MeshBasicMaterial).color = new Color(fill_color);;
                    }

                }

            }
            else {
                let solid_mesh = ele._solid_mesh3D;
                let group_node = solid_mesh as Group;
                if (group_node) {
                    let design_material_info = group_node.userData[UserDataKey.MaterialInfo];
                    if (ele.pictureViewImg && design_material_info && design_material_info.ContentUrl) {
                        if (!options.isWhite) {
                        }
                        ele.updateRenderedTopViewImg();
                    } else if (ele.shouldUseRenderedTopViewImg()) {
                        ele.updateRenderedTopViewImg();
                    }
                    let scene3d = this.scene3D;
                    if (scene3d) {
                        scene3d.outlinePostProcessing.makeOutlineDirty();
                        let selEle = scene3d.selection_box?.userData[UserDataKey.EntityOfMesh];
                        if (selEle == ele) {
                            scene3d.setSelectionBox(ele);
                        }
                    }
                }

            }


        }
    }
    UpdateScene3DWithMaterials = async (force: boolean = false, options: { needs_centered?: boolean } = {}) => {

        if(this.renderPerformanceMode === "SingleRoom")
        {
            this.UpdateScene3DWithMaterialsInSingleRoom(force);
            return;
        }
        let container = this.container;
        LayoutAI_App.emit_M(EventName.SceneContentStateChanged, { state: 0 });

        this.updateScene3D(force);

        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: true, title: "更新3D场景中..." });

        let validCabinetStyleIds: string[] = [];
        container._rooms.forEach((room) => {
            if (room._cabinetStyleId) {
                if (!validCabinetStyleIds.includes(room._cabinetStyleId)) validCabinetStyleIds.push(room._cabinetStyleId);
            }
        });
        TextureManager.instance.clearCache(validCabinetStyleIds);

        // 并行处理所有房间的材质更新
        const roomPromises = force ? container._rooms.map(room =>
            TDesignMaterialUpdater.instance.updateRoomFurnitureDesignMaterials(room, null, force)
        ) : [];

        // 并行处理所有窗户的材质更新
        const windowPromises = TDesignMaterialUpdater.instance.updateWindowsFurnitureDesignMaterials(
            container._window_entities.filter((win) => !win?._win_figure_element?._solid_mesh3D)
        );

        // 等待所有材质更新完成
        await Promise.allSettled([...roomPromises, windowPromises]);

        let scene3d = (LayoutAI_App.instance).scene3D as Scene3D;
        scene3d.setLightMode(scene3d.getLightMode());
        scene3d.entity_mesh_mode = scene3d.entity_mesh_mode;
        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: false, title: "" });

        scene3d.update();
        // 通知给padMobile, 所有材质更新完成， 更新视角
        LayoutAI_App.emit_M(EventName.updateAllMaterialScene3D, { state: 1 });
    }

    UpdateScene3DWithMaterialsInSingleRoom = async (force : boolean = false)=>
    {
        if(this.renderPerformanceMode !== "SingleRoom") return;
        let container = this.container;
        if(!this.currentRoomEntity)
        {
            this.currentRoomEntity = container._selected_room?._room_entity || null;

            // 依然找不到, 找面积最大的房间
            let roomEntities = [...container._room_entities];

            roomEntities.sort((a,b)=>b._area - a._area);

            this.currentRoomEntity = roomEntities[0] || null;
        }
        if(!this.currentRoomEntity) return;
        if(!this._updatingRoomUuids.includes(this.currentRoomEntity._uuid))
        {
            this._updatingRoomUuids.push(this.currentRoomEntity._uuid);
        }
        LayoutAI_App.emit_M(EventName.SceneContentStateChanged, { state: 0 });

        this.updateScene3D(force);

        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: true, title: "更新3D场景中..." });

        let validCabinetStyleIds: string[] = [];
        container._rooms.forEach((room) => {
            if (room._cabinetStyleId) {
                if (!validCabinetStyleIds.includes(room._cabinetStyleId)) validCabinetStyleIds.push(room._cabinetStyleId);
            }
        });
        TextureManager.instance.clearCache(validCabinetStyleIds);
        if(!this.currentRoomEntity) return;
        const currentRoomEntity = this.currentRoomEntity;
        if(currentRoomEntity.furniture_entities.length == 0)
        {
            if(currentRoomEntity._room?._furniture_list && currentRoomEntity._room?._furniture_list.length > 0)
            {
                currentRoomEntity._room._furniture_list.forEach((ele)=>{
                    if(ele.furnitureEntity)
                    {
                        ele.furnitureEntity.roomEntity = currentRoomEntity;
                        if(ele.furnitureEntity instanceof TBaseGroupEntity)
                        {
                            ele.furnitureEntity._combination_entitys.forEach((entity)=>{
                                entity.roomEntity = currentRoomEntity;
                            })
                        }
                    }
                })
            }
        }
        let mesh_groups = [this.scene3D.furniture_group];

        for (let group of mesh_groups) {
            let to_remove_meshes: Mesh[] = [];

            for (let mesh of group.children) {
                let entity = mesh.userData[UserDataKey.EntityOfMesh];
                if (!entity || !this.container.containsEntity(entity)) {
                    to_remove_meshes.push(mesh as any);
                }
            }
            // to_remove_meshes.forEach(mesh => Utils3D.disposeObject(mesh));
            group.remove(...to_remove_meshes);
        }

        // 清空家具
        // 家具素材列表
        for(let entity of container._furniture_entities)
        {
            let figure_elements = entity.displayed_figure_elements;

            figure_elements.forEach((ele)=>{
                if(ele._solid_mesh3D) // 清空solidMesh3D
                {
                    Utils3D.disposeObject(ele._solid_mesh3D);
                    ele._solid_mesh3D.removeFromParent();
                    ele._solid_mesh3D = null;
                }
            })
            if(entity._mesh3d){
                entity._mesh3d.removeFromParent();
            }

        }
        currentRoomEntity.furniture_entities.forEach((entity)=>{
                // 删除后不更新simple_mesh3D
                if(entity.rect?.ex_prop['is_deleted'] === '1' || entity?.matched_rect?.ex_prop['is_deleted'] === '1') return;
                if(!entity._mesh3d)
                {
                    entity.updateMesh3D();
                }
                if (entity instanceof TBaseGroupEntity) {
                    let groupEntity = entity as TBaseGroupEntity;
                    groupEntity.combination_entitys.forEach((entity) => {

                        this.scene3D.furniture_group.add(entity.updateMesh3D());
                    });
                    
                } else {
                    let figure_mesh = entity.updateMesh3D();
                    if (figure_mesh) {
                        this.scene3D.furniture_group.add(figure_mesh);
                    }
                }            
            
        })
        

        let room = currentRoomEntity._room;

        // 并行处理所有房间的材质更新
        const roomPromises =[ TDesignMaterialUpdater.instance.updateRoomFurnitureDesignMaterials(room, null, force,false)];


        let _window_entities = currentRoomEntity.getWindowEntities() as TWindowDoorEntity[];
        // 并行处理所有窗户的材质更新
        const windowPromises = TDesignMaterialUpdater.instance.updateWindowsFurnitureDesignMaterials(
            _window_entities.filter((win) => !(win?._win_figure_element?._solid_mesh3D))
        );

        // 等待所有材质更新完成
        await Promise.allSettled([...roomPromises, windowPromises]);



        currentRoomEntity.furniture_entities.forEach((entity)=>{
            // 删除后不更新simple_mesh3D
            if(entity.rect?.ex_prop['is_deleted'] === '1' || entity?.matched_rect?.ex_prop['is_deleted'] === '1') return;
            if(!entity._mesh3d)
            {
                entity.updateMesh3D();
            }
            if (entity instanceof TBaseGroupEntity) {
                let groupEntity = entity as TBaseGroupEntity;
                groupEntity.combination_entitys.forEach((entity) => {
                    if(entity._mesh3d)
                    {
                        this.scene3D.furniture_group.add(entity._mesh3d);
                    }
                });
                
            } else {
                let figure_mesh = entity._mesh3d;
                if (figure_mesh) {
                    this.scene3D.furniture_group.add(figure_mesh);
                }
            }            
            
        })
        
        let scene3d = (LayoutAI_App.instance).scene3D as Scene3D;
        scene3d.setLightMode(scene3d.getLightMode());
        scene3d.entity_mesh_mode = scene3d.entity_mesh_mode;
        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: false, title: "" });

        scene3d.update();

        this._updatedRoomUuid = currentRoomEntity._uuid;
        let tid = this._updatingRoomUuids.indexOf(this._updatedRoomUuid);
        if(tid >= 0) this._updatingRoomUuids.splice(tid);

        for (let entity of container._window_entities)  
        {
            let mesh = entity.updateMesh3D();
            if (mesh) {
                this.scene3D.windows_group.add(mesh);
            }
        }

        // 通知给padMobile, 所有材质更新完成， 更新视角
        LayoutAI_App.emit_M(EventName.updateAllMaterialScene3D, { state: 1 });
    }

    bindOnSelectFigure(func: (...args: any) => void): void {
        if (this.scene3D?.raycasteControls) {
            this.scene3D.raycasteControls.onSelectedFigure = func;
        }
    }
    bindOnSelectRoom(func: (...args: any) => void): void {
        if (this.scene3D?.raycasteControls) {
            this.scene3D.raycasteControls.onSelectedRoom = func;
        }
    }

    updateScene3D(force: boolean = false) {
        if (!this.scene3D) return;
        let container = this.container;


        if (force) {
            this.scene3D.reset();
            this.scene3D.cleanWalls();
            this.scene3D.cleanWindows();
            this.scene3D.cleanFurnitures();
            this.scene3D.cleanRoomEntities();
            this.scene3D.updateShadowTexture();
            this.updateFillLights(true);

            for (let entity of container._window_entities)  // 先更新 门窗
            {
                let mesh = entity.updateMesh3D();
                if (mesh) {
                    this.scene3D.windows_group.add(mesh);
                }
            }

            for (let wall of container._wall_entities) {

                let wall_mesh = wall.updateMesh3D();
                if (wall_mesh) {
                    this.scene3D.walls_group.add(wall_mesh);
                }
            }
            for (let entity of container._room_entities) {
                this.scene3D.rooms_group.add(entity.updateMesh3D());
            }
            for (let entity of container._furniture_entities) {
                if (entity instanceof TBaseGroupEntity) {
                    let groupEntity = entity as TBaseGroupEntity;
                    if (groupEntity.figure_element.haveMatchedGroupMaterial() && !groupEntity.figure_element.isMaterialMarkAsInvisible()) {
                        this.scene3D.furniture_group.add(groupEntity.updateMesh3D());
                    } else {
                        groupEntity.combination_entitys.forEach((entity) => {
                            if (entity.figure_element.isMaterialMarkAsInvisible()) {
                                return;
                            }
                            this.scene3D.furniture_group.add(entity.updateMesh3D());
                        });
                    }
                } else {
                    let figure_mesh = entity.updateMesh3D();
                    if (figure_mesh && !entity.figure_element.isMaterialMarkAsInvisible()) {
                        this.scene3D.furniture_group.add(figure_mesh);
                    }
                }
            }

            if (container._rooms) {
                let lights: TFigureElement[] = [];
                container._rooms.forEach((room) => {
                    lights.push(...room._furniture_list.filter(light => compareNames([light.category], ["主灯"])))
                });

                this.scene3D.addNightLights(lights);
            }


            // this.scene3D.setCenter(this.painter.p_center);
            /**
             *   用自身等于自身的方法, 触发一次更新
             */
            const temp = this.scene3D.entity_mesh_mode;
            this.scene3D.entity_mesh_mode = temp;

            if (this.scene3D.active_controls) {
                this.scene3D.active_controls.updateMeshesVisible();
            }
        }
        else {
            let mesh_groups = [this.scene3D.furniture_group, this.scene3D.walls_group, this.scene3D.windows_group];
            for (let group of mesh_groups) {
                let to_remove_meshes: Mesh[] = [];

                for (let mesh of group.children) {
                    let entity = mesh.userData[UserDataKey.EntityOfMesh];
                    if (!entity || !this.container.containsEntity(entity)) {
                        to_remove_meshes.push(mesh as any);
                    }
                }
                to_remove_meshes.forEach(mesh => Utils3D.disposeObject(mesh));
                group.remove(...to_remove_meshes);
            }
        
            this.scene3D.updateShadowTexture();
            // this.scene3D.updateLightingTexture();
            // this.scene3D.createOrbitControls();
            for (let entity of container._window_entities)  // 先更新 门窗
            {
                let mesh = entity.updateMesh3D();
                if (mesh) {
                    this.scene3D.windows_group.add(mesh);
                }
            }

            for (let wall of container._wall_entities) {

                let wall_mesh = wall.updateMesh3D();
                if (wall_mesh) {
                    this.scene3D.walls_group.add(wall_mesh);
                }
            }
            for (let entity of container._room_entities) {
                this.scene3D.rooms_group.add(entity.updateMesh3D());
            }
            for (let entity of container._furniture_entities) {
                let figure_mesh = entity.updateMesh3D();
                if (figure_mesh) {
                    this.scene3D.furniture_group.add(figure_mesh);
                }
            }
            // this.scene3D.setCenter(this.painter.p_center);
            /**
             *   用自身等于自身的方法, 触发一次更新， 用变量的形式去修改
             */
            const temp = this.scene3D.entity_mesh_mode;
            this.scene3D.entity_mesh_mode = temp;

            if (this.scene3D.active_controls) {
                this.scene3D.active_controls.updateMeshesVisible();
            }
        }

    }


    public updateFillLights(force: boolean = false) {
        this.scene3D.cleanFillLights();
        let fillLightsEntities = this.container.getFillLightEntities();
        for (let entity of fillLightsEntities) {
            this.scene3D.aiLightsGroupOffline.add(entity.update3D());
            this.scene3D.aiLightsGroupTest.add(entity.lightMesh);
        }
    }

    onCameraViewChanged()
    {
        let active_controls = this.scene3D.active_controls as FigureViewControls;
        if(!active_controls) return;
        if(this._updatingRoomUuids.length > 0)
        {
            return;
        }
        if(active_controls instanceof FigureViewControls)
        {
            if(active_controls.target_view_entity?._room_entity)
            {
                if(active_controls._checkViewCameraValid() 
                    && this.currentRoomEntity == active_controls.target_view_entity._room_entity)                
                {
                    return; // 直接返回
                }
            }

            let view_pos = active_controls._rect.rect_center;
            if(this.currentRoomEntity)
            {
                if(this.currentRoomEntity._room_poly.containsPoint(view_pos))
                {
                    return; // 也直接返回 
                }
            }
            let roomEntity = this.container._room_entities.find((room)=>room._room_poly.containsPoint(view_pos));

            if(this.scene3D.isRendering() && roomEntity && roomEntity._uuid !== this._updatedRoomUuid)
            {

                if(this.currentRoomEntity !== roomEntity)
                {
                    this._currentRoomEntity = roomEntity;
                    this.container._selected_room = roomEntity?._room || null;
                    this._updatingRoomUuids.push(roomEntity._uuid);

                    // 先直接切换视角

                    this._updateCameraViews({update_imgs:false});
                    this.UpdateScene3DWithMaterialsInSingleRoom();
                }


            }
            
            // this.currentRoomEntity = roomEntity;
            // this.container._selected_room = roomEntity?._room || null;


        }
    }

    public _findFirstViewRoomEntity() : TRoomEntity
    {
        const sceneManager = this;
        const container = this.container;
        let maxAreaRoomEntity = container._room_entities.reduce((maxRoom: TRoomEntity | null, currentRoom: TRoomEntity) => {
            if (!maxRoom) return currentRoom;
            return currentRoom._area > maxRoom._area ? currentRoom : maxRoom;
          }, null);
          let currentRoomEntity = maxAreaRoomEntity;
          if(LayoutAI_Configs.Configs.renderPerformanceMode === "SingleRoom")
          {
              currentRoomEntity = sceneManager.currentRoomEntity || currentRoomEntity;
          }
          return currentRoomEntity;
    }
    async _updateCameraViews(options:{update_imgs?:boolean,force?:boolean}={update_imgs:true,force:false})
    {
        const scene3d = this._scene3D;
        const sceneManager = this;
        const viewsToUpdate: TViewCameraEntity[] = [];
        const container = this.container;

        let currentRoomEntity = this._findFirstViewRoomEntity();
        let active_controls = scene3d.active_controls as FigureViewControls;
        let needsChangeView = false;
        let originViewRect : ZRect = null;

        const _checkAndUpdateCamera = ()=>{
            if(needsChangeView)
            {
                if (currentRoomEntity?._view_cameras && currentRoomEntity._view_cameras[0]) {
                    scene3d.active_controls.bindViewEntity(currentRoomEntity._view_cameras[0]);
                } 
                else 
                {
                    scene3d.setCenter(currentRoomEntity?._main_rect?.rect_center || container.painter.p_center || new Vector3(0, 0, 0));
                }
            }
            else{
                if(originViewRect)
                {
                    active_controls.bindViewRect(originViewRect);
                }
            }
        }
        if(active_controls instanceof FigureViewControls)
        {
            if(sceneManager.currentRoomEntity && LayoutAI_Configs.Configs.renderPerformanceMode === "SingleRoom") // 如果是单空间模式
            {
                 let view_pos = active_controls._rect.rect_center;
                 if(!active_controls._checkViewCameraValid() && sceneManager.currentRoomEntity._room_poly.containsPoint(view_pos))                 
                 {
                    needsChangeView = false; // 如果视角已经在当前房间内, 则无需自动切换(设置成false)| 0825---全都设置成不改变视角
                    originViewRect = active_controls._rect.clone();
                 }
            }                            
        }

        _checkAndUpdateCamera();

        if(options.update_imgs)
        {
            // 只更新单空间素材
            let currentRoomEntities :TRoomEntity[] = LayoutAI_Configs.Configs.renderPerformanceMode==="SingleRoom"? ([currentRoomEntity]):(container._room_entities);
            currentRoomEntities.forEach((room) => {
                room._view_cameras.forEach((view) => {
                    viewsToUpdate.push(view);
                });
            });
            // 并行执行所有更新操作
            const updatePromises = viewsToUpdate.map(async (view) => {
                if(!view._perspective_img.src || LayoutAI_Configs.Configs.renderPerformanceMode ==="SingleRoom")
                {
                    await view.updatePerspectiveViewImg(container.painter);
                }
            });
            await Promise.allSettled(updatePromises);
            scene3d.update();
            _checkAndUpdateCamera();
            LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: false, title: "" });
        }


    }


    public static getRenderTargetImg(renderer: WebGLRenderer, target: WebGLRenderTarget) {
        // 根据渲染目标的格式选择正确的数据类型
        let width = target.width;
        let height = target.height;
        let pixels;
        if (target.texture.type === FloatType) {
            pixels = new Float32Array(width * height * 4);
        } else {
            pixels = new Uint8Array(width * height * 4);
        }
        // 将 renderTarget 的纹理数据读取到 Canvas 上
        renderer.setRenderTarget(target);
        renderer.readRenderTargetPixels(target, 0, 0, width, height, pixels);
        renderer.setRenderTarget(null);
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        if (ctx) {
            let uint8ClampedArray;
            if (pixels instanceof Float32Array) {
                // 如果是 Float32Array，需要将数据转换为 Uint8ClampedArray
                uint8ClampedArray = new Uint8ClampedArray(width * height * 4);
                for (let i = 0; i < pixels.length; i++) {
                    uint8ClampedArray[i] = Math.min(255, Math.max(0, pixels[i] * 255));
                }
            } else {
                uint8ClampedArray = new Uint8ClampedArray(pixels);
            }

            // 确保 alpha 通道不透明
            // for (let i = 3; i < pixels.length; i += 4) {
            //     uint8ClampedArray[i] = 255;
            // }
            const imageData = new ImageData(uint8ClampedArray, width, height);

            // 创建临时 Canvas 来存储原始图像
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = width;
            tempCanvas.height = height;
            const tempCtx = tempCanvas.getContext('2d');
            if (tempCtx) {
                tempCtx.putImageData(imageData, 0, 0);

                // 使用 GPU 加速的变换来翻转图像
                ctx.save();
                ctx.scale(1, -1);
                ctx.translate(0, -height);
                ctx.drawImage(tempCanvas, 0, 0);
                ctx.restore();
            }
        }
        return canvas.toDataURL();

    }
}