import React, { useEffect, useState } from 'react';
import { Vector3 } from 'three';
import useStyles from './style';
import { useTranslation } from 'react-i18next';
import { Segmented } from '@svg/antd';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/models';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { ServerRenderService } from "@/Apps/LayoutAI/Services/ServerRender/ServerRenderService";
import { RenderFlag } from '@/Apps/LayoutAI/Services/ServerRender/OfflineRenderType';
import { LightRuleService } from "@/Apps/LayoutAI/Scene3D/light/rule/LightRuleService";
import { AutoLightingService } from '@/Apps/LayoutAI/Services/AutoLighting/AutoLightService';
import { SceneLightMode } from '@/Apps/LayoutAI/Scene3D/SceneMode';
import { ViewCameraService } from "@/Apps/LayoutAI/Services/ViewCamera/ViewCameraService";

interface ExitBarProps {
    currentMode: string;
    onExit?: () => void;
}
export enum drawingPicMode {
    aiDrawing = "aiDrawing",
    render = "render", // 标准渲染
    panoRender = "panoRender" // 全景渲染
}

const ExitBar: React.FC = () => {
    let store = useStore();
    const { t } = useTranslation();
    let {
        drawPictureMode,
        setDrawPictureMode,
        setIsdrawPicture
    } = store.homeStore;
    const { styles } = useStyles();
    const [isVisible, setIsVisible] = useState(false);
    const topTabItems2: { value: drawingPicMode, label: string }[] = [
        {
            label: t("标准渲染"),
            value: drawingPicMode.render
        },
        ...(store.homeStore.showPanoRender ? [{
            label: t("全景渲染"),
            value: drawingPicMode.panoRender
        }] : []),
        {
            label: t("AI绘图"),
            value: drawingPicMode.aiDrawing
        }
    ]
    const onClickExit = () => {
        setIsdrawPicture(false);
        LayoutAI_App.instance.Configs.isClickDrawPic = false;
        LightRuleService.cleanLight();  //清除灯光效果
        LayoutAI_App.instance.renderSubmitObject = {
            drawPictureMode: null,
            radioMode: 0,
            resolution: 0
        };
        setDrawPictureMode("aiDrawing");
    }
    const onChangeDrawingMode = (mode: drawingPicMode) => {
        setDrawPictureMode(mode);
        store.homeStore.setAtlasMode(mode === drawingPicMode.aiDrawing ? "aidraw" : mode)
        console.log("drawingPicMode", mode);
    }

    useEffect(() => {
        let scene3D = LayoutAI_App.instance.scene3D;
        let room = ViewCameraService.instance.getCurrentViewRoom();
        if (drawPictureMode === drawingPicMode.render) {
            // AutoLightingService.instance.updateLighting(true);
            // 写实日光2.0
            // ServerRenderService.applyLightRulerByIndex(1);
            // scene3D.setLightMode(SceneLightMode.Night);
            AutoLightingService.instance.updateLighting(true);
            LayoutAI_App.instance.scene3D.setLightGroupVisible(false, false, false);
            ServerRenderService.setRenderFlag(RenderFlag.Normal);
            // 切换视角规则
            ViewCameraService.instance.handleViewCameraChange({methods: 2});
        }
        if (drawPictureMode === drawingPicMode.panoRender) {
            scene3D.setLightMode(SceneLightMode.Night);
            AutoLightingService.instance.updateLighting(true);
            LayoutAI_App.instance.scene3D.setLightGroupVisible(false, false, false);
            ServerRenderService.setRenderFlag(RenderFlag.Panorama);
            // 切换视角规则
            ViewCameraService.instance.handleViewCameraChange({methods: 3, camera_zval: 1200});
        }
        if (drawPictureMode === drawingPicMode.aiDrawing) {
            // 清除灯光效果
            scene3D.setLightMode(SceneLightMode.Day);
            AutoLightingService.instance.cleanLighting();
            LayoutAI_App.instance.scene3D.setLightGroupVisible(false, false, false);
            // 写实日光
            LightRuleService.cleanLight();  //清除灯光效果
            // 切换视角规则
            ViewCameraService.instance.handleViewCameraChange({methods: 2});
        }
        if (room) store.homeStore.setCurrentViewCameras(room._view_cameras);
        store.homeStore.setSubmitViewList([]); // 暂时先简单处理
    }, [drawPictureMode])

    return (
        <div className={styles.exitBarContainer}>
            {
                topTabItems2.map((item, index) => (
                        <div
                            key={"Exit"+index}
                            onClick={() => onChangeDrawingMode(item.value as drawingPicMode)}
                            className={styles.topTabs + (drawPictureMode === item.value ? ` ${styles.active}` : "")}>
                            {item.label}
                            {drawPictureMode === item.value && <span style={{
                                content: '""',
                                position: 'absolute',
                                bottom: '2px',
                                left: '38px',
                                width: '20px',
                                height: '2px', // 下划线的厚度
                                backgroundColor: '#fff' // 下划线的颜色
                            }} />}
                        </div>
                ))
            }
            {/* <div>
                <button className={styles.exitButton} onClick={onClickExit}>{t('退出')}</button>
            </div> */}
        </div>
    );
};

export default observer(ExitBar);
