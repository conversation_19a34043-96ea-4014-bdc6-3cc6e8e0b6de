import React, { useCallback, useEffect, useRef, useState } from 'react';
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useTranslation } from 'react-i18next'
import { useStore } from '@/models';
import { If, Else, Then } from 'react-if';
// import EzdxfRightPanel from '../EzdxfRightPanel/ezdxfRightPanel';
import { PanelContainer } from '@svg/antd-cloud-design';
import { Divider, Radio, Table, Checkbox, Input } from '@svg/antd';
import type { ColumnsType } from '@svg/antd/es/table';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import IconFont from '@/components/IconFont/iconFont';


interface DataType {
    key: React.Key
    name: string
    size: string
    meter: string
    space: string
    area: string
}

const cabinetCompute: React.FC = () => {
    const store = useStore();
    const { t } = useTranslation()
    const { styles } = useStyles();
    const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox');
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [data, setData] = useState<DataType[]>([]);
    const [meterType, setMeterType] = useState<'延米' | '投影面积'>('延米');
    const [key, setKey] = useState<number>(0);
    const [tab, setTab] = useState<{name: string, selected: boolean}[]>([
        {
            name: '定制柜',
            selected: false
        },
        {
            name: '橱柜',
            selected: false
        },
        {
            name: '卫阳',
            selected: false
        }
    ]);

    useEffect(() => {
    }, [data])


    const EditableSizeCell: React.FC<{ id: any, data: DataType[], setData: (data: DataType[]) => void }> = ({ id, data, setData }) => {
        const [isEditing, setIsEditing] = useState(false);
        const dataItem = data.filter(item => item.key === id)
        const [sizeArr, setSizeArr] = useState(dataItem[0].size.split('*'));
        const editRef = useRef<HTMLDivElement>(null);
        
        const handleClickOutside = (event: MouseEvent) => {
            if (editRef.current && !editRef.current.contains(event.target as Node)) {
                submit()
            }
        };

        const submit = () => {
            setIsEditing(false);
            const newSize = sizeArr.join('*');
            const newArea = ((+sizeArr[0]) * (+sizeArr[2]) * 1e-6).toFixed(2)
            setData(data.map(item => item.key === id ? { ...item, size: newSize, area: `${newArea}m²` } : item));
        }

        useEffect(() => {
            if (!isEditing) return;
            document.addEventListener('mousedown', handleClickOutside);
            return () => {
                document.removeEventListener('mousedown', handleClickOutside);
            };
        }, [isEditing, sizeArr, data, id, setData]);

        const handleChange = (index: number, value: string) => {
            // 只允许数字输入
            if (/^\d*$/.test(value)) {
                const newArr = [...sizeArr];
                newArr[index] = value;
                setSizeArr(newArr);
            }
        }

        return (
            <div className={styles.editableSizeColumn} ref={editRef} onKeyUp={e => {if(e.key === 'Enter') submit()}}>
                {isEditing ? 
                <>
                    <input type="text" value={sizeArr[0]} onChange={e => handleChange(0, e.target.value)}/>
                    <span> * </span>
                    <input type="text" value={sizeArr[1]} onChange={e => handleChange(1, e.target.value)}/>
                    <span> * </span>
                    <input type="text" value={sizeArr[2]} onChange={e => handleChange(2, e.target.value)}/>
                </>
                :
                <>
                    <span>{dataItem[0].size ? dataItem[0].size : '数据缺失'}</span>
                    <IconFont
                        type="icon-edit"
                        style={{ marginLeft: 8, cursor: 'pointer' }}
                        onClick={() => setIsEditing(true)}
                    />
                </>}
            </div>
        )
    };

    const columns: ColumnsType<DataType> = [
        {
            title: '类型',
            dataIndex: 'name',
        },
        {
            title: '尺寸',
            dataIndex: 'size',
            width: 200,
            render: (text: string, record: DataType) => <EditableSizeCell id={record.key} data={data} setData={setData} />
        },
        {
            title: (
                <div 
                    style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} 
                    onClick={() => {
                        setMeterType(meterType === '延米' ? '投影面积' : '延米');
                    }
                }>
                    <If condition={tab.find(item => item.name === '定制柜')?.selected}>
                        <Then>
                            投影面积
                        </Then>
                    </If>
                    <If condition={tab.find(item => item.name === '橱柜')?.selected}>
                        <Then>
                            延米
                        </Then>
                    </If>
                    <If condition={tab.find(item => item.name === '卫阳')?.selected}>
                    <Then>
                        <IconFont type='icon-change_logo' />
                        {meterType}
                    </Then>
                    </If>
                </div>
            ),
            dataIndex: 'meter',
            render: (text: string, record: DataType) => {
                return <span>
                    {meterType === '延米' ? record.meter : record.area}
                </span>
            }
        },
        {
            title: '空间',
            dataIndex: 'space',
            hidden: tab.find(item => item.name === '橱柜')?.selected
        },
    ];

    const rowSelection = {
        onChange: (selectedRowKeys: React.Key[], selectedRows: DataType[]) => {
            setSelectedRowKeys(selectedRowKeys);
        },
        getCheckboxProps: (record: DataType) => ({
        disabled: record.name === 'Disabled User', // Column configuration not to be checked
        name: record.name
        })
    }
    const hidePanel = () => {
        store.homeStore.setShowCabinetCompute(false)
    }

    useEffect(() => {
        const selectedTab = tab.find(item => item.selected);
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        setData([]);
        if(container?._room_entities?.length == 0)
        {
            return;
        }
        if(selectedTab?.name === '橱柜')
        {
            setMeterType('延米');
            const room = container._room_entities.find(item => item.name === '厨房')?._room;
            if(room && room._furniture_list?.length > 0 )
            {
                let list = [] as DataType[];
                room._furniture_list.forEach(item => {
                    if(item.category.endsWith('柜'))
                    {
                        let rect = item.matched_rect ? item.matched_rect : item.rect;
                        list.push({
                            key: item.uuid,
                            name: item.category,
                            size: `${rect._w}*${rect._h}*${rect.rect_center_3d.z || item.height}`,
                            meter: `${(rect._w / 1000).toFixed(2)}m`,
                            space: room.aliasName,
                            area: `${(rect._w * (rect.rect_center_3d.z || item.height) / 1000000).toFixed(2)}m²`
                        })

                    }
                });
                setData(list);
            }
        }
        
        if(selectedTab?.name === '卫阳')
        {
            const rooms = container._room_entities.filter(item => {
                if(item.name.includes('卫生间') || item.name.includes('阳台'))
                {
                    return item;
                }
            });
            if(rooms && rooms.length > 0 )
            {
                let list = [] as DataType[];
                rooms.forEach((item: TRoomEntity) => {
                    if(item._room._furniture_list?.length > 0 )
                    {
                        item._room._furniture_list.forEach(item => {
                            if(item.category.endsWith('柜'))
                            {
                                let rect = item.matched_rect ? item.matched_rect : item.rect;
                                list.push({
                                    key: item.uuid,
                                    name: item.category,
                                    size: `${Math.round(rect._w)}*${Math.round(rect._h)}*${Math.round(rect.rect_center_3d.z || item.height)}`,
                                    meter: `${(rect._w / 1000).toFixed(2)}m`,
                                    space: item._room.aliasName,
                                    area: `${(rect._w * Math.round(rect.rect_center_3d.z || item.height) / 1000000).toFixed(2)}m²`
                                })
                            }
                        });
                    }
                });
                setData(list);
            }
        }

        if(selectedTab?.name === '定制柜')
        {
            setMeterType('投影面积');
            const rooms = container._room_entities.filter(item => {
                if(!item.name.includes('卫生间') && !item.name.includes('阳台') && !item.name.includes('厨房'))
                {
                    return item;
                }
            });
            if(rooms && rooms.length > 0 )
            {
                let list = [] as DataType[];
                rooms.forEach((item: TRoomEntity) => {
                    if(item._room._furniture_list?.length > 0 )
                    {
                        item._room._furniture_list.forEach(item => {
                            if(item.category.endsWith('柜'))
                            {
                                list.push({
                                    key: item.uuid,
                                    name: item.category,
                                    size: `${Math.round(item.rect._w)}*${Math.round(item.rect._h)}*${Math.round(item.rect.rect_center_3d.z || item.height)}`,
                                    meter: `${(item.rect._w / 1000).toFixed(2)}m`,
                                    space: item._room.aliasName,
                                    area: `${(item.rect._w * Math.round(item.rect.rect_center_3d.z || item.height) / 1000000).toFixed(2)}m²`
                                })
                            }
                        });     
                    }
                });
                setData(list);
            }
        }

        setSelectedRowKeys([]);
        setKey(key + 1);
    },[tab])

    useEffect(() => {
        if(store.homeStore.showCabinetCompute)
        {
            setTab(tab.map((item, subIndex) => {
                return {
                    ...item,
                    selected: subIndex === 0
                }
            })) 
        }
    },[store.homeStore.showCabinetCompute])

    return (
        <>
            <If condition={store.homeStore.showCabinetCompute}>
                <Then>
                    <PanelContainer
                    center={true}
                    height={446}
                    width={600}
                    showHeader={true}
                    showCloseInContainerbox={false}
                    title='基本算量'
                    className={styles.panelContainer}
                    onClose={hidePanel}
                    draggable={true}
                    >
                    <div style={{ padding: '0 20px 20px 20px' }} className={styles.content}>
                        {/* <div className={styles.title}>
                            柜子算量
                        </div> */}
                        <div className={styles.tab}>
                            {
                                tab.map((item, index) => {
                                    return (
                                        <div onClick={() => {
                                            setTab(tab.map((item, subIndex) => {
                                                return {
                                                    ...item,
                                                    selected: subIndex === index
                                                }
                                            }))
                                        }} className={'tabItem' + (item.selected ? ' selected' : '')} key={index}>{item.name}</div>
                                    )
                                })
                            }
                        </div>
                        <Table
                            key={key}
                            rowSelection={{
                                type: selectionType,
                                columnTitle: '',
                                selectedRowKeys: selectedRowKeys,
                                ...rowSelection
                            }}
                            columns={columns}
                            dataSource={data}
                            pagination={false}
                            scroll={{ y: 240 }}
                            className={styles.table}
                        />
                        <div className={styles.bottom}>
                            <div>
                            <Checkbox 
                                checked={selectedRowKeys.length === data.length}
                                indeterminate={selectedRowKeys.length > 0 && selectedRowKeys.length < data.length}
                                onChange={(e) => {
                                    if (e.target.checked) {
                                        // 全选
                                        setSelectedRowKeys(data.map(item => item.key));
                                    } else {
                                        // 取消全选
                                        setSelectedRowKeys([]);
                                    }
                                }}
                            >
                                全选
                            </Checkbox>
                            </div>
                            <div className={styles.bottomRight}>
                                <If condition={meterType === '延米'}>
                                    <Then>
                                        共 {data
                                        .filter(item => selectedRowKeys.includes(item.key))
                                        .reduce((acc, item) => acc + parseFloat(item.meter), 0)
                                        .toFixed(2)}m
                                    </Then>
                                    <Else>
                                        共 {data
                                        .filter(item => selectedRowKeys.includes(item.key))
                                        .reduce((acc, item) => acc + parseFloat(item.area), 0)
                                        .toFixed(2)}m²
                                    </Else>
                                </If>
                            
                            </div>
                        </div>
                    </div>
                    </PanelContainer>
                </Then>

            </If>
        </>
    );
};


export default observer(cabinetCompute);
