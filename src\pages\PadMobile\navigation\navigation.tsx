

import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import SceneModeBtns from "../sceneModeBtns/sceneModeBtns";
import { message } from "@svg/antd";
import { mini_APP, workDomainMap } from "@/config";
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { OperationManager } from "@/Apps/LayoutAI/OperationInfos/OperationManager";
import IconFont from "@/components/IconFont/iconFont";

export enum PageStates {
    Default = "Default",
    HouseSearch = "HouseSearch",
    HuaweiDemo = "HuaweiDemo"
}
export const NavigationEvent = "NavigationEvent";
const MobileNavigation: React.FC<{ state?: string }> = (props) => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const layoutContainer: TLayoutEntityContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;

    const [redo_disabled, set_redo_disabled] = useState<boolean>(true);
    const [undo_disabled, set_undo_disabled] = useState<boolean>(true);

    useEffect(() => {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.updateSlot("TopMenu_RedoableSlot", {
              ui_name: "RedoDisabled",
              target: LayoutAI_App.instance,
              callback: (t: boolean) => {
                set_redo_disabled(!t);
              }
            });
      
            LayoutAI_App.instance.updateSlot("TopMenu_UndoableSlot", {
              ui_name: "UndoDisabled",
              target: LayoutAI_App.instance,
              callback: (t: boolean) => {
                set_undo_disabled(!t);
              }
            });
        }
        LayoutAI_App.instance.connect_obj(OperationManager.signalRedoable, LayoutAI_App.instance, "TopMenu_RedoableSlot");
        LayoutAI_App.instance.connect_obj(OperationManager.signalUndoable, LayoutAI_App.instance, "TopMenu_UndoableSlot");
        LayoutAI_App.on(NavigationEvent, (state: string) => {
            if (!state) state = PageStates.Default;
        });
    }, []);
    return (
        <div className={styles.navigation}>
            {!mini_APP && 
            <div className={styles.backBtn} onClick={() => {
                if (layoutContainer._room_entities.length == 0) {
                    window.location.href = workDomainMap;
                } else {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                    message.loading(t('保存中...'));
                    setTimeout(() => {
                        message.destroy();
                        window.location.href = workDomainMap;
                    }, 1500);
                }

            }}>
                <IconFont 
                    type="icon-zhuye"
                    style={{
                    fontSize: '18px',
                    marginRight: '4px'}} 
                />
                {/* 主页 */}
            </div>} 
            <SceneModeBtns></SceneModeBtns>
            {/* <div>
                <IconFont
                    type="icon-chexiao"
                    style={{
                        fontSize: '18px',
                        marginRight: '10px',
                        color: undo_disabled ? '#ccc': '#282828'
                    }}
                    onClick={() => {
                        LayoutAI_App.RunCommand(LayoutAI_Commands.Undo)
                    }}
                ></IconFont>
                <IconFont
                    type="icon-huifu"
                    style={{
                        fontSize: '18px',
                        color: redo_disabled ? '#ccc': '#282828'
                    }}
                    onClick={() => {
                        LayoutAI_App.RunCommand(LayoutAI_Commands.Redo)
                    }}
                ></IconFont>
            </div> */}
        </div>
    )

};

export default observer(MobileNavigation);
