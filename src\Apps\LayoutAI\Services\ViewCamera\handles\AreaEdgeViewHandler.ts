import { BaseViewHandler } from "./BaseViewHandler";
import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { IType2UITypeDict } from "@layoutai/basic_data";
import { ZEdge, ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";


/**
 * 在分区中心生成视角
 * 朝向：另一个分区的相反视角
 */
export class AreaEdgeViewHandler extends BaseViewHandler{
    /**
     * 静态方法，用于避免循环依赖
     */
    static handle(ruler: ViewCameraRuler, roomEntity: TRoomEntity, options: IViewCameraGenerateOptions, areaEntitys: TSubSpaceAreaEntity[]): TViewCameraEntity[] {
        const instance = new AreaEdgeViewHandler();
        return instance.handle(ruler, roomEntity, options, areaEntitys);
    }
    
    handle(ruler: ViewCameraRuler, roomEntity: TRoomEntity, options: IViewCameraGenerateOptions, areaEntitys: TSubSpaceAreaEntity[]): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            if (ruler.condition?.spaceArea && areaEntitys) {
                let targetArea: TSubSpaceAreaEntity = null;
                let nearArea: TSubSpaceAreaEntity = null;
                areaEntitys.forEach(area => {
                    if (ruler.condition.spaceArea === IType2UITypeDict[area.space_area_type]) {
                        targetArea = area;
                    } else {
                        nearArea = area;
                    }
                })
                if (!targetArea || !nearArea) return [];

                // 根据最远距离判断
                let targetEdge: ZEdge = null;
                let maxDistance = -1;
                targetArea.rect.edges.forEach(edge => {
                    const distance = edge.center.distanceTo(nearArea.rect.rect_center);
                    if (distance > maxDistance) {
                        maxDistance = distance;
                        targetEdge = edge;
                    }
                });
                if (targetEdge) {
                    const center = roomEntity._main_rect.rect_center.clone();
                    const direction = targetEdge.center.clone().sub(center);
                    entities.push(this.createAreaEdgeView(ruler, options, center, direction, roomEntity));
                }
            }
        }
        return entities;
    }

    private createAreaEdgeView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        pos: Vector3,
        direction: Vector3,
        roomEntity: TRoomEntity
    ): TViewCameraEntity {
        let rect = new ZRect(500, 500);
        rect.nor = direction.normalize();
        rect.rect_center_3d = this.getViewCameraPosByPoint(ruler, pos, rect.nor);
        let name = ruler.name + "-侧方";
        let viewEntity = this.createViewCameraEntity(
            ruler,
            options,
            rect,
            [],
            roomEntity,
            name
        );
        return viewEntity;
    }
 }