import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  return {
    enterPage: css`
      position:fixed;
      left:0;
      bottom:0;
      height: calc(var(--vh, 1vh) * 100);
      width:100%;
      z-index: 999;
      background: #fff;
      .slide-enter {
        transform: translateX(-100%);
        opacity: 0;
      }

      .slide-enter-active {
        transform: translateX(0);
        opacity: 1;
        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
      }

      .slide-exit {
        transform: translateX(0);
        opacity: 1;
      }

      .slide-exit-active {
        transform: translateX(100%);
        opacity: 0;
        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
      }



      .slide-reverse-enter {
        transform: translateX(100%);
        opacity: 0;
      }

      .slide-reverse-enter-active {
        transform: translateX(0);
        opacity: 1;
        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
      }

      .slide-reverse-exit {
        transform: translateX(0);
        opacity: 1;
      }

      .slide-reverse-exit-active {
        transform: translateX(-100%);
        opacity: 0;
        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
      }

      .upload_hx
      {
        position: fixed;
        right: 25px;
        bottom: 60%;
      }
    `,
    selectHx: css`
      padding: 0px 10px;
      height: 100%;
      /* .right_btns
      {
        position: fixed;
        right: 25px;
        top: 25px;
      } */

    `,
    hxRoot: css`
      width: 100%;
      height: 100%;
    `,
    selectDemand: css`
      padding: 0px 40px;
      height: calc(var(--vh, 1vh) * 100 - 170px);
      margin-top: 16px;
      overflow-y: scroll;
      ::-webkit-scrollbar
      {
        display: none;
      }
      .demandLabel
        {
          font-weight: 600;
          font-size: 16px;
          color: #000;
          margin-bottom: 8px;
          margin-top: 20px;
        }
      .demandItem
      {
       
        .tabRoot
        {
          display: flex;
          flex-wrap: wrap;
        }
      }
      .demandtab
      {
        display: flex;
        width: 100px;
        height: 32px;
        padding: 4px 16px;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        background: #F2F3F4;
        margin-right: 12px;
        margin-bottom: 12px;
      }
      .selected
      {
        border-radius: 6px;
        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
        box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08);
        color: #fff;
      }
    `,
    styleTitle: css`
      display: flex;
      align-items: center;
      justify-content: space-between;
    `,
    demandRoot: css`
      width: 100%;
      height: 100%;
    `,
    hxHeader: css`
      display: flex;
      padding: 40px 40px 0px 40px;
      justify-content: space-between;
      align-items: center;

      .title{
        font-size: 24px;
        font-weight: 600;
        display: flex;
        align-items: center;
        .back {
          width: 28px;
          height: 28px;
          border-radius: 6px;
          background: #E9EBEB;
          padding: 4px;
          margin-right: 8px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
        }
      }

      .mySchemeButton{
        display: flex;
        align-items:center;
        font-weight: 600;
        font-size: 11px;
      }
      .myAtlasButton{
        display: flex;
        align-items:center;
        font-weight: 600;
        font-size: 11px;
      }
    `,
    bottom: css`
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 88px;
      background: #fff;
      display: flex;
      align-items: center;
      padding: 20px 60px;
      justify-content: space-between;
      .ant-btn
      {
        width: 160px;
        height: 48px;
        border-radius: 24px;
      }
      .rotate
      {
        font-size: 16px;
        color: #5B5E60;
      }
    `,
    container_listInfo: css`
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      margin-top: 20px;
    `,
    container_list: css`
      width: calc(20% - 10px);
      height: auto;
      padding: 2px;
      box-sizing: border-box;
      position: relative;
      margin-right: 10px;
      @media (max-width: 800px) {
        width: calc(33.33% - 10px);
      }
      img{
        width: 100%;
        aspect-ratio: 5/3;
      }
    `,
    textInfo: css`
      padding: 0 5px;
      `,
    container_title: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: medium;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      margin-top: 5px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      padding: 0 10px;
      .ant-rate
      {
        color: #FFAA00;
        font-size: 16px !important;
        .ant-rate-star:not(:last-child)
        {
          margin-inline-end: 3px;
        }
      }
    `,
    container_desc: css`
      color: #6C7175;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
      display: flex;
      margin-top: 5px;
    `,
  }

});
