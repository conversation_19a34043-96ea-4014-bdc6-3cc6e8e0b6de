import { openApiRequest } from '@/utils';

class MultiSchemeListService {

    private _GetSchemeListByPagePath: string = "/api/njvr/layoutSchemeMult/listByPage";
    private _InsertSchemePath: string = "/api/njvr/layoutSchemeMult/insert";
    private _DeleteSchemePath: string = "/api/njvr/layoutSchemeMult/delete";
    private _GetLayoutSchemeMultDetailPath: string = "/api/njvr/layoutSchemeMult/get";

    //获取布局多方案关联详情
    // relateId: 关联ID
    public getLayoutSchemeMultDetail = async (relateId: string): Promise<LayoutSchemeMultResult | null> => {
        const response = await openApiRequest({
            method: 'post',
            url: this._GetLayoutSchemeMultDetailPath,
            data: {
                id: relateId,
            },
        });
        if (response && response.success) {
            console.log(response)
            return response.result;
        }
        console.warn('getLayoutSchemeMultDetail response is null');
        return null;
    }


    // 获取方案列表
    public loadSchemeList = async (originSchemeId: string): Promise<LayoutSchemeMultResult[]> => {

        const response = await openApiRequest({
            method: 'post',
            url: this._GetSchemeListByPagePath,
            data: {
                originSchemeId: originSchemeId,
                page: 1,
                pageSize: 10,
                orderBy: "create_date desc"
            },
            timeout: 60000,
        }).catch((e: any) => {
            console.error(e);
        });

        if (response && response.success) {
            if (response?.result?.result?.length > 0) {
                return response.result.result;
            }
            return [];
        }
        console.warn('getSchemeListByPage response is null');
        return [];
    }

    // 添加方案
    public insertScheme = async (originSchemeId: string, refSchemeId: string, refSchemeName: string): Promise<boolean> => {
        // 先保存当前方案
        const response = await openApiRequest({
            method: 'post',
            url: this._InsertSchemePath,
            data: {
                originSchemeId: originSchemeId,
                refSchemeId: refSchemeId,
                refSchemeName: refSchemeName,
            },
        });

        if (response && response.success) {
            return true;
        }
        return false;
    }

    // 删除方案
    public deleteScheme = async (schemeId: string): Promise<boolean> => {
        const response = await openApiRequest({
            method: 'post',
            url: this._DeleteSchemePath,
            data: { id: schemeId },
        });

        if (response && response.success) {
            return true;
        }
        return false;
    }
}

export const multiSchemeListService = new MultiSchemeListService();

// 布局多方案关联结果
export interface LayoutSchemeMultResult {

    id: string;
    // 封面图url
    imageUrl: string;
    // 原始布局方案ID
    originSchemeId: string;
    // 关联方案ID
    refSchemeId: string;
    // 关联方案名称
    refSchemeName: string;

}