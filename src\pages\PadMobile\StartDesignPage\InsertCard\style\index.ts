import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
    cardContainer: css`
        display: flex;
        flex-wrap: wrap;
        height: auto;
        margin: 0 -20px;
    `,
    card: css`
        margin: 10px 20px !important;
        border-radius: 8px;
        overflow: hidden;

        @media screen and (min-width: 700px) {
        width: calc((100% / 2) - 40px) !important;
        }
        @media screen and (min-width: 900px) {
        width: calc((100% / 3) - 40px) !important;
        }
        @media screen and (min-width: 1150px) {
        width: calc((100% / 4) - 40px) !important;
        }
        /* @media screen and (min-width: 1500px) {
        width: calc((100% / 5) - 40px) !important;
        }
        @media screen and (min-width: 2000px) {
        width: calc((100% / 6) - 40px) !important;
        } */
    `,
    content: css`
        display: flex;
        width: auto;
        img {
        width: 48px;
        margin-top: -3px;
        }
    `,
    right: css`
        margin-left: 8px;
        justify-content: center;
    `,
    title: css`
        color: #282828;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 14px;
        line-height: 1.57;
        letter-spacing: 0px;
        text-align: left;
    `,
    desc: css`
        color: #959598;
        font-family: PingFang SC;
        font-weight: normal;
        font-size: 12px;
        line-height: 1.67;
        letter-spacing: 0px;
        text-align: left;
    `,
    insertBtn: css`
        border: none;
        padding: 0 8px;
        margin-left: 30px;
        height: 32px;
        background-color: #f4f5f5;
        color: #282828;
        font-family: PingFang SC;
        font-weight: normal;
        font-size: 14px;
        line-height: 1.57;
        letter-spacing: 0px;
        text-align: left;
        &:hover {
        color: #282828 !important;
        background-color: #f4f5f5 !important;
        }
    `,
    rotatedIcon: css`
        transform: rotate(45deg);
    `,
}));