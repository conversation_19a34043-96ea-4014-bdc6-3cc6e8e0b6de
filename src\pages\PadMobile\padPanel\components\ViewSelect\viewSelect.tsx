import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useStore } from "@/models";
import { useNavigate } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import Icon from "@/components/Icon/icon";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import { pageViewImage } from "@/services/padMobile";
import { If,Then } from "react-if";
import IconFont from "@/components/IconFont/iconFont";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { Image } from "@svg/antd";
import { MathUtils } from "@/Apps/LayoutAI/Utils/math_utils";

const ViewSelect: React.FC = () => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const navigate = useNavigate();
    const store = useStore();
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const [showSectedInfo, setShowSectedInfo] = useState<boolean>(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const selectInfoRef = useRef<HTMLDivElement>(null)
    const itemInfoRef = useRef<HTMLDivElement[]>([])
    const [pageRenderList, setPageRenderList] = useState<any[]>([]);
    const [showRenderList, setShowRenderList] = useState<boolean>(false);
    const switchView = (index: number) => {
        setShowRenderList(false);
        setCurrentIndex(index);
        // 只在用户点击时触发 bindViewEntity
        let scene3D = (LayoutAI_App.instance as TAppManagerBase).scene3D as Scene3D;
        scene3D.active_controls.bindViewEntity(store.homeStore.currentViewCameras[index]);
        scene3D.update();
    };

    const getPageViewList = async (item: TViewCameraEntity) => {
        setPageRenderList([]);
        // setShowRenderList(!showRenderList);
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        const params = {
            pageIndex: 1,
            pageSize: 500,
            viewAngleId: item.ukey,
            roomId: item._room_entity.uidN,
            layoutSchemeId: container._layout_scheme_id,
        }
        const res = await pageViewImage(params);
        if(res.success)
        {
            setPageRenderList(res.result.result);
        }
    }
    // 添加外部点击检测
    useEffect(() => {
        function handleOutsideInteraction(event: MouseEvent | TouchEvent) {
            const target = (event as TouchEvent).touches 
                ? (event as TouchEvent).target 
                : (event as MouseEvent).target;
            const dom = window.document.querySelector('.scene3D')
            if (dom && dom.contains(target as Node)) {
                setShowSectedInfo(false);
            }
        }

        if (showSectedInfo) {
            document.addEventListener('mousedown', handleOutsideInteraction);
            document.addEventListener('touchstart', handleOutsideInteraction, { passive: true });
        }

        return () => {
            document.removeEventListener('mousedown', handleOutsideInteraction);
            document.removeEventListener('touchstart', handleOutsideInteraction);
        };
    }, [showSectedInfo]);

    // 选中对象自动滚动到可视区域
    useEffect(() => {
        if(selectInfoRef.current && itemInfoRef.current[currentIndex]){
            const container = selectInfoRef.current
            const item = itemInfoRef.current[currentIndex]

            const containerRect = container.getBoundingClientRect()
            const itemRect = item.getBoundingClientRect()

            const scrollLeft = itemRect.left - containerRect.left + container.scrollLeft
            const centerOffset = (containerRect.width - itemRect.width) / 2
            container.scrollTo({
                left: scrollLeft - centerOffset,
                behavior: 'smooth'
            })
        }
    }, [currentIndex, showSectedInfo])

    // 切换视角列表的时候更新索引
    useEffect(() => {
        setCurrentIndex(0);
    }, [store.homeStore.drawPictureMode])


    useEffect(() => {
        if(!store.homeStore?.currentViewCameraEntity) return;
        if(!MathUtils._checkViewCameraValid(store.homeStore?.currentViewCameraEntity, LayoutAI_App?.instance?.scene3D))
        {
            setCurrentIndex(-1);
        }
    }, [LayoutAI_App?.instance?.scene3D?.camera?.position, store.homeStore?.currentViewCameraEntity, store.homeStore?.guideMapCurrentRoom?.uuid]);

    return (
        <div className={styles.shijiaoBarContainer} ref={containerRef}>
            <div 
                onClick={() => {
                    setShowSectedInfo(true);
                    if (currentIndex > 0) {
                        switchView(currentIndex - 1);
                    } else {
                        switchView(store.homeStore.currentViewCameras.length - 1);
                    }
                }} 
                className={styles.leftArrow}
            >
                <Icon style={{ color: '#bcb9b9', fontSize: 14 }} iconClass='iconfill_left' />
            </div>

            {/* 底部按钮 */}
            <div
                className={styles.shijiaoBar}
                onClick={() => {
                    setShowSectedInfo(!showSectedInfo);
                }}
            >
                视角{currentIndex + 1}
            </div>

            <div 
                onClick={() => {
                    setShowSectedInfo(true);
                    if (currentIndex < store.homeStore.currentViewCameras.length - 1) {
                        switchView(currentIndex + 1);
                    } else {
                        switchView(0);
                    }
                }} 
                className={styles.rightArrow}
            >
                <Icon style={{ color: '#bcb9b9', fontSize: 14 }} iconClass='iconfill_right' />
            </div>

            {pageRenderList?.length > 0 && showSectedInfo && showRenderList &&(
                <div className="pageRenderList"                 
                    onWheel={(e) => {
                        e.preventDefault();
                        const container = e.currentTarget;
                        const scrollSpeed = 30;
                        container.scrollLeft += e.deltaY * scrollSpeed;
                    }}
                >
                    {pageRenderList.map((item, index) => (
                        <div key={index} className="pageRenderItem">
                            <Image src={item.imageUrl} width={122} height={91} />
                        </div>
                    ))}
                </div>
            )}

            <div
                className='selectInfo'
                ref={selectInfoRef}
                onWheel={(e) => {
                    e.preventDefault();
                    const container = e.currentTarget;
                    const scrollSpeed = 30;
                    container.scrollLeft += e.deltaY * scrollSpeed;
                }}
                style={{ height: showSectedInfo ? 'auto' : 0 }}
            >

                {store.homeStore.currentViewCameras.map((item, index) => {
                    return (
                        <div
                            className='shijiaoItem'
                            key={index}
                            ref={el => itemInfoRef.current[index] = el as HTMLDivElement}
                            style={{ border: index === currentIndex ? '2px solid #147FFA' : '2px solid #fff' }}
                            onClick={() => {
                                switchView(index);
                                setPageRenderList([])
                                getPageViewList(item)
                            }}
                        >
                            <If condition={index === currentIndex && pageRenderList?.length > 0}>
                                <Then>
                                    <div
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            setShowRenderList(!showRenderList)
                                            // getPageViewList(item)
                                        }} 
                                        className="tag"
                                    >
                                        效果图{showRenderList ? <IconFont type="icon-a-fangxiangshang"/> :<IconFont type="icon-a-fangxiangxia"/>}
                                    </div>
                                </Then>
                            </If>
                            <img src={item._perspective_img.src} alt="" />
                            <div className='title'>视角{index + 1}</div>
                        </div>
                    )
                })}
            </div>
        </div>
    );
};


export default observer(ViewSelect);