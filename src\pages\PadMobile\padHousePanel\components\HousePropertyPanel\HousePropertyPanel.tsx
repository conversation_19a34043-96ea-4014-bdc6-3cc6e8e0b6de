import { observer } from "mobx-react-lite";
import { useStore } from "@/models";
import useStyles from './HousePropertyPanel.style';
import EntityProperties from "@/components/RightPropertyPanel/widget/EntityProperties/EntityProperties";
import { AI_PolyTargetType } from "@/Apps/LayoutAI/Layout/IRoomInterface";

interface HousePropertyPanelProps {
    isCollapse: boolean;
    onCollapseChange: (collapse: boolean) => void;
}

/**
 * 户型属性面板组件 - 处理门窗、结构件、墙体和空间的实体属性
 */
const HousePropertyPanel: React.FC<HousePropertyPanelProps> = ({ isCollapse, onCollapseChange }) => {
    const store = useStore();
    const { styles } = useStyles();
    const isLeftPanelOpen = !isCollapse;

    const selectedEntity = store.homeStore.selectEntity;

    const getEntityTitle = (entity: any) => {
        if (!entity) return '';
        const type = entity.type;
        if (type === AI_PolyTargetType.Door) return '门信息';
        if (type === AI_PolyTargetType.Window) return '窗信息';
        if (type === AI_PolyTargetType.StructureEntity) return '结构件信息';
        if (type === AI_PolyTargetType.Wall) return '墙体信息';
        if (type === AI_PolyTargetType.RoomArea) return '空间信息';
        return '';
    };

    const isValidEntity = (entity: any) => {
        if (!entity) return false;
        const type = entity.type;
        return type === AI_PolyTargetType.Door || 
               type === AI_PolyTargetType.Window || 
               type === AI_PolyTargetType.StructureEntity ||
               type === AI_PolyTargetType.Wall ||
               type === AI_PolyTargetType.RoomArea;
    };

    return (
        <>
            <div className={styles.leftPanelRoot + " leftPanelRoot " + (!isLeftPanelOpen ? "panel_hide" : "")} >
                {isLeftPanelOpen && (
                    <>
                        <div className="closeBtn iconfont iconclose1" onClick={() => onCollapseChange(true)}></div>
                        <div className={styles.propertyContainer}>
                            {selectedEntity && isValidEntity(selectedEntity) && (
                                <>
                                    <div className={styles.propertyTitle}>{getEntityTitle(selectedEntity)}</div>
                                    <EntityProperties Entity={selectedEntity} />
                                </>
                            )}
                        </div>
                    </>
                )}
            </div>
            <div
                className={styles.collapseBtn + (!isLeftPanelOpen ? " panel_hide iconfont iconfill_right" : " iconfont iconfill_left")}
                onClick={() => onCollapseChange(!isCollapse)}
            />
        </>
    );
};

export default observer(HousePropertyPanel); 