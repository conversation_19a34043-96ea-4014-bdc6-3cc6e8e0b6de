import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    leftPanelRoot: css`
      position: fixed;
      background: #fff;
      z-index: 10;
      transition: all 0.3s ease-in-out;
      .closeBtn {
        display: none;
        position: absolute;
        right: 6px;
        top: 6px;
        font-size: 20px;
        width: 60px;
        height: 24px;
        text-align: right;
      }
      &.panel_hide {
        box-shadow: 0px 0px 0px 0px #00000000;
      }
      @media screen and (orientation: landscape) {
        position: fixed;
        left: 12px;
        top: 52px;
        bottom: 12px;
        right: auto;
        height: auto;
        padding-left: 0 !important;
        max-height: calc(var(--vh, 1vh) * 100);
        max-width: 224px;
        width: 224px;
        border-radius: 8px;
        box-shadow: 0px 0px 16px 10px #0000000A;
        &.panel_hide {
          transform: translateX(calc(-100% - 12px));
        }
      }
      @media screen and (orientation: portrait) {
        position: fixed;
        left: 0;
        bottom: 0px;
        right: 0;
        width: auto;
        height: 340px;
        max-width: auto;
        max-height: 340px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 8px 8px 0px 0px;
        box-shadow: 0px 0px 16px 10px #0000000A;
        &.panel_hide {
          transform: translateY(100%);
        }
        .closeBtn {
          display: block;
        }
      }
    `,
    collapseBtn: css`
      display: none;
      width: 20px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      background-color: #fff;
      border-radius: 0px 6px 6px 0px;
      box-shadow: 0px -16px 16px 0px #00000005;
      cursor: pointer;
      transition: all 0.3s ease-in-out;

      @media screen and (orientation: landscape) {
        display: block;
        position: fixed;
        left: 235px;
        top: calc(50% - 48px);
        z-index: 9;
      }
      @media screen and (orientation: portrait) {
        position: fixed;
        bottom: 120px;
        left: 0px;
        z-index: 999;
      }
      &.panel_hide {
        left: 0px;
        display: block;
      }
    `,
    propertyContainer: css`
      padding: 12px;
      height: calc(100% - 40px);
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 4px;
      }
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      &::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 2px;
      }
    `,
    propertyTitle: css`
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e8e8e8;
    `
  };
}); 