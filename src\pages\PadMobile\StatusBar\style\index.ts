import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      display: flex;
      justify-content: start;
      align-items: center;
      border-radius: 50px;
      background: #fff;
      border: 1px solid #FFFFFF;
      box-shadow: 0px 6px 20px 0px #0000001E;
      position: fixed;
      left: 50%;
      z-index: 9;
      transform: translateX(-50%);
      overflow:hidden;
      top: 60px; 
      left: 50%; 
       flex-direction: row; 
      .topLine{
        width: 40px;
        height: 2px;
        background: #E0E1E1;
        position: absolute;
        top: 5px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 10px;
      }
    `,
    show: css`
      opacity: 1;
    `,
    hide: css`
      opacity: 0;
    `,
    btnInfo:css`
      width: 56px;
      height: 60px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #282828;
      font-size: 12px;
      padding: 4px 0px;
      margin: 0 8px;
      position: relative;
      @media screen and (max-width: 450px) { // 手机宽度
        width: 30px !important;
        height: 44px !important;
        margin: 0 4px !important;
        font-size: 10px !important;
        .label {
          width:20px;
        }
      }
      @media screen and (orientation: landscape) {
        width: 48px;
        font-size: 11px;
      }
      div{
        margin: 0px 0px;
      }
      .divider
      {
        position: absolute;
        left: -5px;
        height: 100%;
        border-left: 1px #E0E1E1 solid;
        width: 1px;
      }
    `
  }
});
