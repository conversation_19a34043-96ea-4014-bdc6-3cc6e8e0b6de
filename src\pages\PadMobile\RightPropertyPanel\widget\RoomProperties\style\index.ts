import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      position: relative;
    `,
    rootInfo: css`
      position: relative;
      overflow-y: scroll;
    `,
    rootItem: css`
      display: flex;
      justify-content: space-between;
      padding: 5px 12px;
      
    `,
    line: css`
      width: 85%;
      margin: 15px auto;
      height: 1px;
      background: #00000014;
    `,
    clearBtn: css`
      position: absolute;
      bottom: -10px;
      height: 0px;
      width: 100%;
      text-align: center;
      padding-top: 15px;
      button{
        width: 100px;
        height: 24px;
        background: #F2F3F5;
        border-radius: 2px;
        border: 1px solid #F2F3F5; 
        transition: all .3s;
        cursor: pointer;
      }
      button:hover{
        background: #DDDFE4;
        border: 1px solid #DDDFE4; 
      }
    `
  }
});
