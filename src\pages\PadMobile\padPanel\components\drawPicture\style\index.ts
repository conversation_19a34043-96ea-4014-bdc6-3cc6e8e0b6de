import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    box: css`
      position: absolute;
      top: 0;
      left: 0;
      z-index: 8;
      width: 100%;
      height: 100%;
      border-width: 40px 45px;
      border-color: #000000;
      border-style: solid;
      opacity: 0.35;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      pointer-events: none;
    `,
    contentBox: css`
      width: 100%;
      height: 100%;
      background-color: transparent; /* 中间区域透明 */
      border: 3px solid #ffffff; /* 添加白色边框 */
      border-radius: 3px;
      display: flex;
      justify-content: center;
      align-items: center;
      pointer-events: none;
    `,
  }
});