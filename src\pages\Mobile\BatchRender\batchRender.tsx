import React, { useEffect, useState, useMemo, useCallback } from 'react';
import useStyles from './style';
import { observer } from 'mobx-react-lite';
import { useTranslation } from 'react-i18next';
import { useStore } from '@/models';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { PanelContainer } from '@svg/antd-cloud-design';
import { Checkbox, CheckboxChangeEvent, message, Button, Select } from '@svg/antd'
import { CheckCard } from '@ant-design/pro-components';
import { TViewCameraEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity';
import { ServerRenderService } from '@/Apps/LayoutAI/Services/ServerRender/ServerRenderService';
import { ResolutionTag, ResolutionConfig } from '@/Apps/LayoutAI/Services/ServerRender/OfflineRenderType';
import { EventName } from "@/Apps/EventSystem";
import { insertViewEffects } from '@/services/padMobile';
import { FigureViewControls } from '@/Apps/LayoutAI/Scene3D/controls/FigureViewControls';
import { PerspectiveCamera } from 'three';
import { SdkService } from '@/services/SdkService';


interface DataType {
    name: string;
    area: string;
    viewList: TViewCameraEntity[];
}

/**
 * @description: 批量渲染
 */
const BatchRender: React.FC = () => {
    const store = useStore();
    const { t } = useTranslation();
    const { styles } = useStyles();
    const [data, setData] = useState<DataType[]>([]);
    const [checkRecommend, setCheckRecommend] = useState(true);
    const [checkCustom, setCheckCustom] = useState(false);
    const [recommendViewList, setRecommendViewList] = useState<TViewCameraEntity[]>([])
    const [customViewList, setCustomViewList] = useState<TViewCameraEntity[]>([]);
    const [selectViewList, setSelectViewList] = useState<TViewCameraEntity[]>([]);
    const [ resolution, setResolution] = useState<ResolutionTag>(ResolutionTag.FHD);
    // const [disabled, setDisabled] = useState(false);

    const resolutionOptions = [
        // { label: '高清', value: ResolutionTag.FHD },
        { label: '超清', value: ResolutionTag.HD },
    ]

    useEffect(() => {
        if (!store.homeStore.showBatchRender) return;
        // 获取房间视角数据
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;

        const getData = container._room_entities
            // 过滤掉没有视角的房间
            .filter(room => room._view_cameras.length > 0)
            // 获取房间及其视角信息
            .map(room => ({
                name: room.aliasName,
                area: room._area.toFixed(2),
                viewList: room._view_cameras
            }))
            .sort((a, b) => {
                const order = ['客餐厅', '卧室', '厨房', '卫生间', '书房', '阳台'];
                const parseRoom = (name: string) => {
                    const match = name.match(/^(.*?)(\d*)$/);
                    return {
                        type: match?.[1] || name,
                        number: match?.[2] ? parseInt(match[2], 10) : 0
                    };
                };
                const roomA = parseRoom(a.name);
                const roomB = parseRoom(b.name);
                const orderA = order.indexOf(roomA.type);
                const orderB = order.indexOf(roomB.type);
                // 如果房间类型不在预定义顺序中，按名称排序
                if (orderA === -1 || orderB === -1) {
                    return orderA === orderB 
                        ? a.name.localeCompare(b.name)
                        : orderA === -1 ? 1 : -1;
                }
                // 先按房间类型排序，相同类型则按序号排序
                return orderA === orderB 
                    ? roomA.number - roomB.number
                    : orderA - orderB;
            });
        setData(getData);

        // 根据不同房间筛选推荐视角
        const recommendviewList = getRecommendViewList(getData);
        setRecommendViewList(recommendviewList);

        // 提交视角的回显
        const submitViewList = store.homeStore.submitViewList;
        const viewList = submitViewList.length > 0 ? submitViewList : recommendviewList;
        console.log("submitViewList", submitViewList);
        setSelectViewList(viewList);
        if (submitViewList.length > 0) {
            setCustomViewList(viewList);
            setCheckCustom(true);
            setCheckRecommend(false);
        }
    }, [store.homeStore.showBatchRender]);

    useEffect(() => {
        if (store.homeStore.showBatchRender) return;
        setData([]);
        setCustomViewList([]);
        setSelectViewList([]);
        setCheckRecommend(true);
        setCheckCustom(false);
    }, [store.homeStore.showBatchRender])

    useEffect(() => {
        if (customViewList.length > 12) {
            setCustomViewList(selectViewList);
            message.warning('批量选择图片数量不可超过12张，请减少选择后再提交哦');
            // setDisabled(true);
        } else {
            // setDisabled(false);
        }
    }, [customViewList])

    /**
     * @description 获取推荐列表
    */
    const getRecommendViewList = (data: DataType[]): TViewCameraEntity[] => {
        const viewList: TViewCameraEntity[] = [];
        data.forEach(item => {
            const name = item.name.replace(/\d+$/, '');
            switch (name) {
                case '客餐厅':
                    viewList.push(
                        ...item.viewList.filter(view =>
                            (view._target.includes('沙发') && view._target.length === 1)||
                            (view._target.includes('餐桌') && view._target.length === 1)
                        )
                    );
                    break;
                case '卧室':
                    if (Number(item.area) >= 6) {
                        viewList.push(...creatViewStrategy(item.viewList, '床'));
                    } 
                    break;
                case '厨房':
                    viewList.push(...creatViewStrategy(item.viewList, '炉灶'));
                    break;
                case '卫生间':
                    if (Number(item.area) >= 3) {
                        viewList.push(...creatViewStrategy(item.viewList, '浴室柜'));
                    }
                    break;
                // case '书房':
                //     viewList.push(...creatViewStrategy(item.viewList, '书桌'));
                //     break;
                // case '阳台':
                //     viewList.push(...creatViewStrategy(item.viewList, '阳台柜'));
                //     break;
            }
        })
        return viewList;
    }

    const creatViewStrategy = (viewList: TViewCameraEntity[], target: string) => {
        let views: TViewCameraEntity[] = [];
        const frontView = viewList.find(view => view._target.includes(target) && view._dir === '正面');
        if (frontView) views.push(frontView);
        if (views.length === 0 ) {
            const sideView = viewList.find(view => view._target.includes(target) && view._dir === '侧面');
            if (sideView) views.push(sideView);
        }
        return views;
    }

    const hidePanel = () => {
        store.homeStore.setShowBatchRender(false)
    }

    const clickSubmit = async () => {
        if (!selectViewList.length) {
            message.error('请至少选择一个视角');
            return;
        }
        LayoutAI_App.emit(EventName.ApplySeriesSample, {
            seriesOpening: true,
            title: "批量提交视角中...",
            timeout: 6000 * selectViewList.length
        });
        
        let isNeedCheck = true;
        try {
            for (const view of selectViewList) {
                const layoutContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;
                if (!layoutContainer._layout_scheme_id) {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                    await new Promise<void>((resolve, reject) => {
                        const interval = setInterval(() => {
                            if (layoutContainer._layout_scheme_id) {
                                clearInterval(interval);
                                resolve();
                            }
                        }, 500);
                    });
                }

                // 处理渲染：只有第一次提交的时候要判断余额
                let res = isNeedCheck ? await handleBatchRender(view, selectViewList.length) : await handleBatchRender(view);
                if (res.success) {
                    await insertRenderImageToView(res.queueId, view);
                } else {
                    throw new Error(res.msg);
                }
                isNeedCheck = false;
            }
        } catch (error) {
            LayoutAI_App.emit(EventName.ApplySeriesSample, {
                seriesOpening: false,
                title: ""
            });
            return;
        }

        LayoutAI_App.emit(EventName.ApplySeriesSample, {
            seriesOpening: false,
            title: ""
        });
        store.homeStore.setSubmitViewList(selectViewList);
        message.success('提交批量渲染成功！');
        SdkService.batchRender();
    }

    /**
     * @description 处理渲染
    */
    const handleBatchRender = async (view: TViewCameraEntity, renderNum?: number): Promise<{ success: boolean, msg: string, queueId: string, schemeId: string }> => {
        let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;
        const controls = scene3d.active_controls as FigureViewControls;
        // 获取当前视角
        const prevView = controls.target_view_entity;
        // 创建临时相机进行渲染
        const camera = (scene3d.active_controls.camera as PerspectiveCamera).clone();
        camera.near = view.near;
        camera.far = view.far;
        camera.fov = view.fov;
        TViewCameraEntity.updateCameraByRect(camera, view.rect);
        // 隐藏
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        controls.updateContainerMeshesVisibilityByViewCamera(container, view);
        // 设置分辨率
        if (store.userStore.isHaiEr) {
            ServerRenderService.resolutionTag = resolution;
        } else {
            ServerRenderService.resolutionTag = ResolutionTag.HD;
        }
        // 设置渲染出图比例  1=>4/3  2->16:9  3->3:4  4->9:16 5->原图
        const roomName = view._room_entity.roomname;
        let radioMode = store.homeStore.aspectRatioMode;
        if (roomName === "客餐厅") {
            radioMode = 2;
        } else if (roomName === "卧室" || roomName === "书房" || roomName === "厨房") {
            radioMode = 1;
        } else if (roomName === "阳台" || roomName === "卫生间") {
            radioMode = 4;
        }
        LayoutAI_App.instance.renderSubmitObject = {
            drawPictureMode: store.homeStore.drawPictureMode, 
            radioMode: radioMode,
            resolution: 5
        };
        // 离线渲染
        const res = await ServerRenderService.commitOfflineRender(camera, renderNum);
        // 恢复
        if (prevView && controls._checkViewCameraValid()) {
            controls.updateContainerMeshesVisibilityByViewCamera(container, prevView);
        } else {
            controls.updateContainerMeshesVisibilityByViewCamera(container, null);
        }
    
        console.log(res);
        if(!res.success){
            if(res.msg === '批量渲染失败，渲染券余额不足'){
                let tip = '提交渲染失败，余额不足';
                if (res.couponData) {
                    tip += `(剩余${res.couponData.surplusUseCount}张)`;
                }
                message.error(
                    <>
                        {tip}，请点击
                        <a href="https://mall.3vjia.com/" target="_blank" rel="noopener noreferrer">
                            购买
                        </a>
                    </>,
                    5
                )
                return res;
            }
            message.error(
                <>
                    {res.msg}
                </>,
                3
            )
        }
        return res;
    };

    /**
     * @description 关联渲染图到视角中
    */
    const insertRenderImageToView = async (queueId: string, view: TViewCameraEntity) => {
        let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        if (view.rect.rect_center_3d.distanceTo(scene3d.camera.position) < 50) {
            await insertViewEffects({
                layoutSchemeId: container._layout_scheme_id,
                queueFlag: 0,
                queueId: queueId,
                roomId: view._room_entity.uidN,
                roomName: view._room_entity.roomname,
                viewAngleData: view.exportData(),
                viewAngleId: view.ukey,
                viewAngleName: view.name,
            });
        }
    }

    const handleResolutionChange = (value: string) => {
        console.log(value);
        setResolution(value as ResolutionTag);
    }

    return (
        <>
            {store.homeStore.showBatchRender && (
                <PanelContainer
                    center={true}
                    height={550}
                    width={700}
                    className={styles.panelContainer}
                    showHeader={true}
                    title={
                        <>
                            <span className={styles.title}>{t('批量渲染')}</span>
                            <span className={styles.subtitle}>
                                {store.userStore.isHaiEr ? 
                                    <>
                                        {t('默认使用')}
                                        <Select
                                            defaultValue="超清"
                                            style={{ width: 70 }}
                                            onChange={handleResolutionChange}
                                            options={resolutionOptions}
                                        />
                                        {t('渲染')}
                                    </>
                                : t('默认使用高清')}
                            </span>
                        </>
                    }
                    onClose={hidePanel}
                    draggable={false}
                    mask={true}
                    zIndex={999}
                >
                    <>
                        <div className={styles.tab}>
                            <Checkbox
                                onChange={(e: CheckboxChangeEvent) => {
                                    setSelectViewList(e.target.checked ? recommendViewList : []);
                                    setCheckRecommend(e.target.checked);
                                    setCheckCustom(false);
                                }}
                                checked={checkRecommend}
                            >
                                {t('推荐视角')}
                            </Checkbox>
                            <Checkbox
                                onChange={(e: CheckboxChangeEvent) => {
                                    setSelectViewList(e.target.checked ? customViewList : []);
                                    setCheckCustom(e.target.checked);
                                    setCheckRecommend(false);
                                }}
                                checked={checkCustom}
                            >
                                {t('自由视角')}
                            </Checkbox>
                        </div>
                        <div className={styles.content}>
                            <CheckCard.Group
                                multiple
                                onChange={(value) => {
                                    const list = value as any[];
                                    setCustomViewList(list);
                                    if (list.length <= 12) {
                                        setSelectViewList(list);
                                        setCheckCustom(true);
                                        setCheckRecommend(false);
                                    }
                                }}
                                value={selectViewList as any[]}
                            >
                                {data.map(item =>
                                    <>
                                        <div className={styles.contentTitle}>{item.name}</div>
                                        <div className={styles.contentList}>
                                            {item.viewList.map(view => {
                                                const index = selectViewList.findIndex(selectedView => 
                                                    selectedView == view
                                                );
                                                const dataOrder = index !== -1 ? index + 1 : 0;
                                                return (
                                                    <CheckCard 
                                                        className={styles.contentItem} 
                                                        cover={<img loading='lazy' src={view._perspective_img.src} />}
                                                        value={view}
                                                    >
                                                        {dataOrder > 0 && (
                                                            <div className={styles.contentItemNum}>
                                                                {dataOrder}
                                                            </div>
                                                        )}
                                                    </CheckCard>
                                                );
                                            })}
                                        </div>
                                    </>
                                )}
                            </CheckCard.Group>
                        </div>
                        <div className={styles.submitBatchRender}>
                            {/* <Button className={styles.submitBtn} onClick={clickSubmit} disabled={disabled}> */}
                            <Button className={styles.submitBtn} onClick={clickSubmit}>
                                {t('提交渲染')}
                            </Button>
                        </div>
                    </>
                </PanelContainer>
            )}
        </>
    );
};

export default observer(BatchRender);
