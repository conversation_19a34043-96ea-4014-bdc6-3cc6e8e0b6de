{"转角沙发-FC_矩形茶几-FL_休闲椅": {"group_code": "转角沙发-FC_矩形茶几-FL_休闲椅", "serial_id": "", "group_space_category": "沙发组合区II", "seed_figure_group": {"main_figure": {"category": "沙发", "public_category": "沙发", "sub_category": "转角沙发", "priority": 0, "deform_method": null, "z_level": 0, "shape": "L形", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2800, "depth": 1600, "height": 820}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "(0.1*md+0.5*td+300)", "dir_offset_dist": "-(1./6. * ml)", "allowed_overlap": true, "check_conditions": ["(tl < ml * 0.5)"]}, "_dir_side": 1}, "figure": {"category": "茶几", "public_category": "茶几", "sub_category": "矩形茶几", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1000, "depth": 800, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 45, "nor_offset_dist": "(0.5*td + 200)", "dir_offset_dist": "-(0.5*ml+0.3*tl+100)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 680, "depth": 740, "height": 1500}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "沙发组合区II", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3580, "y": 2060, "z": 0}}, "group_code": "转角沙发-FC_矩形茶几-FL_休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "转角沙发-FC_圆形茶几-FL_休闲椅": {"group_code": "转角沙发-FC_圆形茶几-FL_休闲椅", "serial_id": "", "group_space_category": "沙发组合区II", "seed_figure_group": {"main_figure": {"category": "沙发", "public_category": "沙发", "sub_category": "转角沙发", "priority": 0, "deform_method": null, "z_level": 0, "shape": "L形", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2800, "depth": 1600, "height": 820}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "(0.1*md+0.5*td+300)", "dir_offset_dist": "-200", "allowed_overlap": true, "check_conditions": ["(tl < ml * 0.4)"]}, "_dir_side": 1}, "figure": {"category": "茶几", "public_category": "茶几", "sub_category": "圆形茶几", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 900, "depth": 690, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 45, "nor_offset_dist": "(0.5*td + 450)", "dir_offset_dist": "-(0.5*ml-(ml>2701?0.5*tl:0.25*tl)+200)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 780, "depth": 740, "height": 820}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "沙发组合区II", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3147.401153701776, "y": 2157.401153701776, "z": 0}}, "group_code": "转角沙发-FC_圆形茶几-FL_休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "直排沙发-FC_矩形茶几-FL_休闲椅": {"group_code": "直排沙发-FC_矩形茶几-FL_休闲椅", "serial_id": "", "group_space_category": "沙发组合区", "seed_figure_group": {"main_figure": {"category": "沙发", "public_category": "沙发", "sub_category": "直排沙发", "priority": 0, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2100, "depth": 900, "height": 820}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "(0.5*md+0.5*td+350)", "dir_offset_dist": "200", "allowed_overlap": false, "check_conditions": ["(tl<ml*0.5 && td > 601 )"]}, "_dir_side": 1}, "figure": {"category": "茶几", "public_category": "茶几", "sub_category": "矩形茶几", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1000, "depth": 800, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 90, "nor_offset_dist": "(0.5*md+0.5*tl + 200)", "dir_offset_dist": "-(0.2*ml+0.5*td+200)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 680, "depth": 740, "height": 820}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "沙发组合区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2410, "y": 2050, "z": 0}}, "group_code": "直排沙发-FC_矩形茶几-FL_休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅": {"group_code": "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅", "serial_id": "", "group_space_category": "沙发组合区", "seed_figure_group": {"main_figure": {"category": "沙发", "public_category": "沙发", "sub_category": "直排沙发", "priority": 0, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2100, "depth": 900, "height": 820}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "(0.5*md+0.5*td+350)", "dir_offset_dist": "0", "allowed_overlap": false, "check_conditions": ["(tl < ml * 0.5 && (ml >= 2100 -0.1 && ml < 2700) && (td >= 750))"]}, "_dir_side": 1}, "figure": {"category": "茶几", "public_category": "茶几", "sub_category": "矩形茶几", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1000, "depth": 800, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 90, "nor_offset_dist": "(0.5*md+0.5*tl+300)", "dir_offset_dist": "-(ml * 0.25 + td*0.5 + 200)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "脚踏", "public_category": "脚踏", "sub_category": "脚踏", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 800, "depth": 600, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": -45, "nor_offset_dist": "(0.5 * md + (td / 0.736) * 0.5 + 100)", "dir_offset_dist": "(ml * 0.25 + (td / 0.736) * 0.5 + 10)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 680, "depth": 740, "height": 820}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "沙发组合区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2864.7632059467965, "y": 2050, "z": 0}}, "group_code": "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅-FL_圆形边几": {"group_code": "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅-FL_圆形边几", "serial_id": "", "group_space_category": "沙发组合区", "seed_figure_group": {"main_figure": {"category": "沙发", "public_category": "沙发", "sub_category": "直排沙发", "priority": 0, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 3100, "depth": 900, "height": 820}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "(0.5*md+0.5*td+401)", "dir_offset_dist": "0", "allowed_overlap": false, "check_conditions": ["(tl < ml * 0.6 && ml>1800)"]}, "_dir_side": 1}, "figure": {"category": "茶几", "public_category": "茶几", "sub_category": "矩形茶几", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1402, "depth": 772, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 90, "nor_offset_dist": "(0.5 * md + 401 + 386)", "dir_offset_dist": "-(ml * 0.5)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "脚踏", "public_category": "脚踏", "sub_category": "脚踏", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 691, "depth": 518, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": -45, "nor_offset_dist": "(0.5 * md + 401 + 386)", "dir_offset_dist": "(ml * 0.25 + (td / 0.736) * 0.5 + 100)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 680, "depth": 740, "height": 820}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": -90, "nor_offset_dist": "5", "dir_offset_dist": "-(ml * 0.5 + tl * 0.5 + 100)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "边几", "public_category": "边几", "sub_category": "圆形边几", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 500, "height": 450}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "沙发组合区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 4004.7632059467965, "y": 2189.0458146424485, "z": 0}}, "group_code": "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅-FL_圆形边几"}, "seed_candidate_list": [], "generated_list": []}, "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅-FL_圆形边几-FR_落地灯": {"group_code": "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅-FL_圆形边几-FR_落地灯", "serial_id": "", "group_space_category": "沙发组合区", "seed_figure_group": {"main_figure": {"category": "沙发", "public_category": "沙发", "sub_category": "直排沙发", "priority": 0, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 3100, "depth": 900, "height": 820}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "(0.5*md+0.5*td+401)", "dir_offset_dist": "0", "allowed_overlap": false, "check_conditions": ["(tl < ml * 0.6 && ml>1800)"]}, "_dir_side": 1}, "figure": {"category": "茶几", "public_category": "茶几", "sub_category": "矩形茶几", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1402, "depth": 772, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 90, "nor_offset_dist": "(0.5 * md + 401 + 386)", "dir_offset_dist": "-(ml * 0.5)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "脚踏", "public_category": "脚踏", "sub_category": "脚踏", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 691, "depth": 518, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": -45, "nor_offset_dist": "(0.5 * md + 401 + 386)", "dir_offset_dist": "(ml * 0.25 + (td / 0.736) * 0.5 + 150)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 680, "depth": 740, "height": 820}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": -90, "nor_offset_dist": "5", "dir_offset_dist": "-(ml * 0.5 + tl * 0.5 + 100)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "边几", "public_category": "边几", "sub_category": "圆形边几", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 500, "height": 450}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": -90, "nor_offset_dist": "5", "dir_offset_dist": "(ml * 0.5 + tl * 0.5 + 100)", "allowed_overlap": false}, "_dir_side": 1}, "figure": {"category": "落地灯", "public_category": "落地灯", "sub_category": "落地灯", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 500, "depth": 500, "height": 1300}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "沙发组合区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 4275, "y": 2189.0458146424485, "z": 0}}, "group_code": "直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅-FL_圆形边几-FR_落地灯"}, "seed_candidate_list": [], "generated_list": []}, "直排沙发-FC_矩形茶几": {"group_code": "直排沙发-FC_矩形茶几", "serial_id": "", "group_space_category": "沙发组合区", "seed_figure_group": {"main_figure": {"category": "沙发", "public_category": "沙发", "sub_category": "直排沙发", "priority": 0, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2100, "depth": 900, "height": 820}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "(0.5*md+0.5*td+350)", "dir_offset_dist": "0", "allowed_overlap": false, "check_conditions": ["(tl < ml * 0.8 && ml < 2000)"]}, "_dir_side": 1}, "figure": {"category": "茶几", "public_category": "茶几", "sub_category": "矩形茶几", "priority": 1, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1000, "depth": 800, "height": 450}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "沙发组合区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2100, "y": 2050, "z": 0}}, "group_code": "直排沙发-FC_矩形茶几"}, "seed_candidate_list": [], "generated_list": []}, "直排沙发": {"group_code": "直排沙发", "serial_id": "", "group_space_category": "沙发组合区", "seed_figure_group": {"main_figure": {"category": "沙发", "public_category": "沙发", "sub_category": "直排沙发", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 784.105, "model_flag": "1", "params": {"length": 2902.611, "depth": 940.284, "height": 784.114}, "_ex_prop": {"material_id": "159988709", "t_group_name": "沙发组合区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "沙发组合区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2902.611, "y": 940.284, "z": 0}}, "group_code": "直排沙发"}, "seed_candidate_list": [], "generated_list": []}, "餐桌-FC4_餐椅": {"group_code": "餐桌-FC4_餐椅", "serial_id": "", "group_space_category": "餐桌区", "seed_figure_group": {"main_figure": {"category": "餐桌", "public_category": "餐桌", "sub_category": "餐桌", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1600, "depth": 900, "height": 790}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"array_method": "rect_around", "array_num": 4, "array_side_padding": 100, "array_depth_offset": 150, "check_conditions": ["(ml > 1100 && md < 1300 && ml < 1800)"]}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 450, "height": 760}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "餐桌区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1600, "y": 1650, "z": 0}}, "group_code": "餐桌-FC4_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "餐桌-FC2_餐椅": {"group_code": "餐桌-FC2_餐椅", "serial_id": "", "group_space_category": "餐桌区II", "seed_figure_group": {"main_figure": {"category": "餐桌", "public_category": "餐桌", "sub_category": "餐桌", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1400, "depth": 700, "height": 790}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"array_method": "rect_around", "array_num": 2, "array_side_padding": 100, "array_depth_offset": 100, "check_conditions": ["(ml > 1200 && md < 950)"]}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 450, "height": 760}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "餐桌区II", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1400, "y": 1025, "z": 0}}, "group_code": "餐桌-FC2_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "餐桌-FC6_餐椅": {"group_code": "餐桌-FC6_餐椅", "serial_id": "", "group_space_category": "餐桌区", "seed_figure_group": {"main_figure": {"category": "餐桌", "public_category": "餐桌", "sub_category": "餐桌", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2200, "depth": 900, "height": 790}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"array_method": "rect_around", "array_num": 6, "array_side_padding": 100, "array_depth_offset": 150, "array_sideable": false, "check_conditions": ["(ml > 1750 && md < 1350)"]}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 450, "height": 780}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "餐桌区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2200, "y": 1650, "z": 0}}, "group_code": "餐桌-FC6_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "餐桌-FC6T_餐椅": {"group_code": "餐桌-FC6T_餐椅", "serial_id": "", "group_space_category": "餐桌区", "seed_figure_group": {"main_figure": {"category": "餐桌", "public_category": "餐桌", "sub_category": "餐桌", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1800, "depth": 1000, "height": 790}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"array_method": "rect_around", "array_num": 6, "array_sideable": true, "array_side_padding": 300, "check_conditions": ["(ml > 1750 && md > 1750 && ml < 1850 && md < 1600)"]}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 450, "height": 760}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "餐桌区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2450, "y": 1870, "z": 0}}, "group_code": "餐桌-FC6T_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "圆形餐桌-FC5_餐椅": {"group_code": "圆形餐桌-FC5_餐椅", "serial_id": "", "group_space_category": "餐桌区II", "seed_figure_group": {"main_figure": {"category": "餐桌", "public_category": "餐桌", "sub_category": "圆形餐桌", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1400, "depth": 1400, "height": 780}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"array_method": "ellipse_around", "array_num": 5, "array_side_padding": 180, "array_sideable": false, "check_conditions": ["(md > 1300)"]}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 450, "height": 780}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "餐桌区II", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1886.9981663095055, "y": 1984.1072890813243, "z": 0}}, "group_code": "圆形餐桌-FC5_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "圆形餐桌-FC4T_餐椅": {"group_code": "圆形餐桌-FC4T_餐椅", "serial_id": "", "group_space_category": "餐桌区II", "seed_figure_group": {"main_figure": {"category": "餐桌", "public_category": "餐桌", "sub_category": "圆形餐桌", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1400, "depth": 1400, "height": 780}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"array_method": "ellipse_around", "array_num": 4, "array_side_padding": 250, "array_sideable": true, "check_conditions": ["(ml > 800 && ml < 1050 && md > 1350 && ml < 1450)"]}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 550, "height": 780}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "餐桌区II", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1732.411613907041, "y": 1732.4116139070418, "z": 0}}, "group_code": "圆形餐桌-FC4T_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "餐桌-FC4T_餐椅": {"group_code": "餐桌-FC4T_餐椅", "serial_id": "", "group_space_category": "餐桌区II", "seed_figure_group": {"main_figure": {"category": "餐桌", "public_category": "餐桌", "sub_category": "餐桌", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 1600, "depth": 1000, "height": 790}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"array_method": "rect_around", "array_num": 4, "array_side_padding": 100, "array_sideable": true, "check_conditions": ["(ml > 1200 && md > 800 && ml < 1800)"]}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 450, "height": 760}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "餐桌区II", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2370, "y": 1870, "z": 0}}, "group_code": "餐桌-FC4T_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "岛台-FC6_餐椅": {"group_code": "岛台-FC6_餐椅", "serial_id": "", "group_space_category": "岛台区", "seed_figure_group": {"main_figure": {"category": "岛台", "public_category": "岛台", "sub_category": "岛台", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2700, "depth": 750, "height": 1000}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"array_method": "rect_around", "array_num": 6, "array_side_padding": 200, "array_sideable": false, "check_conditions": ["(ml > 2600)"]}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 550, "height": 780}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "岛台区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2700, "y": 1720, "z": 0}}, "group_code": "岛台-FC6_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "岛台-FR_餐桌-FR_餐椅-FR_餐椅-FR_餐椅": {"group_code": "岛台-FR_餐桌-FR_餐椅-FR_餐椅-FR_餐椅", "serial_id": "", "group_space_category": "岛台区", "seed_figure_group": {"main_figure": {"category": "岛台", "public_category": "岛台", "sub_category": "岛台", "priority": 2, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2700, "depth": 750, "height": 1000}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 270, "allowed_overlap": true, "nor_offset_dist": "(0.5*tl + 0.5*md)", "dir_offset_dist": "(0.5*ml - 0.5*td)", "check_conditions": ["(ml > 2700)"]}, "_dir_side": 1}, "figure": {"category": "餐桌", "public_category": "餐桌", "sub_category": "餐桌", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 2000, "depth": 600, "height": 780}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 90, "allowed_overlap": true, "nor_offset_dist": "(md)", "dir_offset_dist": "(0.25*ml)", "check_conditions": []}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 450, "depth": 550, "height": 780}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 90, "allowed_overlap": true, "nor_offset_dist": "(md+td)", "dir_offset_dist": "(0.25*ml)", "check_conditions": []}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 550, "depth": 600, "height": 780}, "_ex_prop": {}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 90, "allowed_overlap": true, "nor_offset_dist": "(md+2*td)", "dir_offset_dist": "(0.25*ml)", "check_conditions": []}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 2, "deform_method": null, "z_level": 0, "shape": "Rect", "min_z": 0, "max_z": 0, "model_flag": "1", "params": {"length": 550, "depth": 600, "height": 780}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "岛台区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2700, "y": 2750, "z": 0}}, "group_code": "岛台-FR_餐桌-FR_餐椅-FR_餐椅-FR_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "床-FL_床头柜-FR_床头柜": {"group_code": "床-FL_床头柜-FR_床头柜", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": 1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3000, "y": 2000, "z": 0}}, "group_code": "床-FL_床头柜-FR_床头柜"}, "seed_candidate_list": [], "generated_list": []}, "床-FR_床头柜": {"group_code": "床-FR_床头柜", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1600, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 479.13, "model_flag": "1", "params": {"length": 494.39, "depth": 398.24, "height": 471.03}, "_ex_prop": {"material_id": "178025319", "t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2194.39, "y": 2000, "z": 0}}, "group_code": "床-FR_床头柜"}, "seed_candidate_list": [], "generated_list": []}, "床-FL_床头柜": {"group_code": "床-FL_床头柜", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1600, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 450, "depth": 400, "height": 500}, "_ex_prop": {"material_id": "147868430", "t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2150, "y": 2000, "z": 0}}, "group_code": "床-FL_床头柜"}, "seed_candidate_list": [], "generated_list": []}, "床": {"group_code": "床", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 961, "model_flag": "1", "params": {"length": 1300, "depth": 1900, "height": 961}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1300, "y": 1900, "z": 0}}, "group_code": "床"}, "seed_candidate_list": [], "generated_list": []}, "床-FL_床头柜-FR_床头柜-FC_床尾凳": {"group_code": "床-FL_床头柜-FR_床头柜-FC_床尾凳", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": 1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "length_func": "(ml)", "nor_offset_dist": "(md*0.5 +0.5*td + 100)", "dir_offset_dist": "0", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床尾凳", "public_category": "床尾凳", "sub_category": "床尾凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 1900, "depth": 400, "height": 300}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3000, "y": 2500, "z": 0}}, "group_code": "床-FL_床头柜-FR_床头柜-FC_床尾凳"}, "seed_candidate_list": [], "generated_list": []}, "床-FR_床头柜-FC_床尾凳": {"group_code": "床-FR_床头柜-FC_床尾凳", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1600, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 479.13, "model_flag": "1", "params": {"length": 494.39, "depth": 398.24, "height": 471.03}, "_ex_prop": {"material_id": "178025319", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "length_func": "(ml)", "nor_offset_dist": "(md*0.5 +0.5*td + 100)", "dir_offset_dist": "0", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床尾凳", "public_category": "床尾凳", "sub_category": "床尾凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 1600, "depth": 400, "height": 300}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2194.39, "y": 2500, "z": 0}}, "group_code": "床-FR_床头柜-FC_床尾凳"}, "seed_candidate_list": [], "generated_list": []}, "床-FL_床头柜-FC_床尾凳": {"group_code": "床-FL_床头柜-FC_床尾凳", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1600, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 450, "depth": 400, "height": 500}, "_ex_prop": {"material_id": "147868430", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "length_func": "(ml)", "nor_offset_dist": "(md*0.5 +0.5*td + 100)", "dir_offset_dist": "0", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床尾凳", "public_category": "床尾凳", "sub_category": "床尾凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 1600, "depth": 400, "height": 300}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2150, "y": 2500, "z": 0}}, "group_code": "床-FL_床头柜-FC_床尾凳"}, "seed_candidate_list": [], "generated_list": []}, "床-FC_床尾凳": {"group_code": "床-FC_床尾凳", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 961, "model_flag": "1", "params": {"length": 1300, "depth": 1900, "height": 961}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "length_func": "(ml)", "nor_offset_dist": "(md*0.5 +0.5*td + 100)", "dir_offset_dist": "0", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床尾凳", "public_category": "床尾凳", "sub_category": "床尾凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 1300, "depth": 400, "height": 300}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1300, "y": 2400, "z": 0}}, "group_code": "床-FC_床尾凳"}, "seed_candidate_list": [], "generated_list": []}, "床-FL_梳妆凳-FL_梳妆台-FR_床头柜": {"group_code": "床-FL_梳妆凳-FL_梳妆台-FR_床头柜", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10 + 200)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100 + 200)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "梳妆凳", "public_category": "梳妆凳", "sub_category": "梳妆凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 450, "depth": 500, "height": 850}, "_ex_prop": {"material_id": "139261367", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "梳妆台", "public_category": "梳妆台", "sub_category": "梳妆台", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 900, "depth": 400, "height": 750}, "_ex_prop": {"material_id": "124498145", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": 1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3450, "y": 2000, "z": 0}}, "group_code": "床-FL_梳妆凳-FL_梳妆台-FR_床头柜"}, "seed_candidate_list": [], "generated_list": []}, "床-FR_梳妆凳-FR_梳妆台-FL_床头柜": {"group_code": "床-FR_梳妆凳-FR_梳妆台-FL_床头柜", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10 + 200)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100 + 200)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "梳妆凳", "public_category": "梳妆凳", "sub_category": "梳妆凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 450, "depth": 500, "height": 850}, "_ex_prop": {"material_id": "139261367", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "梳妆台", "public_category": "梳妆台", "sub_category": "梳妆台", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 900, "depth": 400, "height": 750}, "_ex_prop": {"material_id": "124498145", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": -1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3450, "y": 2000, "z": 0}}, "group_code": "床-FR_梳妆凳-FR_梳妆台-FL_床头柜"}, "seed_candidate_list": [], "generated_list": []}, "床-FL_梳妆凳-FL_梳妆台-FR_床头柜-FC_床尾凳": {"group_code": "床-FL_梳妆凳-FL_梳妆台-FR_床头柜-FC_床尾凳", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10 + 200)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100 + 200)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "梳妆凳", "public_category": "梳妆凳", "sub_category": "梳妆凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 450, "depth": 500, "height": 850}, "_ex_prop": {"material_id": "139261367", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "梳妆台", "public_category": "梳妆台", "sub_category": "梳妆台", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 900, "depth": 400, "height": 750}, "_ex_prop": {"material_id": "124498145", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": 1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "length_func": "(ml)", "nor_offset_dist": "(md*0.5 +0.5*td + 100)", "dir_offset_dist": "0", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床尾凳", "public_category": "床尾凳", "sub_category": "床尾凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 1900, "depth": 400, "height": 300}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3450, "y": 2500, "z": 0}}, "group_code": "床-FL_梳妆凳-FL_梳妆台-FR_床头柜-FC_床尾凳"}, "seed_candidate_list": [], "generated_list": []}, "床-FR_梳妆凳-FR_梳妆台-FL_床头柜-FC_床尾凳": {"group_code": "床-FR_梳妆凳-FR_梳妆台-FL_床头柜-FC_床尾凳", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10 + 200)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100 + 200)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "梳妆凳", "public_category": "梳妆凳", "sub_category": "梳妆凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 450, "depth": 500, "height": 850}, "_ex_prop": {"material_id": "139261367", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "梳妆台", "public_category": "梳妆台", "sub_category": "梳妆台", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 900, "depth": 400, "height": 750}, "_ex_prop": {"material_id": "124498145", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": -1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "length_func": "(ml)", "nor_offset_dist": "(md*0.5 +0.5*td + 100)", "dir_offset_dist": "0", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床尾凳", "public_category": "床尾凳", "sub_category": "床尾凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 1900, "depth": 400, "height": 300}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3450, "y": 2500, "z": 0}}, "group_code": "床-FR_梳妆凳-FR_梳妆台-FL_床头柜-FC_床尾凳"}, "seed_candidate_list": [], "generated_list": []}, "床-FL_书椅-FL_书桌-FR_床头柜": {"group_code": "床-FL_书椅-FL_书桌-FR_床头柜", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10 + 300)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100 + 200)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "书椅", "public_category": "书椅", "sub_category": "书椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 600, "depth": 600, "height": 850}, "_ex_prop": {"material_id": "145406105", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 900, "depth": 400, "height": 750}, "_ex_prop": {"material_id": "113246518", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": 1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3450, "y": 2000, "z": 0}}, "group_code": "床-FL_书椅-FL_书桌-FR_床头柜"}, "seed_candidate_list": [], "generated_list": []}, "床-FR_书椅-FR_书桌-FL_床头柜": {"group_code": "床-FR_书椅-FR_书桌-FL_床头柜", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10 + 300)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100 + 200)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "书椅", "public_category": "书椅", "sub_category": "书椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 600, "depth": 600, "height": 850}, "_ex_prop": {"material_id": "145406105", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 900, "depth": 400, "height": 750}, "_ex_prop": {"material_id": "113246518", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": -1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3450, "y": 2000, "z": 0}}, "group_code": "床-FR_书椅-FR_书桌-FL_床头柜"}, "seed_candidate_list": [], "generated_list": []}, "床-FL_书椅-FL_书桌-FR_床头柜-FC_床尾凳": {"group_code": "床-FL_书椅-FL_书桌-FR_床头柜-FC_床尾凳", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10 + 300)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100 + 200)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "书椅", "public_category": "书椅", "sub_category": "书椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 600, "depth": 600, "height": 850}, "_ex_prop": {"material_id": "145406105", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": -1}, "figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 900, "depth": 400, "height": 750}, "_ex_prop": {"material_id": "113246518", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": 1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "length_func": "(ml)", "nor_offset_dist": "(md*0.5 +0.5*td + 100)", "dir_offset_dist": "0", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床尾凳", "public_category": "床尾凳", "sub_category": "床尾凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 1900, "depth": 400, "height": 300}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3450, "y": 2500, "z": 0}}, "group_code": "床-FL_书椅-FL_书桌-FR_床头柜-FC_床尾凳"}, "seed_candidate_list": [], "generated_list": []}, "床-FR_书椅-FR_书桌-FL_床头柜-FC_床尾凳": {"group_code": "床-FR_书椅-FR_书桌-FL_床头柜-FC_床尾凳", "serial_id": "", "group_space_category": "卧床区", "seed_figure_group": {"main_figure": {"category": "床", "public_category": "床", "sub_category": "床", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 858, "model_flag": "1", "params": {"length": 1900, "depth": 2000, "height": 850}, "_ex_prop": {"material_id": "168541239", "t_group_name": "卧床区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10 + 300)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100 + 200)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "书椅", "public_category": "书椅", "sub_category": "书椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 600, "depth": 600, "height": 850}, "_ex_prop": {"material_id": "145406105", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 900, "depth": 400, "height": 750}, "_ex_prop": {"material_id": "113246518", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md+0.5*td + 10)", "dir_offset_dist": "dir_side * (0.5*ml+0.5*tl + 100)"}, "_dir_side": -1}, "figure": {"category": "床头柜", "public_category": "床头柜", "sub_category": "床头柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 450, "depth": 500.2099999999991, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "length_func": "(ml)", "nor_offset_dist": "(md*0.5 +0.5*td + 100)", "dir_offset_dist": "0", "check_conditions": ["(ml > 1250)"]}, "_dir_side": 1}, "figure": {"category": "床尾凳", "public_category": "床尾凳", "sub_category": "床尾凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 508.1, "model_flag": "1", "params": {"length": 1900, "depth": 400, "height": 300}, "_ex_prop": {"t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "卧床区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 3450, "y": 2500, "z": 0}}, "group_code": "床-FR_书椅-FR_书桌-FL_床头柜-FC_床尾凳"}, "seed_candidate_list": [], "generated_list": []}, "衣柜": {"group_code": "衣柜", "serial_id": "", "group_space_category": "卧室-衣柜区", "seed_figure_group": {"main_figure": {"category": "衣柜", "public_category": "衣柜", "sub_category": "衣柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400, "model_flag": "1", "params": {"length": 600, "depth": 600, "height": 2400}, "_ex_prop": {"t_group_name": "卧室-衣柜区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "卧室-衣柜区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 600, "z": 0}}, "group_code": "衣柜"}, "seed_candidate_list": [], "generated_list": []}, "电视柜": {"group_code": "电视柜", "serial_id": "", "group_space_category": "电视柜区", "seed_figure_group": {"main_figure": {"category": "电视柜", "public_category": "电视柜", "sub_category": "电视柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400, "model_flag": "1", "params": {"length": 1800, "depth": 400, "height": 2400}, "_ex_prop": {"t_group_name": "电视柜区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "电视柜区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1800, "y": 400, "z": 0}}, "group_code": "电视柜"}, "seed_candidate_list": [], "generated_list": []}, "电视": {"group_code": "电视", "serial_id": "", "group_space_category": "挂墙电视区", "seed_figure_group": {"main_figure": {"category": "电视", "public_category": "电视", "sub_category": "电视", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 700, "max_z": 1657.58, "model_flag": "1", "params": {"length": 1450, "depth": 54.05, "height": 957.58}, "_ex_prop": {"material_id": "124285781", "t_group_name": "挂墙电视区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "挂墙电视区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1450, "y": 54.05, "z": 0}}, "group_code": "电视"}, "seed_candidate_list": [], "generated_list": []}, "玄关柜": {"group_code": "玄关柜", "serial_id": "", "group_space_category": "玄关柜区", "seed_figure_group": {"main_figure": {"category": "玄关柜", "public_category": "玄关柜", "sub_category": "玄关柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400, "model_flag": "1", "params": {"length": 600, "depth": 350, "height": 2400}, "_ex_prop": {"t_group_name": "玄关柜区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "玄关柜区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 350, "z": 0}}, "group_code": "玄关柜"}, "seed_candidate_list": [], "generated_list": []}, "梳妆台": {"group_code": "梳妆台", "serial_id": "", "group_space_category": "梳妆台区", "seed_figure_group": {"main_figure": {"category": "梳妆台", "public_category": "梳妆台", "sub_category": "梳妆台", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.001, "model_flag": "1", "params": {"length": 1050, "depth": 600, "height": 850}, "_ex_prop": {"material_id": "124498145", "t_group_name": "梳妆台区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "梳妆台区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1050, "y": 600, "z": 0}}, "group_code": "梳妆台"}, "seed_candidate_list": [], "generated_list": []}, "梳妆台-FC_书椅": {"group_code": "梳妆台-FC_书椅", "serial_id": "", "group_space_category": "梳妆台区", "seed_figure_group": {"main_figure": {"category": "梳妆台", "public_category": "梳妆台", "sub_category": "梳妆台", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 765, "model_flag": "1", "params": {"length": 1800, "depth": 500, "height": 765}, "_ex_prop": {"t_group_name": "梳妆台区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(0.5*md+0.2*td + 0)", "dir_offset_dist": "0"}, "_dir_side": 1}, "figure": {"category": "书椅", "public_category": "书椅", "sub_category": "书椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850, "model_flag": "1", "params": {"length": 525, "depth": 595, "height": 850}, "_ex_prop": {"material_id": "145406105", "t_group_name": "梳妆台区"}, "mirror": 0}}], "group_space_category": "梳妆台区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1800, "y": 916.5, "z": 0}}, "group_code": "梳妆台-FC_书椅"}, "seed_candidate_list": [], "generated_list": []}, "梳妆台-FC_餐椅": {"group_code": "梳妆台-FC_餐椅", "serial_id": "", "group_space_category": "梳妆台区", "seed_figure_group": {"main_figure": {"category": "梳妆台", "public_category": "梳妆台", "sub_category": "梳妆台", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 750, "model_flag": "1", "params": {"length": 1200, "depth": 350, "height": 750}, "_ex_prop": {"t_group_name": "梳妆台区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(0.5*md+0.2*td + 0)", "dir_offset_dist": "0"}, "_dir_side": -1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 809.331, "model_flag": "1", "params": {"length": 530.18, "depth": 577.48, "height": 809.33}, "_ex_prop": {"material_id": "138988571", "t_group_name": "梳妆台区"}, "mirror": 0}}], "group_space_category": "梳妆台区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1200, "y": 754.2360000000001, "z": 0}}, "group_code": "梳妆台-FC_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "梳妆凳": {"group_code": "梳妆凳", "serial_id": "", "group_space_category": "梳妆凳区", "seed_figure_group": {"main_figure": {"category": "梳妆凳", "public_category": "梳妆凳", "sub_category": "梳妆凳", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 550, "model_flag": "1", "params": {"length": 950, "depth": 750, "height": 550}, "_ex_prop": {"material_id": "139261367", "t_group_name": "梳妆凳区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "梳妆凳区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 950, "y": 750, "z": 0}}, "group_code": "梳妆凳"}, "seed_candidate_list": [], "generated_list": []}, "餐边柜": {"group_code": "餐边柜", "serial_id": "", "group_space_category": "餐边柜区", "seed_figure_group": {"main_figure": {"category": "餐边柜", "public_category": "餐边柜", "sub_category": "餐边柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400, "model_flag": "1", "params": {"length": 200, "depth": 200, "height": 2400}, "_ex_prop": {"material_id": "100111854", "t_group_name": "卧室-边柜区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "餐边柜区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 200, "y": 200, "z": 0}}, "group_code": "餐边柜"}, "seed_candidate_list": [], "generated_list": []}, "书桌-FC_餐椅": {"group_code": "书桌-FC_餐椅", "serial_id": "", "group_space_category": "书桌区", "seed_figure_group": {"main_figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 1201.393, "model_flag": "1", "params": {"length": 1400, "depth": 600, "height": 700}, "_ex_prop": {"material_id": "113246518", "t_group_name": "书桌区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(0.5*md + 0)", "dir_offset_dist": "0"}, "_dir_side": 1}, "figure": {"category": "餐椅", "public_category": "餐椅", "sub_category": "餐椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 845.461, "model_flag": "1", "params": {"length": 554.39, "depth": 594.82, "height": 845.46}, "_ex_prop": {"material_id": "124555838", "t_group_name": "书桌区"}, "mirror": 0}}], "group_space_category": "书桌区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1400, "y": 897.4100000000001, "z": 0}}, "group_code": "书桌-FC_餐椅"}, "seed_candidate_list": [], "generated_list": []}, "书桌": {"group_code": "书桌", "serial_id": "", "group_space_category": "书桌区", "seed_figure_group": {"main_figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 761.58, "model_flag": "1", "params": {"length": 1204.27, "depth": 511.285, "height": 761.58}, "_ex_prop": {"material_id": "31754873", "t_group_name": "书桌区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "书桌区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1204.27, "y": 511.285, "z": 0}}, "group_code": "书桌"}, "seed_candidate_list": [], "generated_list": []}, "书桌-FC_书椅": {"group_code": "书桌-FC_书椅", "serial_id": "", "group_space_category": "书桌区", "seed_figure_group": {"main_figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 700, "model_flag": "1", "params": {"length": 1400, "depth": 600, "height": 700}, "_ex_prop": {"material_id": "31713464", "t_group_name": "书桌区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(0.5*md+ 0)", "dir_offset_dist": "0"}, "_dir_side": -1}, "figure": {"category": "书椅", "public_category": "书椅", "sub_category": "书椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 786.031, "model_flag": "1", "params": {"length": 443.49, "depth": 493.28, "height": 786.04}, "_ex_prop": {"material_id": "31455644", "t_group_name": "书桌区"}, "mirror": 0}}], "group_space_category": "书桌区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1400, "y": 846.6400000000001, "z": 0}}, "group_code": "书桌-FC_书椅"}, "seed_candidate_list": [], "generated_list": []}, "书桌-FC_休闲椅": {"group_code": "书桌-FC_休闲椅", "serial_id": "", "group_space_category": "书桌区", "seed_figure_group": {"main_figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 829.84, "model_flag": "1", "params": {"length": 2437.73, "depth": 800, "height": 829.839}, "_ex_prop": {"material_id": "108745456", "t_group_name": "书桌区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(0.5*md +0.2*td+ 0)", "dir_offset_dist": "0"}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 902.43, "model_flag": "1", "params": {"length": 887.012, "depth": 737.781, "height": 902.429}, "_ex_prop": {"material_id": "132011711", "t_group_name": "书桌区"}, "mirror": 0}}], "group_space_category": "书桌区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2437.73, "y": 1316.4467, "z": 0}}, "group_code": "书桌-FC_休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "书桌-FC_书椅-BL_休闲椅-BR_休闲椅": {"group_code": "书桌-FC_书椅-BL_休闲椅-BR_休闲椅", "serial_id": "", "group_space_category": "书桌区II", "seed_figure_group": {"main_figure": {"category": "书桌", "public_category": "书桌", "sub_category": "书桌", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 1201.393, "model_flag": "1", "params": {"length": 1400, "depth": 600, "height": 700}, "_ex_prop": {"material_id": "113246518", "t_group_name": "书桌区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 180, "allowed_overlap": true, "nor_offset_dist": "(0.5*md+0.2*td + 0)", "dir_offset_dist": "0"}, "_dir_side": 1}, "figure": {"category": "书椅", "public_category": "书椅", "sub_category": "书椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 845.461, "model_flag": "1", "params": {"length": 554.39, "depth": 594.82, "height": 845.46}, "_ex_prop": {"t_group_name": "书桌区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md-0.5*td - 50)", "dir_offset_dist": "dir_side * (0.25*ml)", "check_conditions": ["(ml > 1200)"]}, "_dir_side": -1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 861.6100026419128, "model_flag": "1", "params": {"length": 400, "depth": 400, "height": 853.6187211467641}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "(-0.5*md-0.5*td - 50)", "dir_offset_dist": "dir_side * (0.25*ml)"}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 645.9599895039605, "model_flag": "1", "params": {"length": 400, "depth": 400, "height": 637.9687080088117}, "_ex_prop": {"material_id": "", "t_group_name": "卧床区"}, "mirror": 0}}], "group_space_category": "书桌区II", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1400, "y": 1466.374, "z": 0}}, "group_code": "书桌-FC_书椅-BL_休闲椅-BR_休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "圆形茶几-FR_圆形边几": {"group_code": "圆形茶几-FR_圆形边几", "serial_id": "", "group_space_category": "休闲茶几区", "seed_figure_group": {"main_figure": {"category": "茶几", "public_category": "茶几", "sub_category": "圆形茶几", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Circle", "min_z": 0, "max_z": 761.58, "model_flag": "1", "params": {"length": 900, "depth": 690, "height": 761.58}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "allowed_overlap": true, "nor_offset_dist": "0", "dir_offset_dist": "(0.5*ml+0.5*tl + 20)"}, "_dir_side": 1}, "figure": {"category": "边几", "public_category": "边几", "sub_category": "圆形边几", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Circle", "min_z": 0, "max_z": 576.9019999999999, "model_flag": "1", "params": {"length": 400, "depth": 400, "height": 576.81}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "休闲茶几区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1320, "y": 690, "z": 0}}, "group_code": "圆形茶几-FR_圆形边几"}, "seed_candidate_list": [], "generated_list": []}, "边柜": {"group_code": "边柜", "serial_id": "", "group_space_category": "卧室-收纳柜区", "seed_figure_group": {"main_figure": {"category": "边柜", "public_category": "边柜", "sub_category": "边柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 1788.97, "model_flag": "1", "params": {"length": 1702.6, "depth": 460.19, "height": 1788.97}, "_ex_prop": {"material_id": "111975513", "t_group_name": "卧室-收纳柜区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "卧室-收纳柜区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1702.6, "y": 460.19, "z": 0}}, "group_code": "边柜"}, "seed_candidate_list": [], "generated_list": []}, "书柜": {"group_code": "书柜", "serial_id": "", "group_space_category": "书柜区", "seed_figure_group": {"main_figure": {"category": "书柜", "public_category": "书柜", "sub_category": "书柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2434.3830000000003, "model_flag": "1", "params": {"length": 1000, "depth": 300, "height": 2434.382}, "_ex_prop": {"material_id": "122639847", "t_group_name": "书柜区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "书柜区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1000, "y": 300, "z": 0}}, "group_code": "书柜"}, "seed_candidate_list": [], "generated_list": []}, "洗衣机柜": {"group_code": "洗衣机柜", "serial_id": "", "group_space_category": "阳台柜区", "seed_figure_group": {"main_figure": {"category": "洗衣机柜", "public_category": "洗衣机柜", "sub_category": "洗衣机柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400, "model_flag": "1", "params": {"length": 600, "depth": 500, "height": 2400}, "_ex_prop": {"t_group_name": "阳台柜区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "阳台柜区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 500, "z": 0}}, "group_code": "洗衣机柜"}, "seed_candidate_list": [], "generated_list": []}, "休闲椅": {"group_code": "休闲椅", "serial_id": "", "group_space_category": "休闲沙发区", "seed_figure_group": {"main_figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 771.15, "model_flag": "1", "params": {"length": 488.11, "depth": 540.81, "height": 763.15}, "_ex_prop": {"material_id": "103838365", "t_group_name": "休闲沙发区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "休闲沙发区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 488.11, "y": 540.81, "z": 0}}, "group_code": "休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "浴室柜": {"group_code": "浴室柜", "serial_id": "", "group_space_category": "浴室柜区", "seed_figure_group": {"main_figure": {"category": "浴室柜", "public_category": "浴室柜", "sub_category": "浴室柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 400, "max_z": 2116.45, "model_flag": "1", "params": {"length": 600, "depth": 400, "height": 1716.45}, "_ex_prop": {"material_id": "122376206", "t_group_name": "浴室柜区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "浴室柜区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 400, "z": 0}}, "group_code": "浴室柜"}, "seed_candidate_list": [], "generated_list": []}, "马桶": {"group_code": "马桶", "serial_id": "", "group_space_category": "马桶区", "seed_figure_group": {"main_figure": {"category": "马桶", "public_category": "马桶", "sub_category": "马桶", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 763.7, "model_flag": "1", "params": {"length": 450, "depth": 600, "height": 755.6}, "_ex_prop": {"material_id": "118291667", "t_group_name": "马桶区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "马桶区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 450, "y": 600, "z": 0}}, "group_code": "马桶"}, "seed_candidate_list": [], "generated_list": []}, "毛巾架": {"group_code": "毛巾架", "serial_id": "", "group_space_category": "马桶-毛巾架", "seed_figure_group": {"main_figure": {"category": "毛巾架", "public_category": "毛巾架", "sub_category": "毛巾架", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 1300, "max_z": 1750, "model_flag": "1", "params": {"length": 400, "depth": 260, "height": 450}, "_ex_prop": {"material_id": "136220434", "t_group_name": "马桶区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "马桶-毛巾架", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 400, "y": 260, "z": 0}}, "group_code": "毛巾架"}, "seed_candidate_list": [], "generated_list": []}, "花洒": {"group_code": "花洒", "serial_id": "", "group_space_category": "花洒-淋浴区", "seed_figure_group": {"main_figure": {"category": "花洒", "public_category": "花洒", "sub_category": "花洒", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 518, "max_z": 2000, "model_flag": "1", "params": {"length": 400, "depth": 600, "height": 1350}, "_ex_prop": {"material_id": "13708714", "t_group_name": "花洒"}, "mirror": 0}, "sub_figures": [], "group_space_category": "花洒-淋浴区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 400, "y": 600, "z": 0}}, "group_code": "花洒"}, "seed_candidate_list": [], "generated_list": []}, "钻石形淋浴房": {"group_code": "钻石形淋浴房", "serial_id": "", "group_space_category": "钻石形淋浴房区", "seed_figure_group": {"main_figure": {"category": "淋浴房", "public_category": "淋浴房", "sub_category": "钻石形淋浴房", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2100.001, "model_flag": "1", "params": {"length": 800, "depth": 800, "height": 2100}, "_ex_prop": {"t_group_name": "钻石形淋浴房区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "钻石形淋浴房区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 800, "y": 800, "z": 0}}, "group_code": "钻石形淋浴房"}, "seed_candidate_list": [], "generated_list": []}, "一字形淋浴房": {"group_code": "一字形淋浴房", "serial_id": "", "group_space_category": "一字形淋浴房区", "seed_figure_group": {"main_figure": {"category": "淋浴房", "public_category": "淋浴房", "sub_category": "一字形淋浴房", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2100.001, "model_flag": "1", "params": {"length": 1200, "depth": 50, "height": 2100}, "_ex_prop": {"t_group_name": "一字形淋浴房区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "一字形淋浴房区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1200, "y": 50, "z": 0}}, "group_code": "一字形淋浴房"}, "seed_candidate_list": [], "generated_list": []}, "沙发背景墙": {"group_code": "沙发背景墙", "serial_id": "", "group_space_category": "沙发背景墙", "seed_figure_group": {"main_figure": {"category": "沙发背景墙", "public_category": "沙发背景墙", "sub_category": "沙发背景墙", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400.001, "model_flag": "1", "params": {"length": 450, "depth": 75, "height": 2400}, "_ex_prop": {"t_group_name": "沙发背景墙"}, "mirror": 0}, "sub_figures": [], "group_space_category": "沙发背景墙", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 450, "y": 75, "z": 0}}, "group_code": "沙发背景墙"}, "seed_candidate_list": [], "generated_list": []}, "电视背景墙": {"group_code": "电视背景墙", "serial_id": "", "group_space_category": "电视背景墙", "seed_figure_group": {"main_figure": {"category": "电视背景墙", "public_category": "电视背景墙", "sub_category": "电视背景墙", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400.001, "model_flag": "1", "params": {"length": 450, "depth": 20, "height": 2400}, "_ex_prop": {"t_group_name": "电视背景墙"}, "mirror": 0}, "sub_figures": [], "group_space_category": "电视背景墙", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 450, "y": 20, "z": 0}}, "group_code": "电视背景墙"}, "seed_candidate_list": [], "generated_list": []}, "卧室背景墙": {"group_code": "卧室背景墙", "serial_id": "", "group_space_category": "卧室背景墙", "seed_figure_group": {"main_figure": {"category": "背景墙", "public_category": "背景墙", "sub_category": "卧室背景墙", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400.001, "model_flag": "1", "params": {"length": 450, "depth": 50, "height": 2400}, "_ex_prop": {"t_group_name": "卧室背景墙"}, "mirror": 0}, "sub_figures": [], "group_space_category": "卧室背景墙", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 450, "y": 50, "z": 0}}, "group_code": "卧室背景墙"}, "seed_candidate_list": [], "generated_list": []}, "窗帘": {"group_code": "窗帘", "serial_id": "", "group_space_category": "窗帘区", "seed_figure_group": {"main_figure": {"category": "窗帘", "public_category": "窗帘", "sub_category": "窗帘", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2808.100000116974, "model_flag": "1", "params": {"length": 200, "depth": 100, "height": 2400}, "_ex_prop": {"material_id": "137065652", "t_group_name": "窗帘区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "窗帘区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 200, "y": 100, "z": 0}}, "group_code": "窗帘"}, "seed_candidate_list": [], "generated_list": []}, "地毯": {"group_code": "地毯", "serial_id": "", "group_space_category": "地毯区", "seed_figure_group": {"main_figure": {"category": "地毯", "public_category": "地毯", "sub_category": "地毯", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 16, "model_flag": "1", "params": {"length": 200, "depth": 200, "height": 11}, "_ex_prop": {"material_id": "144507862", "t_group_name": "地毯区"}, "mirror": 0}, "sub_figures": [], "group_space_category": "地毯区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 200, "y": 200, "z": 0}}, "group_code": "地毯"}, "seed_candidate_list": [], "generated_list": []}, "床头吊灯": {"group_code": "床头吊灯", "serial_id": "", "group_space_category": "卧室-床头吊灯", "seed_figure_group": {"main_figure": {"category": "床头吊灯", "public_category": "床头吊灯", "sub_category": "床头吊灯", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 1400, "max_z": 2400, "model_flag": "1", "params": {"length": 200, "depth": 200, "height": 1200}, "_ex_prop": {"t_group_name": "卧室-床头吊灯"}, "mirror": 0}, "sub_figures": [], "group_space_category": "卧室-床头吊灯", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 200, "y": 200, "z": 0}}, "group_code": "床头吊灯"}, "seed_candidate_list": [], "generated_list": []}, "边几-FL_休闲椅": {"group_code": "边几-FL_休闲椅", "serial_id": "", "group_space_category": "阳台-休闲区", "seed_figure_group": {"main_figure": {"category": "边几", "public_category": "边几", "sub_category": "边几", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 280, "max_z": 844.28, "model_flag": "1", "params": {"length": 600, "depth": 600, "height": 600}, "_ex_prop": {"material_id": "139710134", "t_group_name": "阳台-休闲区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 20, "nor_offset_dist": "(0.1*md)", "dir_offset_dist": "-(0.5 * ml + 0.6 * tl)", "allowed_overlap": true}, "_dir_side": -1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 280, "max_z": 1087, "model_flag": "1", "params": {"length": 877, "depth": 816, "height": 807}, "_ex_prop": {"material_id": "145537939", "t_group_name": "阳台-休闲区"}, "mirror": 0}}], "group_space_category": "阳台-休闲区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1677.7994326914936, "y": 1066.7408442579128, "z": 0}}, "group_code": "边几-FL_休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "边几-FL_休闲椅-FR_休闲椅": {"group_code": "边几-FL_休闲椅-FR_休闲椅", "serial_id": "", "group_space_category": "阳台-休闲区", "seed_figure_group": {"main_figure": {"category": "边几", "public_category": "边几", "sub_category": "边几", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 280, "max_z": 844.28, "model_flag": "1", "params": {"length": 600, "depth": 600, "height": 600}, "_ex_prop": {"material_id": "124095418", "t_group_name": "阳台-休闲区"}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 20, "nor_offset_dist": "(0.1*md)", "dir_offset_dist": "-(0.5 * ml + 0.6 * tl)", "allowed_overlap": true}, "_dir_side": -1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.61, "model_flag": "1", "params": {"length": 712.26, "depth": 672.29, "height": 850.61}, "_ex_prop": {"material_id": "124093740", "t_group_name": "阳台-休闲区"}, "mirror": 0}}, {"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": -20, "nor_offset_dist": "(0.1*md)", "dir_offset_dist": "(0.5 * ml + 0.6 * tl)", "allowed_overlap": true}, "_dir_side": 1}, "figure": {"category": "休闲椅", "public_category": "休闲椅", "sub_category": "休闲椅", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 850.61, "model_flag": "1", "params": {"length": 680, "depth": 740, "height": 1500}, "_ex_prop": {"material_id": "124093740", "t_group_name": "阳台-休闲区"}, "mirror": 0}}], "group_space_category": "阳台-休闲区", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 2331.020038216399, "y": 927.9462368430269, "z": 0}}, "group_code": "边几-FL_休闲椅-FR_休闲椅"}, "seed_candidate_list": [], "generated_list": []}, "烟道包管": {"group_code": "烟道包管", "serial_id": "", "group_space_category": "烟道包管", "seed_figure_group": {"main_figure": {"category": "烟道包管", "public_category": "烟道包管", "sub_category": "烟道包管", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 0, "max_z": 2400, "model_flag": "1", "params": {"length": 600, "depth": 450, "height": 2400}, "_ex_prop": {"t_group_name": "烟道包管"}, "mirror": 0}, "sub_figures": [], "group_space_category": "烟道包管", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 450, "z": 0}}, "group_code": "烟道包管"}, "seed_candidate_list": [], "generated_list": []}, "转角地柜-FL_拉篮地柜": {"group_code": "转角地柜-FL_拉篮地柜", "serial_id": "", "group_space_category": "转角自由地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角地柜", "public_category": "地柜-转角地柜", "sub_category": "转角地柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 600, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "0", "dir_offset_dist": "-(0.5 * ml + 0.5 * tl)", "allowed_overlap": true, "check_conditions": ["(ml >=899 && tl <350)"]}, "_dir_side": -1}, "figure": {"category": "地柜-拉篮地柜", "public_category": "地柜-拉篮地柜", "sub_category": "拉篮地柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 300, "depth": 560, "height": 810}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "转角自由地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 900, "y": 560, "z": 0}}, "group_code": "转角地柜-FL_拉篮地柜"}, "seed_candidate_list": [], "generated_list": []}, "转角地柜-FR_地柜收口板": {"group_code": "转角地柜-FR_地柜收口板", "serial_id": "", "group_space_category": "转角自由地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角地柜", "public_category": "地柜-转角地柜", "sub_category": "转角地柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 400, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "0", "dir_offset_dist": "(0.5 * ml + 0.5 * tl)", "allowed_overlap": true}, "_dir_side": -1}, "figure": {"category": "小板件-地柜收口板", "public_category": "小板件-地柜收口板", "sub_category": "地柜收口板", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 50, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "转角自由地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 450, "y": 560, "z": 0}}, "group_code": "转角地柜-FR_地柜收口板"}, "seed_candidate_list": [], "generated_list": []}, "转角地柜-FR_地柜见光板": {"group_code": "转角地柜-FR_地柜见光板", "serial_id": "", "group_space_category": "转角自由地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角地柜", "public_category": "地柜-转角地柜", "sub_category": "转角地柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 400, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "0", "dir_offset_dist": "(0.5 * ml + 0.5 * tl)", "allowed_overlap": true}, "_dir_side": -1}, "figure": {"category": "小板件-地柜见光板", "public_category": "小板件-地柜见光板", "sub_category": "地柜见光板", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 20, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "转角自由地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 420, "y": 560, "z": 0}}, "group_code": "转角地柜-FR_地柜见光板"}, "seed_candidate_list": [], "generated_list": []}, "转角自由地柜": {"group_code": "转角自由地柜", "serial_id": "", "group_space_category": "转角自由地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角自由地柜", "public_category": "地柜-转角自由地柜", "sub_category": "转角自由地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 400, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "转角自由地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 400, "y": 560, "z": 0}}, "group_code": "转角自由地柜"}, "seed_candidate_list": [], "generated_list": []}, "单门地柜-FR_地柜收口板": {"group_code": "单门地柜-FR_地柜收口板", "serial_id": "", "group_space_category": "自由收口地柜", "seed_figure_group": {"main_figure": {"category": "地柜-单门地柜", "public_category": "地柜-单门地柜", "sub_category": "单门地柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 300, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "0", "dir_offset_dist": "(0.5 * ml + 0.5 * tl)", "allowed_overlap": true, "check_conditions": ["(ml >= tl-0.1)"]}, "_dir_side": 1}, "figure": {"category": "小板件-地柜收口板", "public_category": "小板件-地柜收口板", "sub_category": "地柜收口板", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 30, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "自由收口地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 330, "y": 560, "z": 0}}, "group_code": "单门地柜-FR_地柜收口板"}, "seed_candidate_list": [], "generated_list": []}, "拉篮地柜-FR_地柜收口板": {"group_code": "拉篮地柜-FR_地柜收口板", "serial_id": "", "group_space_category": "自由收口地柜", "seed_figure_group": {"main_figure": {"category": "地柜-拉篮地柜", "public_category": "地柜-拉篮地柜", "sub_category": "拉篮地柜", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 200, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "0", "dir_offset_dist": "(0.5 * ml + 0.5 * tl)", "allowed_overlap": true, "check_conditions": ["(ml >= tl-0.1)"]}, "_dir_side": 1}, "figure": {"category": "小板件-地柜收口板", "public_category": "小板件-地柜收口板", "sub_category": "地柜收口板", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 30, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "自由收口地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 230, "y": 560, "z": 0}}, "group_code": "拉篮地柜-FR_地柜收口板"}, "seed_candidate_list": [], "generated_list": []}, "地柜收口板-FR_地柜收口板": {"group_code": "地柜收口板-FR_地柜收口板", "serial_id": "", "group_space_category": "自由收口地柜", "seed_figure_group": {"main_figure": {"category": "小板件-地柜收口板", "public_category": "小板件-地柜收口板", "sub_category": "地柜收口板", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 50, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [{"relation": {"type": "NextTo", "_order": 0, "_max_attempt_num": -1, "_level_codes": [], "neighbor_info": {"dir_type": 0, "nor_offset_dist": "0", "dir_offset_dist": "(0.5 * ml + 0.5 * tl)", "allowed_overlap": true, "check_conditions": ["(ml >= tl-0.1)"]}, "_dir_side": 1}, "figure": {"category": "小板件-地柜收口板", "public_category": "小板件-地柜收口板", "sub_category": "地柜收口板", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 30, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}}], "group_space_category": "自由收口地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 80, "y": 560, "z": 0}}, "group_code": "地柜收口板-FR_地柜收口板"}, "seed_candidate_list": [], "generated_list": []}, "地柜收口板": {"group_code": "地柜收口板", "serial_id": "", "group_space_category": "自由收口地柜", "seed_figure_group": {"main_figure": {"category": "小板件-地柜收口板", "public_category": "小板件-地柜收口板", "sub_category": "地柜收口板", "priority": 0, "deform_method": null, "z_level": 1, "shape": "Rect", "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 50, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "自由收口地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 50, "y": 560, "z": 0}}, "group_code": "地柜收口板"}, "seed_candidate_list": [], "generated_list": []}, "自由收口地柜": {"group_code": "自由收口地柜", "serial_id": "", "group_space_category": "自由收口地柜", "seed_figure_group": {"main_figure": {"category": "地柜-自由收口地柜", "public_category": "地柜-自由收口地柜", "sub_category": "自由收口地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 30, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "自由收口地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 30, "y": 560, "z": 0}}, "group_code": "自由收口地柜"}, "seed_candidate_list": [], "generated_list": []}, "水槽地柜": {"group_code": "水槽地柜", "serial_id": "", "group_space_category": "水槽地柜", "seed_figure_group": {"main_figure": {"category": "地柜-水槽地柜", "public_category": "地柜-水槽地柜", "sub_category": "水槽地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 800, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "水槽地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 800, "y": 560, "z": 0}}, "group_code": "水槽地柜"}, "seed_candidate_list": [], "generated_list": []}, "转角水槽地柜": {"group_code": "转角水槽地柜", "serial_id": "", "group_space_category": "转角水槽地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角水槽地柜", "public_category": "地柜-转角水槽地柜", "sub_category": "转角水槽地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 800, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "转角水槽地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 800, "y": 560, "z": 0}}, "group_code": "转角水槽地柜"}, "seed_candidate_list": [], "generated_list": []}, "消毒地柜": {"group_code": "消毒地柜", "serial_id": "", "group_space_category": "消毒地柜", "seed_figure_group": {"main_figure": {"category": "地柜-消毒地柜", "public_category": "地柜-消毒地柜", "sub_category": "消毒地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 600, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "消毒地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 560, "z": 0}}, "group_code": "消毒地柜"}, "seed_candidate_list": [], "generated_list": []}, "微波炉地柜": {"group_code": "微波炉地柜", "serial_id": "", "group_space_category": "微波炉地柜", "seed_figure_group": {"main_figure": {"category": "地柜-微波炉地柜", "public_category": "地柜-微波炉地柜", "sub_category": "微波炉地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 600, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "微波炉地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 560, "z": 0}}, "group_code": "微波炉地柜"}, "seed_candidate_list": [], "generated_list": []}, "炉灶地柜": {"group_code": "炉灶地柜", "serial_id": "", "group_space_category": "炉灶地柜", "seed_figure_group": {"main_figure": {"category": "地柜-炉灶地柜", "public_category": "地柜-炉灶地柜", "sub_category": "炉灶地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 900, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "炉灶地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 800, "y": 560, "z": 0}}, "group_code": "炉灶地柜"}, "seed_candidate_list": [], "generated_list": []}, "灶台柜": {"group_code": "灶台柜", "serial_id": "", "group_space_category": "炉灶地柜", "seed_figure_group": {"main_figure": {"category": "地柜-炉灶地柜", "public_category": "地柜-炉灶地柜", "sub_category": "灶台柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 800, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "炉灶地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 800, "y": 560, "z": 0}}, "group_code": "灶台柜"}, "seed_candidate_list": [], "generated_list": []}, "转角炉灶地柜": {"group_code": "转角炉灶地柜", "serial_id": "", "group_space_category": "转角炉灶地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角炉灶地柜", "public_category": "地柜-转角炉灶地柜", "sub_category": "转角炉灶地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 1000, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "转角炉灶地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 1000, "y": 560, "z": 0}}, "group_code": "转角炉灶地柜"}, "seed_candidate_list": [], "generated_list": []}, "抽屉地柜": {"group_code": "抽屉地柜", "serial_id": "", "group_space_category": "抽屉地柜", "seed_figure_group": {"main_figure": {"category": "地柜-抽屉地柜", "public_category": "地柜-抽屉地柜", "sub_category": "抽屉地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 400, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "抽屉地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 400, "y": 560, "z": 0}}, "group_code": "抽屉地柜"}, "seed_candidate_list": [], "generated_list": []}, "拉篮地柜": {"group_code": "拉篮地柜", "serial_id": "", "group_space_category": "工具拉篮地柜", "seed_figure_group": {"main_figure": {"category": "地柜-拉篮地柜", "public_category": "地柜-拉篮地柜", "sub_category": "拉篮地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 300, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "工具拉篮地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 300, "y": 560, "z": 0}}, "group_code": "拉篮地柜"}, "seed_candidate_list": [], "generated_list": []}, "冰箱": {"group_code": "冰箱", "serial_id": "", "group_space_category": "冰箱", "seed_figure_group": {"main_figure": {"category": "五金电器-冰箱", "public_category": "五金电器-冰箱", "sub_category": "冰箱", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 2500, "model_flag": "1", "params": {"length": 830, "depth": 750, "height": 2400}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "冰箱", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 830, "y": 750, "z": 0}}, "group_code": "冰箱"}, "seed_candidate_list": [], "generated_list": []}, "冰箱高柜": {"group_code": "冰箱高柜", "serial_id": "", "group_space_category": "冰箱高柜", "seed_figure_group": {"main_figure": {"category": "高柜-功能高柜", "public_category": "高柜-功能高柜", "sub_category": "冰箱高柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 0, "max_z": 2400, "model_flag": "1", "params": {"length": 650, "depth": 600, "height": 2400}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "冰箱高柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 650, "y": 600, "z": 0}}, "group_code": "冰箱高柜"}, "seed_candidate_list": [], "generated_list": []}, "米箱地柜": {"group_code": "米箱地柜", "serial_id": "", "group_space_category": "米箱地柜", "seed_figure_group": {"main_figure": {"category": "地柜-米箱柜", "public_category": "地柜-米箱柜", "sub_category": "米箱地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 300, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "米箱地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 300, "y": 560, "z": 0}}, "group_code": "米箱地柜"}, "seed_candidate_list": [], "generated_list": []}, "单门地柜": {"group_code": "单门地柜", "serial_id": "", "group_space_category": "单门地柜", "seed_figure_group": {"main_figure": {"category": "地柜-单门地柜", "public_category": "地柜-单门地柜", "sub_category": "单门地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 350, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "单门地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 350, "y": 560, "z": 0}}, "group_code": "单门地柜"}, "seed_candidate_list": [], "generated_list": []}, "层板地柜": {"group_code": "层板地柜", "serial_id": "", "group_space_category": "单门地柜", "seed_figure_group": {"main_figure": {"category": "地柜-单门地柜", "public_category": "地柜-单门地柜", "sub_category": "层板地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 350, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "单门地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 350, "y": 560, "z": 0}}, "group_code": "层板地柜"}, "seed_candidate_list": [], "generated_list": []}, "双门地柜": {"group_code": "双门地柜", "serial_id": "", "group_space_category": "双门地柜", "seed_figure_group": {"main_figure": {"category": "地柜-双门地柜", "public_category": "地柜-双门地柜", "sub_category": "双门地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 600, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "双门地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 560, "z": 0}}, "group_code": "双门地柜"}, "seed_candidate_list": [], "generated_list": []}, "转角单门地柜": {"group_code": "转角单门地柜", "serial_id": "", "group_space_category": "转角单门地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角单门地柜", "public_category": "地柜-转角单门地柜", "sub_category": "转角单门地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 350, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "转角单门地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 350, "y": 560, "z": 0}}, "group_code": "转角单门地柜"}, "seed_candidate_list": [], "generated_list": []}, "转角自由单门地柜": {"group_code": "转角自由单门地柜", "serial_id": "", "group_space_category": "转角自由单门地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角自由单门地柜", "public_category": "地柜-转角自由单门地柜", "sub_category": "转角自由单门地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 30, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "转角自由单门地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 30, "y": 560, "z": 0}}, "group_code": "转角自由单门地柜"}, "seed_candidate_list": [], "generated_list": []}, "主操作区地柜": {"group_code": "主操作区地柜", "serial_id": "", "group_space_category": "主操作区地柜", "seed_figure_group": {"main_figure": {"category": "地柜-主操作区地柜", "public_category": "地柜-主操作区地柜", "sub_category": "主操作区地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 50, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "主操作区地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 50, "y": 560, "z": 0}}, "group_code": "主操作区地柜"}, "seed_candidate_list": [], "generated_list": []}, "洗菜区地柜": {"group_code": "洗菜区地柜", "serial_id": "", "group_space_category": "洗菜区地柜", "seed_figure_group": {"main_figure": {"category": "地柜-洗菜区地柜", "public_category": "地柜-洗菜区地柜", "sub_category": "洗菜区地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 50, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "洗菜区地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 50, "y": 560, "z": 0}}, "group_code": "洗菜区地柜"}, "seed_candidate_list": [], "generated_list": []}, "基础阵列地柜": {"group_code": "基础阵列地柜", "serial_id": "", "group_space_category": "基础阵列地柜", "seed_figure_group": {"main_figure": {"category": "地柜-基础阵列地柜", "public_category": "地柜-基础阵列地柜", "sub_category": "基础阵列地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 30, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "基础阵列地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 30, "y": 560, "z": 0}}, "group_code": "基础阵列地柜"}, "seed_candidate_list": [], "generated_list": []}, "基础阵列吊柜": {"group_code": "基础阵列吊柜", "serial_id": "", "group_space_category": "基础阵列吊柜", "seed_figure_group": {"main_figure": {"category": "吊柜-基础阵列吊柜", "public_category": "吊柜-基础阵列吊柜", "sub_category": "基础阵列吊柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 300, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "基础阵列吊柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 300, "y": 375, "z": 0}}, "group_code": "基础阵列吊柜"}, "seed_candidate_list": [], "generated_list": []}, "转角地柜": {"group_code": "转角地柜", "serial_id": "", "group_space_category": "转角地柜", "seed_figure_group": {"main_figure": {"category": "地柜-转角地柜", "public_category": "地柜-转角地柜", "sub_category": "转角地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 400, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "转角地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 400, "y": 560, "z": 0}}, "group_code": "转角地柜"}, "seed_candidate_list": [], "generated_list": []}, "烟机吊柜": {"group_code": "烟机吊柜", "serial_id": "", "group_space_category": "烟机吊柜", "seed_figure_group": {"main_figure": {"category": "吊柜-烟机吊柜", "public_category": "吊柜-烟机吊柜", "sub_category": "烟机吊柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 800, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "烟机吊柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 800, "y": 375, "z": 0}}, "group_code": "烟机吊柜"}, "seed_candidate_list": [], "generated_list": []}, "地柜见光板": {"group_code": "地柜见光板", "serial_id": "", "group_space_category": "地柜见光板", "seed_figure_group": {"main_figure": {"category": "小板件-地柜见光板", "public_category": "小板件-地柜见光板", "sub_category": "地柜见光板", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 18, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "地柜见光板", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 18, "y": 560, "z": 0}}, "group_code": "地柜见光板"}, "seed_candidate_list": [], "generated_list": []}, "假门地柜": {"group_code": "假门地柜", "serial_id": "", "group_space_category": "假门地柜", "seed_figure_group": {"main_figure": {"category": "地柜-假门柜", "public_category": "地柜-假门柜", "sub_category": "假门地柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 50, "depth": 100, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "假门地柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 50, "y": 100, "z": 0}}, "group_code": "假门地柜"}, "seed_candidate_list": [], "generated_list": []}, "地柜转角封板": {"group_code": "地柜转角封板", "serial_id": "", "group_space_category": "地柜转角封板", "seed_figure_group": {"main_figure": {"category": "小板件-地柜转角封板", "public_category": "小板件-地柜转角封板", "sub_category": "地柜转角封板", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 100, "max_z": 810, "model_flag": "1", "params": {"length": 30, "depth": 560, "height": 720}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "地柜转角封板", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 30, "y": 560, "z": 0}}, "group_code": "地柜转角封板"}, "seed_candidate_list": [], "generated_list": []}, "吊柜收口板": {"group_code": "吊柜收口板", "serial_id": "", "group_space_category": "吊柜收口板", "seed_figure_group": {"main_figure": {"category": "小板件-吊柜收口板", "public_category": "小板件-吊柜收口板", "sub_category": "吊柜收口板", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 30, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "吊柜收口板", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 30, "y": 375, "z": 0}}, "group_code": "吊柜收口板"}, "seed_candidate_list": [], "generated_list": []}, "吊柜见光板": {"group_code": "吊柜见光板", "serial_id": "", "group_space_category": "吊柜见光板", "seed_figure_group": {"main_figure": {"category": "小板件-吊柜见光板", "public_category": "小板件-吊柜见光板", "sub_category": "吊柜见光板", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 18, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "吊柜见光板", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 18, "y": 375, "z": 0}}, "group_code": "吊柜见光板"}, "seed_candidate_list": [], "generated_list": []}, "窗前吊柜": {"group_code": "窗前吊柜", "serial_id": "", "group_space_category": "窗前吊柜", "seed_figure_group": {"main_figure": {"category": "吊柜-窗前吊柜", "public_category": "吊柜-窗前吊柜", "sub_category": "窗前吊柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 600, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "窗前吊柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 375, "z": 0}}, "group_code": "窗前吊柜"}, "seed_candidate_list": [], "generated_list": []}, "翻门吊柜": {"group_code": "翻门吊柜", "serial_id": "", "group_space_category": "翻门吊柜", "seed_figure_group": {"main_figure": {"category": "吊柜-翻门吊柜", "public_category": "吊柜-翻门吊柜", "sub_category": "翻门吊柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 600, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "翻门吊柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 375, "z": 0}}, "group_code": "翻门吊柜"}, "seed_candidate_list": [], "generated_list": []}, "转角吊柜": {"group_code": "转角吊柜", "serial_id": "", "group_space_category": "转角吊柜", "seed_figure_group": {"main_figure": {"category": "吊柜-转角吊柜", "public_category": "吊柜-转角吊柜", "sub_category": "转角吊柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 600, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "转角吊柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 375, "z": 0}}, "group_code": "转角吊柜"}, "seed_candidate_list": [], "generated_list": []}, "开放吊柜": {"group_code": "开放吊柜", "serial_id": "", "group_space_category": "开放吊柜", "seed_figure_group": {"main_figure": {"category": "吊柜-开放吊柜", "public_category": "吊柜-开放吊柜", "sub_category": "开放吊柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 600, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "开放吊柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 375, "z": 0}}, "group_code": "开放吊柜"}, "seed_candidate_list": [], "generated_list": []}, "单门吊柜": {"group_code": "单门吊柜", "serial_id": "", "group_space_category": "吊柜", "seed_figure_group": {"main_figure": {"category": "吊柜-单门吊柜", "public_category": "吊柜-单门吊柜", "sub_category": "单门吊柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 200, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "吊柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 200, "y": 375, "z": 0}}, "group_code": "单门吊柜"}, "seed_candidate_list": [], "generated_list": []}, "双门吊柜": {"group_code": "双门吊柜", "serial_id": "", "group_space_category": "吊柜", "seed_figure_group": {"main_figure": {"category": "吊柜-双门吊柜", "public_category": "吊柜-双门吊柜", "sub_category": "双门吊柜", "priority": 0, "deform_method": null, "z_level": 0, "min_z": 1680, "max_z": 2384, "model_flag": "1", "params": {"length": 600, "depth": 375, "height": 704}, "_ex_prop": {}, "mirror": 0}, "sub_figures": [], "group_space_category": "吊柜", "normal_free": false, "size_range": {"min": {"x": 0, "y": 0, "z": 0}, "max": {"x": 600, "y": 375, "z": 0}}, "group_code": "双门吊柜"}, "seed_candidate_list": [], "generated_list": []}}