
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
      @media screen and (orientation: landscape) {
        height: calc(var(--vh, 1vh) * 100);
        width: 224px;
      }
    `,
    menu_container: css`

    `,
    tab_box: css`
      display: flex;
      flex-wrap: nowrap;
      margin-top: 10px;
      overflow-x: scroll;
      padding: 0 16px;
      &::-webkit-scrollbar {
        display: none;
      }
      scrollbar-width: none;
      -ms-overflow-style: none;
      @media screen and (orientation: landscape) {
        flex-wrap: wrap;
        padding: 0 12px;
      }
      .item
      {
        width: 66px;
        height: 28px;
        padding: 2px 10px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        margin: 2px 5px 2px 0;
        text-overflow: ellipsis;
        white-space: nowrap;
        @media screen and (orientation: landscape) {
          margin: 2px 4px 2px 0px;
          width: 61px;
        }
      }
      img{
        width: 16px;
        height: 16px;
      }
      .active
      {
        border-radius: 4px;
        background: #EAEAEB;
      }
    `,
    line: css`
      width: 100%;
      height: 1px;
      background: #EAEAEB;
      margin: 10px 0;
    `,
    filterlist: css`
      display: flex;
      margin-bottom: 4px;
      overflow-x: scroll;
      width: auto;
      margin-left: 20px;
      .item
      {
        color: #959598;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 12px;
        line-height: 1.67;
        letter-spacing: 0px;
        text-align: left;
        margin-right: 16px;
        white-space: nowrap;
        cursor: pointer;
      }
      .active
      {
        color: #147FFA;
      }
    `,





    menu_box: css`
      box-sizing: border-box;
      padding: 12px 12px 0 0;
      transition: all .3s;
      min-width: 156px;
      ul {
        padding: 0;
      }
      li {
        padding: 0;
        margin: 0;
        list-style: none;
      }
      .menu {
        > li {
          margin-bottom: 16px;
          transition: all .3s;
        }
        li:hover{
          color: #5B5E60;
        }
        &_columns {
          display: flex;
        }

        &_item {
          /* background: #f2f2f2; */
          padding: 8px 0;

          :first-child {
            margin-right: 12px;
          }
          
          :last-child li:first-child {
            width: 72px;
          }
          li {
            // padding: 0 16px 0 22px;
            margin: 8px 0;
            color: #25282D;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 14px;
            line-height: 20px;
            height: 20px;
            width: 60px;
            letter-spacing: 0px;
            text-align: left;
            cursor: pointer;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            user-select:none;
          }
        }
      }
      .icon {
        color: red;
      }
      .label {
        color: #25282D;
        font-weight: 600;
        font-size: 16px;
        line-height: 1.5;
        letter-spacing: 0px;
        text-align: left;
        height: 24px;
        display: flex;
        align-items: center;
        cursor: pointer;
        user-select:none;

        &_name {
          // margin-left: 5px;
          height: 100%;
        }

        &_name::after {
          content: '';
          height: 8px;
          display: block;
          position: relative;
          top: -3px;
          opacity: 0.5;
          background: linear-gradient(90deg, #66B8FF 0%, #147FFA00 100%);
        }

        &.active {
          color: rgba(20, 127, 250, 1);
        }
      }
    `,
    figure_box: css`
      height: 100%;
      /* min-width: 256px; */
      .layout_btns {
        display: flex;
        align-items: center;
        margin-top:10px;
        padding-left:16px;

        .btn {
          border-radius: 2px;
          background: rgba(20, 127, 250, 0.1);
          color: rgba(20,127,250,1);
          font-weight: bold;
          font-size: 14px;
          margin-bottom:8px;
          width: 100px;
          text-align: center;
          &:first-child{
            margin-right: 8px;
          }
          &:hover{
            background: rgba(20, 127, 250, 0.25);
            color: rgba(20,127,250,1);
          }
        }
      }
    `,
    container_input: css`
      border-radius: 4px;
      background: #F2F3F5;
      color: #000;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 32px;
      letter-spacing: 0px;
      text-align: left;
      // min-width: 326px;
      width: 100%;
      height: 32px;
      border: none;
      padding-left: 36px;
      :focus {
        border-color: none; /* 取消聚焦边框 */
        box-shadow: none; /* 取消聚焦阴影效果 */
        outline: none; /* 取消聚焦时的外边框效果 */
      }
    `,
    Icon: css`
      position: absolute;
      top: 50%;
      translate: 16px -50%;
    `,
    deleteIcon: css`
      position: absolute;
      top: 8px;
      right: 10px;
      cursor: pointer;
    `,
    searchInfo: css`
      display: flex;
    `,
    closeInfo: css`
      display: flex;
      align-items: center;
    `,
    inputInfo: css`
      display: flex;
      flex-grow: 1;
      position: relative;
    `,
    select: css`
      color: #147FFA !important;
    `,

    searchList: css`
      position: absolute;
      top: 32px;
      left: 0;
      right: 0;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
      z-index: 100;
      padding: 8px 0;
      max-height: 300px;
      overflow: auto;
    `,
  };
});
