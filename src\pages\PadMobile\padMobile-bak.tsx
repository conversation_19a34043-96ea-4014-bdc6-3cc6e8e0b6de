import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { Button, Modal, message } from "@svg/antd";
import { useEffect, useState, useRef } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { AI2DesignBasicModes, AI2DesignManager } from "@/Apps/AI2Design/AI2DesignManager";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import Scene3DDiv from "@/components/Scene3DDiv/scene3DDiv";
import MobileNavigation, { NavigationEvent, PageStates } from "./navigation/navigation";
import { _createHxId, _editHxId, cabinetReplaceUrl, checkIsMobile, is_debugmode_website, is_dreamer_mini_App, mini_APP, mode_type, scheme_Id, workDomainMap } from "@/config";
import { HomeProgress } from '@/components';
import HouseSearchPopup from './houseSearchPopup/houseSearchPopup';
import MyLayoutSchemeList from "@/components/MyLayoutSchemeList/MyLayoutSchemeList";
import SaveLayoutSchemeDialog from "@/components/SaveLayoutSchemeDialog/SaveLayoutSchemeDialog";
import DreamerPopup from "../Home/DreamerPopup/dreamerPopup";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample";
import { LayoutSchemeService } from "@/Apps/LayoutAI/Services/Basic/LayoutSchemeService";
import { loadFile } from "@/IndexDB";
import { getHouseScheme, getReplacementData } from "@/services/home/<USER>";
import CustomKeyboard from "@/components/CustomKeyBoard/customKeyBoard";
import RoomAreaBtns from "./roomAreaBtns/roomAreaBtns";
import MultiSchemeList from "./multiScheme/multiSchemeList/multiSchemeList";
import DreamerHxSearch from '@/components/HxSearch/dreamerHxSearch';
import RoomImagePredict from '@/components/RoomImagePredict/roomImagePredict';
import MobileHistoricalAtlas from "../AiDraw/components/MobileHistoricalAtlas";
import PadPanels from "./padPanel/padPanels";
import { Scene3DEvents } from "@/Apps/LayoutAI/Scene3D/Scene3DEvents";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { SdkService } from "@/services/SdkService";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import EnterPage from "./EnterPage/enterPage";
import { If,Then } from "react-if";
import CabinetCompute from "./CabinetCompute/cabinetCompute";
import { GuidanceChannel } from "@svg/antd-cloud-design";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { Mesh, Vector3 } from "three";
import PadHousePanel from "./padHousePanel/PadHousePanels";
import StartDesignPage from "./StartDesignPage/startDesignPage";
import { IFrameMsgCenter } from "../SdkFrame/MsgCenter/IFrameMsgCenter";
import { IFrameMsgServer } from "../SdkFrame/MsgCenter/IFrameMsgServer";
import { IFrameMsgClientExFuncs } from "../SdkFrame/MsgCenter/IFrameMsgClientExFuncs";
import { IFrameMsgCommnads } from "../SdkFrame/MsgCenter/IFrameMsgCommands";
import { IFrameMsgServerExFuncs } from "../SdkFrame/MsgCenter/IFrameMsgServerExFuncs";
import { IFrameMsgTestScripts } from "../SdkFrame/MsgCenter/IFrameMsgTestScripts";
import { getMyCaseById } from "@/services/home/<USER>";
import { LayoutAI_Configs } from "@/Apps/LayoutAI/Layout/TLayoutEntities/configures/LayoutAIConfigs";
import { Scene3DManager } from "@/Apps/LayoutAI/Scene3D/Scene3DManager";
import { FigureViewControls } from "@/Apps/LayoutAI/Scene3D/controls/FigureViewControls";
import { ZRect } from "@layoutai/z_polygon";
import Progress from "@/components/Progress/Progress";
import { getPollingService } from "@/services/RenderPollingService";
import { SvgGltfCabinetNode } from "@layoutai/model3d_api";
import { Model3dApi } from "@/Apps/LayoutAI/Api/Model3dApi";
import { MeshBuilder } from "@/Apps/LayoutAI/Scene3D/MeshBuilder";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";

function updateMetaViewport()
{
  // 获取viewport的meta标签
var meta = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;
let initialScale = "1.0";
if(Math.min(window.innerWidth,window.innerHeight) < 450 && window.devicePixelRatio>2.8)
{
    initialScale = "0.5";
}
// 修改视口设置
if (meta) {
    meta.setAttribute('content', 'width=device-width, initial-scale='+initialScale+', maximum-scale='+initialScale+' user-scalable=no');
} else {
    // 如果不存在，则添加新的meta标签
    meta = document.createElement('meta')
    meta.name = "viewport";
    meta.content = 'width=device-width, initial-scale='+initialScale+' maximum-scale='+initialScale+', user-scalable=no';
    document.getElementsByTagName('head')[0].appendChild(meta);
}
}


/**
 * @description 主页
 */

const App: React.FC = () => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [layoutSchemeName, setLayoutSchemeName] = useState<string>("");
  const [zIndexOf3DViewer, setZIndexOf3DViwer] = useState<number>(-2);
  let store = useStore();
  const { confirm } = Modal;
  // const schemeNameRef = useRef(null);
  const messageKey = 'SaveSchemeProgress';
  const [messageApi, contextHolder] = message.useMessage();
  const [isSaveAs, setIsSaveAs] = useState<boolean>(false);
  const [showKeyboard, setShowKeyboard] = useState<boolean>(false);
  const [input, setInput] = useState<HTMLInputElement | null>(null);
  const [inputValue, setInputValue] = useState<string>('');
  const [zIndexOfMobileAtlas, setZIndexOfMobileAtlas] = useState<number>(-2);
  const [updateKey, setUpdateKey] = useState<number>(0);
  const hxSearchRef = useRef<any>();
  const [valueChangeCallback, setValueChangeCallback] = useState<((value: number) => void) | null>(null);
  const [showProgress, setShowProgress] = useState<boolean>(false);
  LayoutAI_App.UseApp(AI2DesignManager.AppName); // 确保当前的app_id
  if (LayoutAI_App.instance) {
    LayoutAI_App.t = t;
  }

  const object_id = "PadMobile";

  const updateCanvasSize = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
      LayoutAI_App.instance.update();
    }
    if(LayoutAI_App.IsDebug)
    {
      console.log("UpdateWindowsSize", window.innerWidth,window.innerHeight,window.devicePixelRatio);
    }
    updateLandscape();
  };
  const disposeLayoutCanvas = ()=>{
    if (LayoutAI_App.instance) {
      let app = LayoutAI_App.instance as TAppManagerBase;
      app.disposeLayerCanvas();
    }
  }
  const updateIsWebSiteDebug = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_website_debug = is_debugmode_website;
    }
  }

  const updateLandscape = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_landscape = window.innerWidth > window.innerHeight;
    }
    let t_is_lanscape = store.homeStore.IsLandscape;
    store.homeStore.setIsLandscape(window.innerWidth > window.innerHeight);

    document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
  }

  

  const handleMenuCommand = (command: string) => {
    let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    switch (command) {
      case LayoutAI_Commands.SaveMyLayoutSchemeAs:
        if (layout_container._room_entities.length == 0) {
          messageApi.open({
            key: messageKey,
            type: 'error',
            content: t("当前方案为空，无法保存！"),
            duration: 2,
            style: {
              marginTop: '13vh',
              zIndex: 9999,
            }
          });
        } else {
          setIsSaveAs(true);
          store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: ''});
        }
        break;
      case LayoutAI_Commands.SaveMyLayoutScheme:
        if (layout_container._room_entities.length == 0) {
          messageApi.open({
            key: messageKey,
            type: 'error',
            content: t("当前方案为空，无法保存！"),
            duration: 2,
            style: {
              marginTop: '13vh',
              zIndex: 9999,
            }
          });
        } else {
          if (layout_container._layout_scheme_id == null) {
            setIsSaveAs(false);
            store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: ''});
          } else {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutScheme, null);
          }
        }
        break;
      default:
        break;
    }
  };

  const getSchemeData = async () => {
    if (!scheme_Id) return;
    let params = {
      isDelete: 0,
      pageIndex: 1,
      pageSize: 9,
      keyword: scheme_Id
    }
    const { layoutSchemeDataList, total } = await LayoutSchemeService.getLayoutSchemeList(params);
    if (layoutSchemeDataList) {
      LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, layoutSchemeDataList[0]);
      LayoutAI_App.emit(EventName.OpenHouseSearching, false);
    }
  }

  const layoutWorkOpen = async () => {
    // 个人工作台跳转逻辑
    
    if (mode_type === 'HouseId') {
      (async () => {
        try {
          const file = await loadFile('HouseId');
          console.log(file);
          const fileData = (file as { data: any }).data;
          // pad端走的新的流程，选需求
          if(checkIsMobile())
          {
            // store.trialStore.houseData.id = '7JCEHCICKJFIDLJGFC';    //本地调试用
            store.trialStore.houseData.id = fileData;
          } else 
          {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, { id: fileData, name: '' });
          }
        } catch (error) {
          console.error('Error loading file:', error);
        }
      })();
    } else if (mode_type === 'DwgBase64') {
      (async () => {
        try {
          LayoutAI_App.RunCommand(LayoutAI_Commands.OpenDwgFilefromWork);
        } catch (error) {
          console.error('Error loading file:', error);
        }
      })();
    } else if (mode_type === 'CopyingBase64') {
      (async () => {
        try {
          const file = await loadFile('CopyingBase64');
          const fileData = (file as { data: any }).data;
          store.homeStore.setImgBase64(fileData);
          if(checkIsMobile())
          {
            console.log('fileData',fileData);    
          } else 
          {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.LoadImitateImageFile, {imagePath: fileData});
          }
        } catch (error) {
          console.error('Error loading file:', error);
        }
      })();
    } else if (mode_type === 'hxcreate') {
      if (_createHxId) {
        const res = await getHouseScheme({ id: _createHxId });
        res.result.contentUrl = res.result.dataUrl;
        LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, res.result);
        LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);

      }
    } else if (mode_type === 'hxedit') {
      if (_editHxId) {
        const res: any = await getHouseScheme({ id: _editHxId });
        if (res.success && res.result && res.result.dataUrl) {
          res.result.contentUrl = res.result.dataUrl;
          LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, res.result);
        }
      }
      if (LayoutAI_App.instance) {
        if ((LayoutAI_App.instance as TAppManagerBase)?.layout_container?._drawing_layer_mode == 'SingleRoom') {
          LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
        }
      }
      LayoutAI_App.instance._current_handler_mode = AI2DesignBasicModes.HouseDesignMode;
      LayoutAI_App.RunCommand(AI2DesignBasicModes.HouseDesignMode);
      store.homeStore.setDesignMode(AI2DesignBasicModes.HouseDesignMode);
    } 
  }

  const handleKeyPress = (key: string) => {
    if (input) {
      input.value = input.value + key;
      setInputValue(input.value);
    }
  };

  const handleDelete = () => {
    if (input) {
      input.value = input.value.slice(0, -1);
      setInputValue(input.value);
    }
  };

  const handleConfirm = () => {
    if (!input) return;
    const value = parseFloat(inputValue);
    if (isNaN(value)) return;
    
    // 如果有回调函数，直接调用回调
    if (valueChangeCallback) {
      valueChangeCallback(value);
    } else {
      // 原有的处理逻辑
      input.value = inputValue;
      const event = new Event('change', { bubbles: true });
      input.dispatchEvent(event);
    }
    
    setShowKeyboard(false);
  };

  const handleClose = () => {
    setShowKeyboard(false);
    setInputValue('');
  };

  useEffect(() => {
    updateIsWebSiteDebug();
    updateMetaViewport();
    // LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
    if(LayoutAI_App.IsDebug)
    {            
        let twindow = globalThis as any;
        if(!twindow.VConsole)
        {
          // 引入vConsole
          // const script = document.createElement('script');
          // script.src = 'https://cdn.bootcdn.net/ajax/libs/vConsole/3.15.1/vconsole.min.js';
          // script.onload = function() {
          //     // vConsole 加载完成后初始化
          //     if(twindow.VConsole)
          //     {
          //          var vConsole = new twindow.VConsole();


          //     }
          // };
          // document.head.appendChild(script);
        }

    
    }

    window.addEventListener('resize', updateCanvasSize);
    updateCanvasSize();

    IFrameMsgServer.makeInstance(false, () => {
      for (let key in IFrameMsgClientExFuncs) {
        (IFrameMsgServer.instance as IFrameMsgServer).addAsyncProcessor(key, (IFrameMsgServerExFuncs as any)[key])
      }
      for (let key in IFrameMsgCommnads) {
        (IFrameMsgServer.instance as IFrameMsgServer).addCommand(key, IFrameMsgCommnads[key]);
      }

      (window as any).IFrameMsgServerExFuncs = IFrameMsgServerExFuncs;
      (window as any).IFrameMsgTestScripts = IFrameMsgTestScripts;
    }); // 初始化

    IFrameMsgCenter.instance.init();
    if (LayoutAI_App.instance) {
      if (!LayoutAI_App.instance.initialized) {
        if(checkIsMobile() && (mode_type === 'HouseId' || mode_type === 'CopyingBase64')){
          LayoutAI_App.emit(EventName.Initializing, { initializing: true });
        }
        LayoutAI_App.instance.init();
        LayoutAI_App.instance.startClearConsoleTimer();
        // 初始化默认配置修改
        LayoutAI_Configs.Configs.kitchen_perfer_high_cabient = store.userStore.isHaiEr || false;
        if(store.userStore.isHaiEr)
        {
          if(!LayoutAI_Configs.Configs.default_materialIds)
          {
            LayoutAI_Configs.Configs.default_materialIds = {};
          }
          LayoutAI_Configs.Configs.default_materialIds.SkirtMaterialVoId = "391963992";

          LayoutAI_Configs.Configs.kitchen_layout_size_configs = {
            floorCupboardDepth : 580,
            floorCupboardHeight : 720,
            floorCupboardZVal : 100,
            highCupboardDepth : 580,
            highCupboardHeight : 2400, 
            highCupboardZVal : 0, // 带踢脚为100 不带踢脚为0                
            wallCupboardDepth : 375,
            wallCupboardHeight : 704,
            wallCupboardZVal : 1680
          }
        }

        LayoutAI_Configs.LoadLocalRenderPerformanceMode();
        // 默认先进入AiCadMode
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
        LayoutAI_App.instance.prepare().then(() => {
          // 个人工作台跳转过来的相关逻辑
          getSchemeData();
          layoutWorkOpen();
          LayoutAI_App.emit(EventName.Initializing, { initializing: false });
          let scene3D =  (LayoutAI_App.instance).scene3D as Scene3D;
          if (scene3D) {
            scene3D.stopRender();
          }
        })
        LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);

        // 梦想家进入就打开户型库
        // if (mini_APP) {
        //   LayoutAI_App.emit(EventName.OpenHouseSearching, true);
        // }
      }
      if (window?.URLSearchParams) {
        const urlParams = new URLSearchParams(window.location.search);
        const debug = urlParams.get("debug");
        if (debug !== null) { // 只有在 debug 存在时才执行赋值
          const debugValue = debug === '1' ? 1 : 0; // 根据 debug 的值设置 debugValue
          if (localStorage) {
            localStorage.setItem("LayoutAI_Debug", String(debugValue));
            LayoutAI_App.instance._debug_mode = debugValue;
          }
        }
      }

      LayoutAI_App.instance.update();
    }
    LayoutAI_App.on_M(Scene3DEvents.showLight3DViewer, object_id, (t: boolean) => {
      if (t) {

        if (zIndexOf3DViewer < 0) {
          setZIndexOf3DViwer(2);
          store.homeStore.setZIndexOf3DViewer(2);
          LayoutAI_App.emit(Scene3DEvents.UpdateScene3D, true);
        }
      }
      else {
        setZIndexOf3DViwer(-1);
      }
    });
    LayoutAI_App.on(EventName.ShowDreamerPopup, (t: boolean) => {
      store.homeStore.setShowDreamerPopup(t);
    })
    LayoutAI_App.on(EventName.LayoutSchemeOpened, (event: any) => {
      setLayoutSchemeName(event.name);
      LayoutAI_App.emit(NavigationEvent, PageStates.Default);
    });
    LayoutAI_App.on(EventName.ClearLayout, () => {
      confirm({
        title: t('清空布局'),
        content: t('确定清空单空间布局？'),
        okText: t('确定'),
        cancelText: t('取消'),
        onOk() {
          LayoutAI_App.DispatchEvent(LayoutAI_Events.ClearLayout, this);
        },
        onCancel() { },
      });
    });
    LayoutAI_App.on(EventName.OpenMySchemeList, () => {
      store.homeStore.setShowMySchemeList(true);
    });

    // 这里的showWelcomPage修改
    // LayoutAI_App.on(EventName.ShowWelcomePage,(t:boolean)=>{
    //   LayoutAI_App.emit(EventName.OpenHouseSearching,t);
    // });
    LayoutAI_App.on_M(EventName.RoomList, 'room_list', (roomList: TRoom[]): void => {
      store.homeStore.setRoomInfos(roomList);
    });
    LayoutAI_App.on(EventName.Room2SeriesSampleRoom, (array: [TRoom, TSeriesSample][]) => {
      store.homeStore.setRoom2SeriesSampleArray(array);
    });
    LayoutAI_App.on_M(EventName.SelectingTarget, 'LeftPanelValue',  (params, event, pp) => {
        setShowKeyboard(false);
    });

    LayoutAI_App.on(EventName.showCustomKeyboard, (event: any) => {
      let visible = event?.visible || false;
      if(visible)
      {
        setTimeout(() => {
          setShowKeyboard(true);
        }, 50); // 50ms 延迟1
      } else {
        setTimeout(() => {
          setShowKeyboard(false);
        }, 10); // 50ms 延迟1
      }
      if (event.input) {
        setInputValue(event.input.value);
        setInput(event.input);
        // 保存回调函数
        if (event.onValueChange) {
          setValueChangeCallback(() => event.onValueChange);
        }
      }
    });

    //  素材更新---更新视角
    LayoutAI_App.on_M(EventName.updateAllMaterialScene3D, object_id, async (state: number) => {
      // console.log("SceneContentStateChanged",state);
      let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
      let scene3d =  (LayoutAI_App.instance).scene3D as Scene3D;
      let sceneManager = (LayoutAI_App.instance).scene3DManager as Scene3DManager;
      if (state && store.homeStore.viewMode === '3D_FirstPerson') {
        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: true, title: "更新视角中..." });
        await sceneManager._updateCameraViews({update_imgs:true});
        let currentRoomEntity = sceneManager._findFirstViewRoomEntity();
        store.homeStore.setCurrentViewCameras(currentRoomEntity?._view_cameras || []);        
        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: false, title: "" });
      }
    });

    LayoutAI_App.on(EventName.SaveProgress, (event: { progress: string, id: string, name: string }) => {
      if (event.progress === "success") {
        messageApi.open({
          key: messageKey,
          type: 'success',
          content: t('布局方案保存成功'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
        if(store.homeStore.isAutoExit === 'autoExit')
        {
          SdkService.exitSDK();
          window.parent.postMessage({
              origin: 'layoutai.api',
              type: 'canClose',
              data: {
              canClose: true
              }
          }, '*');
          window.location.href = workDomainMap;
        }
        store.homeStore.setIsAutoExit('');

      } else if (event.progress === "fail") {
        messageApi.open({
          key: messageKey,
          type: 'error',
          content: t('布局方案保存失败'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
      } else if (event.progress === "ongoing") {
        messageApi.open({
          key: messageKey,
          type: 'loading',
          content: t('正在保存布局方案'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
      }
    });


    LayoutAI_App.on_M(EventName.xmlSchemeLoaded, 'padMobile', (data: any) => {
      LayoutContainerUtils.updateAliasName();
      store.homeStore.setRoomEntites((LayoutAI_App.instance as TAppManagerBase).layout_container._room_entities);
    });

    LayoutAI_App.on(EventName.setCurrentViewCameraEntity, (viewEntity: TViewCameraEntity) => {
      store.homeStore.setCurrentViewCameraEntity(viewEntity);
    });

    LayoutAI_App.on(EventName.closeCabinetReplace, (data: any) => {
      store.homeStore.setShowCabinetReplace({show: false});
    });
    LayoutAI_App.on(EventName.saveCabinetReplace, async(data: any) => {
      store.homeStore.setShowCabinetReplace({show: false});
      console.log('store.homeStore.selectEntity',store.homeStore.selectEntity);
      let svgGltfNode = store.homeStore.selectEntity?._figure_element?._solid_mesh3D as SvgGltfCabinetNode;
        //  在这里请求柜子接口，获取柜子替换数据，回显到3D中
        let res = await getReplacementData({
        id: store.homeStore.selectEntity?._figure_element?._matched_material?.nodeId,
        replaceMtlId: store.homeStore.selectEntity?.uidN,
        layoutSchemeId: (LayoutAI_App.instance as TAppManagerBase)?.layout_container._layout_scheme_id,
      });

 
      if(res && res.result.length > 0 && res.result[0]?.replaceMtlData)
      {
        let json = await fetch(res.result[0].replaceMtlData).then(val=>val.json());

        if(json.replaceGlbDescUrl)
        {
          let glbDescJson = await fetch(json.replaceGlbDescUrl).then(val => val.json());  
          if (glbDescJson != null) {
            let newSvgGltfNode = new SvgGltfCabinetNode(glbDescJson);

            newSvgGltfNode.makeSvgGltfNodes();
            newSvgGltfNode.updateCabinetsBox();
            await newSvgGltfNode.updateSolidModels();
            let param = TFigureElement.makeMesh3DMaterialInfoOptions(store.homeStore.selectEntity._figure_element);
           

            store.homeStore.selectEntity._figure_element._solid_mesh3D = newSvgGltfNode;
            store.homeStore.selectEntity.updateMesh3D();

            Model3dApi.UpdatePoseOfModel(newSvgGltfNode, param);
            store.homeStore.selectEntity._figure_element._solid_mesh3D.userData.glbDescUrl = json.replaceGlbDescUrl;
          }
          if(store.homeStore.selectEntity?._figure_element?._solid_mesh3D)
          {
              store.homeStore.selectEntity._figure_element._solid_mesh3D.traverse((node:any) => {
                let mesh = (node as Mesh);
                if (mesh.isMesh) {
                  MeshBuilder.bindMeshMaterials(mesh);
                }
              })
          }

        }

        // 将数组格式转换为Map格式
        const convertedMappings = {
          furnitureNodes: new Map(),
          doorLeafTextureNodes: new Map(),
          doorLeafNodes: new Map(),
          boardPartNodes: new Map(),
        };
        // 转换电器替换数据
        if (json.furnitureNodes && Array.isArray(json.furnitureNodes)) {
          json.furnitureNodes.forEach((item: any) => {
            if (item.uidN) {
              convertedMappings.furnitureNodes.set(item.uidN.toString(), item);
            }
          });
        }
        // 转换门板材质替换数据
        if (json.doorLeafTextureNodes && Array.isArray(json.doorLeafTextureNodes)) {
          json.doorLeafTextureNodes.forEach((item: any) => {
            if (item.uidN) {
              convertedMappings.doorLeafTextureNodes.set(item.uidN.toString(), item);
            }
          });
        }
        // 转换门板素材替换数据
        if (json.doorLeafNodes && Array.isArray(json.doorLeafNodes)) {
          json.doorLeafNodes.forEach((item: any) => {
            if (item.uidN) {
              convertedMappings.doorLeafNodes.set(item.uidN.toString(), item);
            }
          });
        }
        if (json.boardPartNodes && Array.isArray(json.boardPartNodes)) {
          json.boardPartNodes.forEach((item: any) => {
            if (item.uidN) {
              convertedMappings.boardPartNodes.set(item.uidN.toString(), item);
            }
          });
        }
        console.log('转换后的替换数据:', convertedMappings);

        if (convertedMappings) {
            // 回显替换门板
            svgGltfNode.loadGLBParserWithReplacement(svgGltfNode,convertedMappings);
        }


        
        if (LayoutAI_App.instance.scene3DManager) {
          LayoutAI_App.instance.scene3DManager.onElementUpdate(store.homeStore.selectEntity._figure_element, { isWhite: false });
        }
      }
  });
  }, [store.homeStore.isAutoExit]);


  /**
   *  以下是显隐zIndexOf3DViewer的联动
   */
  useEffect(() => {
    if (store.homeStore.zIndexOf3DViewer === 4) {
      setZIndexOf3DViwer(2);
    } else 
    {
      setZIndexOf3DViwer(store.homeStore.zIndexOf3DViewer);
    }

  }, [store.homeStore.zIndexOf3DViewer]);

  useEffect(()=>{
    if(store.homeStore.zIndexOf3DViewer > 0)
    {
       disposeLayoutCanvas();
    }
    updateCanvasSize();
    if(LayoutAI_App.instance.layout_container)
    {
      LayoutContainerUtils.focusCenterByWholeBox(LayoutAI_App.instance.layout_container as any,0.6);
      LayoutAI_App.instance.update();
    }

  },[zIndexOf3DViewer]);



  /**
   * @description 海尔项目跳转到画布需要派发进入画布的事件
   */
  useEffect(() => {
    if(!store.homeStore.showEnterPage.show)
    {
      let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
      setTimeout(async () => {
          if (container._layout_scheme_id) {
              // 执行前立即清除定时器
              SdkService.startDesign(container._layout_scheme_id);
          }
      }, 500);
    }
  }, [store.homeStore.showEnterPage.show]);


  useEffect(() => {
    if(store.homeStore.showCabinetReplace)
    {
      setShowProgress(true);
      setTimeout(() => {
        setShowProgress(false);
      }, 3000);
    }
  }, [store.homeStore.showCabinetReplace]);


  return (
    <div className={styles.root} >
 
      {store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode
        ? <PadHousePanel />
        : <PadPanels updateKey={updateKey} />
      }
      <div id='Canvascontent' className={styles.content}>
        <div className={"3d_container " + styles.canvas3d} style={{ zIndex: zIndexOf3DViewer }}>
          <Scene3DDiv defaultViewMode={4}></Scene3DDiv>
        </div>
        <div id="body_container" className={styles.canvas_pannel + " left_panel_layout" + (zIndexOf3DViewer>0?" isShow3DPreview":"")}>
          <canvas
            id="cad_canvas"
            className="canvas"
            onMouseEnter={() => {
              store.homeStore.setIsmoveCanvas(false);
            }}
            onMouseLeave={() => {
              store.homeStore.setIsmoveCanvas(true);
            }}
            onTouchStart={(e) => {
              if (e.touches.length === 2) {
                const dx = e.touches[0].clientX - e.touches[1].clientX;
                const dy = e.touches[0].clientY - e.touches[1].clientY;
                const distance = Math.sqrt(dx * dx + dy * dy)
                store.homeStore.setInitialDistance(distance / store.homeStore.scale);
              }
            }}
            onTouchMove={(e) => {
              e.stopPropagation();
              if (e.touches[e.touches.length - 1].identifier == 2) return;
              if (e.touches.length === 2) {
                const dx = e.touches[0].clientX - e.touches[1].clientX;
                const dy = e.touches[0].clientY - e.touches[1].clientY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                let newScale = distance / store.homeStore.initialDistance;
                if (newScale > 5) {
                  newScale = 5;
                } else if (newScale < 0.001) {
                  newScale = 0.001;
                }
                store.homeStore.setScale(newScale);

                LayoutAI_App.DispatchEvent(LayoutAI_Events.scale, newScale)
              }
            }}
            onTouchEnd={(e) => {
              if (e.touches.length > 0) {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.updateLast_pos, e)
              }
              store.homeStore.setInitialDistance(null);
            }}

            />

          {
            store.homeStore.designMode === AI2DesignBasicModes.MeasurScaleMode &&
            <div className="canvas_btns" style={{ zIndex: 999999, marginBottom: '10vh', gap: '20px' }}>
              <Button
                className="btn"
                type="primary"
                onClick={() => {
                  if (LayoutAI_App.instance) {
                    LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
                    store.homeStore.setDesignMode(AI2DesignBasicModes.AiCadMode);
                  }
                }}
              >{t("取消")}</Button>
              <Button
                className="btn"
                type="primary"
                onClick={() => {
                  if (LayoutAI_App.instance) {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.ConfirmScale, { img_base64: store.homeStore.img_base64 });
                    store.homeStore.setDesignMode(AI2DesignBasicModes.AiCadMode);
                  }
                }}
              >{t("确定")}</Button>
            </div>
          }
        </div>
      </div>
      <If condition={store.homeStore.showEnterPage.show}>
        <Then>
          <EnterPage></EnterPage>
        </Then>
      </If>
      <CabinetCompute></CabinetCompute>
      <HouseSearchPopup></HouseSearchPopup>
      <HomeProgress></HomeProgress>
      <MultiSchemeList></MultiSchemeList>

      {store.homeStore.showDreamerPopup && <DreamerPopup></DreamerPopup>}

      {/* {store.homeStore.isSingleRoom && store.homeStore.viewMode !== "3D_FirstPerson" && */}
      {store.homeStore.viewMode !== "3D_FirstPerson" && store.homeStore.viewMode !== "3D" 
      && store.homeStore.isSingleRoom 
      && store.homeStore.designMode != AI2DesignBasicModes.HouseDesignMode
      &&
        <div className={styles.RoomAreaBtns}>
          <RoomAreaBtns mode={1} />
        </div>}
      {store.homeStore.showSaveLayoutSchemeDialog.show &&
        <div className={styles.overlay}>
          <SaveLayoutSchemeDialog schemeName={layoutSchemeName || ''} closeCb={() => { store.homeStore.setShowSaveLayoutSchemeDialog({show: false, source: ''}) }}
            isSaveAs={isSaveAs} />
        </div>
      }
      <MyLayoutSchemeList schemeNameCb={(schemeName: string) => { setLayoutSchemeName(schemeName) }} />
      {/* {store.homeStore.showReplace && <Replace></Replace>} */}
      <CustomKeyboard
        onKeyPress={handleKeyPress}
        onDelete={handleDelete}
        onConfirm={handleConfirm}
        onClose={handleClose}
        inputValue={inputValue}
        isVisible={showKeyboard}
      />
      {/* {store.homeStore.showAiDraw && <div className={styles.aiDraw}>
        <AiDraw modelType={true}></AiDraw>
      </div>} */}
      {store.homeStore.showDreamerPopup && <DreamerPopup></DreamerPopup>}

      <DreamerHxSearch ref={hxSearchRef} />
      <RoomImagePredict></RoomImagePredict>
      {
        store.homeStore.showAtlas &&
        <div className={styles.mobile_atlas_container} style={{ zIndex: 999 }}>
          <MobileHistoricalAtlas setZIndexOfMobileAtlas={setZIndexOfMobileAtlas} />
        </div>
      }
      {
        store.homeStore.showStartPage.show &&
        <div className={styles.pad_startpage_container} style={{ zIndex: 999 }}>
          <StartDesignPage toSelectHX={() => {}} step={0}/>
        </div>
      }

      <If condition={store.homeStore.showCabinetReplace.show}>
        <Then>
          <div style={{width: '100%', height: '100%', zIndex:9999, position: 'fixed', top: 0, left: 0, background: '#fff'}}>
            {showProgress && <Progress title={t('正在生成柜子...')} onFinish={() => {}} step={3.33} />}
            <iframe 
                style={{width: '100%', height: '100%', border: 'none'}}
                id="cabinet_replace_iframe" 
                // src={`https://pre-cabinet.3vjia.com:8084/modelChange?materialId=${store.homeStore.showCabinetReplace.materialId}&glbDescUrl=${store.homeStore.selectEntity?._figure_element?._solid_mesh3D?.userData?.glbDescUrl}&uid=${store.homeStore.selectEntity?.uidN}&layoutSchemeId=${(LayoutAI_App.instance as TAppManagerBase)?.layout_container._layout_scheme_id}`} 
                src={`${cabinetReplaceUrl}?materialId=${store.homeStore.showCabinetReplace.materialId}&glbDescUrl=${store.homeStore.selectEntity?._figure_element?._solid_mesh3D?.userData?.glbDescUrl}&uid=${store.homeStore.selectEntity?.uidN}&layoutSchemeId=${(LayoutAI_App.instance as TAppManagerBase)?.layout_container._layout_scheme_id}`} 
              ></iframe>
          </div>
        </Then>          
      </If>
      <GuidanceChannel channelCode='Helptips-006' />
      {contextHolder}
    </div>
  );
};

export default observer(App);
