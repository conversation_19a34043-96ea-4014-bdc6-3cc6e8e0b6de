import React, { useEffect, useState } from "react";
import useStyles from './style';
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { EventName } from "@/Apps/EventSystem";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { GuideMapInterationData, TGuideMapLayout } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TGuideMapLayout";
import { useStore } from "@/models";
import * as THREE from 'three';
import { Box3 } from "three";
import { Vector3 } from "three";
import { observer } from "mobx-react-lite";
import { checkIsMobile } from "@/config";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import { AutoLightingService } from "@/Apps/LayoutAI/Services/AutoLighting/AutoLightService";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import HomeStore from "@/models/homeStore";
import { FigureViewControls } from "@/Apps/LayoutAI/Scene3D/controls/FigureViewControls";
import { Scene3DManager } from "@/Apps/LayoutAI/Scene3D/Scene3DManager";
import { RoomSpaceAreaType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { LightManager } from "@/Apps/LayoutAI/Scene3D/LightManager";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";

const GuideMapWidth = 200;
const GuideMapHeight = 130;

/**
 *  GuideMapData:这个变量在这个文件具有唯一性
 */
const GuideMapData: GuideMapInterationData = {
    hover_distance_tol: 500
}
// 导视图
export const GuideMap: React.FC<{ rightOffset?: number; topOffset?: number }> = ({ rightOffset = 64, topOffset = 60 }) => {
    const { styles } = useStyles();
    const [cameraPosition, setCameraPosition] = useState({ x: 0, y: 0, rotation: 0 });
    const [isCollapsed, setIsCollapsed] = useState(true);
    const overlayCanvasRef = React.useRef<HTMLCanvasElement>(null);
    const [layoutImage, setLayoutImage] = useState<string | null>(null);
    const store = useStore();
    const t = LayoutAI_App.t;
    const object_id = "GuideMap";
    const [isMoving, setIsMoving] = useState<boolean>(false)
    useEffect(() => {
        // 初始化相机位置
        const initCameraPosition = () => {
            GuideMapData.transformData = convertTransformData(GuideMapWidth, GuideMapHeight);
            const image = drawLayoutScheme(GuideMapWidth, GuideMapHeight);
            if (image) {
                setLayoutImage(image);
            }
            let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D as Scene3D;
            const newPosition = calculateCameraPosition(scene3d);
            if (newPosition) {
                setCameraPosition(newPosition);
                drawCameraMarker(newPosition);
                // 处理太阳光跟随视角切换
                // handleSunLightingWithView(scene3d);
            }
        };

        // 等待一帧后初始化，确保场景已经加载
        requestAnimationFrame(() => {
            initCameraPosition();
        });

        let handlerScene3DViewUpdate = async (params: string) => {
            if (isCollapsed) return;
            let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D as Scene3D; 
            const newPosition = calculateCameraPosition(scene3d);
            if (newPosition) {
                // setCameraPosition(newPosition);
                drawCameraMarker(newPosition);
                // 处理太阳光跟随视角切换
                // handleSunLightingWithView(scene3d);
            }
        };

        LayoutAI_App.on_M(EventName.Scene3DViewUpdate, object_id, handlerScene3DViewUpdate);
        return () => {
            LayoutAI_App.off_M(EventName.Scene3DViewUpdate, object_id);
        };
    }, [isCollapsed]);

    useEffect(() => {
        // console.log("viewMode:", store.homeStore.viewMode);
        if (store.homeStore.viewMode !== '2D') {
            setIsCollapsed(false);
        }
        else {
            setIsCollapsed(true);
        }
    }, [store.homeStore.viewMode]);

    useEffect(() => {

        const handlerToggleGuideMapCollapse = async () => {
            setIsCollapsed(!isCollapsed);
        };
        LayoutAI_App.on(EventName.ToggleGuideMapCollapse, handlerToggleGuideMapCollapse);
        return () => {
            LayoutAI_App.off(EventName.ToggleGuideMapCollapse);
        };
    }, [isCollapsed]);


    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    // 当相机位置更新时重绘标记
    // useEffect(() => {
    //     drawCameraMarker();
    // }, [cameraPosition]);

    // 添加计算相机位置的公共函数
    const calculateCameraPosition = (scene3d: Scene3D) => {
        if (!scene3d?.active_controls) return null;

        const position = scene3d.active_controls.getViewPosition(1);
        // 获取相机的前向向量
        const forward = scene3d.active_controls.getViewDirection();


        // 计算在XY平面上的投影向量
        const projectedForward = new THREE.Vector2(forward.x, forward.y).normalize();
        const angle = Math.atan2(-projectedForward.y, projectedForward.x);

        // 转换3D坐标到2D坐标
        const pos2D = convertTo2DPosition(
            position.x,
            position.y,
            GuideMapWidth,
            GuideMapHeight
        );

        if (pos2D) {
            return {
                x: pos2D.x,
                y: pos2D.y,
                rotation: angle
            };
        }
        return null;
    };

    const getWorldPosOnCanvas = (xx: number, yy: number) => {
        const canvas = overlayCanvasRef.current;
        if (!canvas) return null;

        // 获取点击位置相对于canvas的坐标
        const rect = canvas.getBoundingClientRect();
        // 计算点击位置相对于canvas的实际坐标
        const scaleX = GuideMapWidth / rect.width;
        const scaleY = GuideMapHeight / rect.height;
        const x = (xx - rect.left) * scaleX;
        const y = (yy - rect.top) * scaleY;
        return convertToWorldPosition(x, y, GuideMapWidth, GuideMapHeight);

    }

    const applyMousePosToControls = (pos: Vector3, checkWall: boolean = false, needsUpdateCameraViews:boolean=false) => {

        if (!pos) return;
        const scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;
        if (!scene3d || !scene3d.active_controls) return;
        const controls = scene3d.active_controls;
        const prevRoom = store.homeStore.guideMapCurrentRoom;
        if (controls.camera) {

            let direction = controls.getViewDirection();
            let distacne = controls.getViewDistance();
            GuideMapData.is_face_to_wall = false;

            if (store.homeStore.guideMapCurrentRoom && checkWall) {

                let edge = TGuideMapLayout.getHouseValidEdge([store.homeStore.guideMapCurrentRoom], GuideMapData.hover_world_pos, GuideMapData);
                if (edge) {
                    let distance = Math.min(edge.length * 0.8, 5000);

                    let room_poly = edge._poly;
                    if (room_poly) {
                        let int_data = room_poly.getRayIntersection(edge.unprojectEdge2d({ x: edge.length / 2, y: -5 }), edge.nor.clone().negate());

                        if (int_data && int_data.point) {
                            let t_dist = (int_data.point.distanceTo(edge.center));

                            distance = Math.min(distance, t_dist - 200);
                        }
                    }

                    const view_dir = controls.getViewDirection();
                    const view_distacne = controls.getViewDistance();

                    direction = edge.nor.clone();
                    pos = edge.unprojectEdge2d({ x: edge.length / 2, y: -distance });
                    const view_plane_length = new Vector3(view_dir.x, view_dir.y, 0).length();
                    direction.normalize().multiplyScalar(view_plane_length);
                    direction.z = view_dir.z;
                    controls.setCenterAndUpdate(pos, direction, view_distacne);
                    GuideMapData.is_face_to_wall = true;
                }
            }

            controls.setCenterAndUpdate(pos, direction, distacne);

        }
        const newPosition = calculateCameraPosition(scene3d as Scene3D);
        if (newPosition) {
            setCameraPosition(newPosition);
            getCurrentRoom(newPosition);
            let currentRoom = store.homeStore.guideMapCurrentRoom;

            if(needsUpdateCameraViews)
            {
                if(prevRoom !== currentRoom) // 当空间发生变化
                {
                    let manager = LayoutAI_App.instance;
                    let sceneManager = manager.scene3DManager as Scene3DManager;
                    if(sceneManager)
                    {
                        sceneManager._updateCameraViews({update_imgs:false,force:true});
                    }
                }
            }

        }

    }

    const handleMouseDown = (event: React.MouseEvent<HTMLCanvasElement>) => {
        const worldPos = getWorldPosOnCanvas(event.clientX, event.clientY);
        GuideMapData.hover_world_pos = worldPos;
        applyMousePosToControls(worldPos, true,true);

        // let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        // TViewCameraEntity.updateViewCameraEntities(container,null,{methods: 2});
        // console.log(store.homeStore.guideMapCurrentRoom,store.homeStore.guideMapCurrentRoom?._room_entity);

        store.homeStore.setCurrentViewCameras(store.homeStore.guideMapCurrentRoom._room_entity._view_cameras);
    }

    const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
        if (checkIsMobile()) return;
        if (event.buttons == 1) {
            const worldPos = getWorldPosOnCanvas(event.clientX, event.clientY);
            GuideMapData.hover_world_pos = worldPos;

            applyMousePosToControls(worldPos, GuideMapData.is_face_to_wall || false);
        }
        else if (event.buttons == 0) {
            const worldPos = getWorldPosOnCanvas(event.clientX, event.clientY);
            GuideMapData.hover_world_pos = worldPos;
            drawCameraMarker(GuideMapData.camera_position);
        }

        event.preventDefault();
    }
    const handleMouseUp = (event: React.MouseEvent<HTMLCanvasElement>) => {

        event.preventDefault();
    }

    const handleTouchStart = (event: React.TouchEvent<HTMLCanvasElement>) => {
        if (!event?.touches[0]) return;
        const worldPos = getWorldPosOnCanvas(event.touches[0].clientX, event.touches[0].clientY);
        GuideMapData.hover_world_pos = worldPos;
        applyMousePosToControls(worldPos, true,true);
    }

    const handleTouchMove = (event: React.TouchEvent<HTMLCanvasElement>) => {
        if (!event?.touches[0]) return;
        const worldPos = getWorldPosOnCanvas(event.touches[0].clientX, event.touches[0].clientY);
        GuideMapData.hover_world_pos = worldPos;
        applyMousePosToControls(worldPos, GuideMapData.is_face_to_wall || false);
    }
    const handleTouchEnd = (event: React.TouchEvent<HTMLCanvasElement>) => {
        if (!event?.touches[0]) return;
        // const worldPos = getWorldPosOnCanvas(event.touches[0].clientX, event.touches[0].clientY);
        // GuideMapData.hover_world_pos = worldPos;
        // applyMousePosToControls(worldPos, true);

        event.preventDefault();
    }


    // 修改 drawCameraMarker 函数
    const drawCameraMarker = (camera_position: { x: number, y: number, rotation: number } = null) => {
        camera_position = camera_position || GuideMapData.camera_position || cameraPosition;

        const canvas = overlayCanvasRef.current;
        if (!canvas) return;
        if (!camera_position) return;
        canvas.width = GuideMapWidth;
        canvas.height = GuideMapHeight;
        canvas.style.width = `${GuideMapWidth}px`;
        canvas.style.height = `${GuideMapHeight}px`;
        const painter = (LayoutAI_App.instance as TAppManagerBase).painter;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // 清除画布
        ctx.clearRect(0, 0, GuideMapWidth, GuideMapHeight);

        // 保存当前状态
        ctx.save();

        // 获取当前相机位置所在的房间
        const currentRoom = getCurrentRoom(camera_position);

        // 如果找到房间，绘制房间名称
        if (currentRoom) {


            ctx.font = '12px Arial';
            ctx.fillStyle = '#333333';
            ctx.textAlign = 'center';
            // 添加文字描边效果
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = 2;
            // 在房间中心位置绘制文字
            ctx.strokeText(t(currentRoom.name), currentRoom.center.x, currentRoom.center.y);
            ctx.fillText(t(currentRoom.name), currentRoom.center.x, currentRoom.center.y);
            ctx.restore();

            ctx.fillStyle = 'rgb(61, 158, 255)';
            ctx.strokeStyle = 'rgb(61, 158, 255)';
            TGuideMapLayout.drawRoomPolyOnCanvas(currentRoom.room, painter, canvas, GuideMapData, container);
        }

        // 移动到计算出的相机位置
        ctx.translate(camera_position.x, camera_position.y);
        ctx.rotate(camera_position.rotation);

        // 绘制相机标记
        const markerSize = 15; // 调整标记大小

        // 创建径向渐变
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, markerSize);
        gradient.addColorStop(0, 'rgba(61, 158, 255, 0.8)');
        gradient.addColorStop(0.6, 'rgba(61, 158, 255, 0.4)');
        gradient.addColorStop(1, 'rgba(61, 158, 255, 0)');

        // 绘制扇形
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.arc(0, 0, markerSize, -Math.PI / 4, Math.PI / 4);
        ctx.lineTo(0, 0);
        ctx.fillStyle = gradient;
        ctx.fill();

        // 绘制中心点
        ctx.beginPath();
        ctx.arc(0, 0, 4, 0, Math.PI * 2);
        // 先填充白色边框
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.stroke();
        // 再填充中心点颜色
        ctx.fillStyle = 'rgba(61, 158, 255, 1)';
        ctx.fill();

        // 恢复状态
        ctx.restore();
    };

    // 修改获取当前房间的方法，返回房间信息和中心点
    const getCurrentRoom = (position: { x: number, y: number }): { name: string, center: { x: number, y: number }, room?: TRoom } | null => {
        let manager = LayoutAI_App.instance as TAppManagerBase;
        if (!manager?.layout_container) return null;

        let scene3d = manager.scene3D as Scene3D;
        GuideMapData.camera_position = position as any;

        let active_controls = scene3d.active_controls as FigureViewControls;
        let ans_room : TRoom = null;
        if(active_controls.target_view_entity && active_controls._checkViewCameraValid())
        {
           ans_room =   active_controls?.target_view_entity?._room_entity?._room;
        } 

        if(!ans_room)
        {
            let pt3D = convertToWorldPosition(position.x, position.y, GuideMapWidth, GuideMapHeight);
            for (let room of manager.layout_container._rooms) {
                if (room.room_shape._poly.containsPoint(pt3D)) {

                    ans_room = room;
                    break;

                }
            }
        }
        if(ans_room)
        {
            let center3D = ans_room._room_entity._main_rect.rect_center;
            // 转换到2D坐标
            const center2D = convertTo2DPosition(center3D.x, center3D.y, GuideMapWidth, GuideMapHeight);
            if (center2D) {
                store.homeStore.setGuideMapCurrentRoom(ans_room);
                store.homeStore.setSelectedRoom(ans_room._room_entity)
                return {
                    name: ans_room.name,
                    center: center2D,
                    room: ans_room
                };
            }
        }
        return null;
    };


    // 添加绘制方案的函数
    const drawLayoutScheme = (width: number, height: number) => {
        const manager = LayoutAI_App.instance as TAppManagerBase;
        if (!manager?.layout_container) return null;

        const layout = new TGuideMapLayout(manager.layout_container);
        const painter = manager.painter;
        if (!painter) {
            return;
        }
        let canvas = document.createElement("canvas") as HTMLCanvasElement;
        canvas.width = width;
        canvas.height = height;
        canvas.style.width = `${width}px`;
        canvas.style.height = `${height}px`;
        TGuideMapLayout.drawHouseOnCanvas(manager.layout_container, manager.painter, canvas, GuideMapData);
        return canvas.toDataURL();
    };

    const convertTransformData = (canvasWidth: number, canvasHeight: number) => {
        const manager = LayoutAI_App.instance as TAppManagerBase;
        if (!manager?.layout_container) return null;

        // 获取布局的边界框
        let bbox = new Box3();
        for (let room of manager.layout_container._rooms) {
            bbox.union(room.room_shape._poly.computeBBox());
        }

        // 使用与 TGuideMapLayout 相同的变换逻辑
        const center = bbox.getCenter(new Vector3());
        const boxSize = bbox.getSize(new Vector3());
        const roomBoxWidth = boxSize.x;
        const roomBoxHeight = boxSize.y;
        const scaleW = canvasWidth / roomBoxWidth;
        const scaleH = canvasHeight / roomBoxHeight;
        const scale = Math.min(scaleW, scaleH) * 0.85;
        return {
            _p_center: center,
            _p_zval: 0,
            _p_sc: scale
        }
    }
    // 将3D场景坐标转换导视图2D坐标
    const convertTo2DPosition = (x: number, y: number, canvasWidth: number, canvasHeight: number) => {
        // const manager = LayoutAI_App.instance as TAppManagerBase;
        // if (!manager?.layout_container) return null;

        // // 获取布局的边界框
        // let bbox = new Box3();
        // for (let room of manager.layout_container._rooms) {
        //     bbox.union(room.room_shape._poly.computeBBox());
        // }

        // // 使用与 TGuideMapLayout 相同的变换逻辑
        // const center = bbox.getCenter(new Vector3());
        // const boxSize = bbox.getSize(new Vector3());
        // const roomBoxWidth = boxSize.x;
        // const roomBoxHeight = boxSize.y;
        // const scaleW = canvasWidth / roomBoxWidth;
        // const scaleH = canvasHeight / roomBoxHeight;
        // const scale = Math.min(scaleW, scaleH) * 0.85;

        if (!GuideMapData.transformData) {
            GuideMapData.transformData = convertTransformData(GuideMapWidth, GuideMapHeight);
        }
        const center = GuideMapData.transformData._p_center;
        const scale = GuideMapData.transformData._p_sc;
        // 计算相对位置
        const relX = x - center.x;
        const relY = y - center.y;

        // 转换到canvas坐标系
        return {
            x: canvasWidth / 2 + relX * scale,
            y: canvasHeight / 2 - relY * scale  // 注意这里是减号，因为canvas的Y轴向下
        };
    };

    // 将导视图2D坐标转换3D场景坐标
    const convertToWorldPosition = (canvasX: number, canvasY: number, canvasWidth: number, canvasHeight: number) => {
        const manager = LayoutAI_App.instance as TAppManagerBase;
        if (!manager?.layout_container) return null;

        let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;
        if (!scene3d || !scene3d.active_controls) return null;

        if (!GuideMapData.transformData) {
            GuideMapData.transformData = convertTransformData(GuideMapWidth, GuideMapHeight);
        }
        const center = GuideMapData.transformData._p_center;
        const scale = GuideMapData.transformData._p_sc;

        // 计算相对于中心点的偏移
        const relX = (canvasX - canvasWidth / 2) / scale;
        const relY = (canvasY - canvasHeight / 2) / scale;

        // 使用与convertTo2DPosition完全对应的逻辑
        return new Vector3(
            center.x + relX,
            center.y - relY,  // 使用减号来对应convertTo2DPosition中的减号
            scene3d.active_controls.camera.position.z
        );
    };


    return (
        <div 
            className={`${styles.root} ${store.homeStore.viewMode !== "2D" ? styles.blackColor : ''} ${isCollapsed ? styles.collapsed : styles.expanded}`} 
            style={{ right: rightOffset + "px", top: topOffset + "px"}}>
            <div >
                {layoutImage && (
                    <img
                        src={layoutImage}
                        alt={t("导视图")}
                    />
                )}
                <canvas
                    ref={overlayCanvasRef}
                    // onClick={handleCanvasClick}
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseOut={handleMouseUp}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                    onContextMenu={(ev: React.MouseEvent) => ev.preventDefault()}
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        pointerEvents: 'auto'
                    }}
                />
            </div>
        </div>
    );
};

export default observer(GuideMap);