import { AI2DesignBasicModes } from '@/Apps/AI2Design/AI2DesignManager';
import { EventName } from '@/Apps/EventSystem';
import { FigureDataList } from '@/Apps/LayoutAI/Drawing/FigureImagePaths';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { SearchList } from '@/components';
import { useStore } from '@/models';
import { getPrefix } from '@/utils/common';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from '@svg/antd';
import { observer } from "mobx-react-lite";
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import FigureMenu from '../FigureMenu/figureMenu';
import useStyles from './style';
import Icon from '@/components/Icon/icon';
import IconFont from '@/components/IconFont/iconFont';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';

/**
 * @description 按钮组件
 */
const FigureLabelList: React.FC = () => {
  const store = useStore();
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [figureDataList, setFigureDataList] = useState<any[]>([]);
  const [showSpin, setShowSpin] = useState<boolean>(false);
  const [rootName, setRootName] = useState('');
  const [filterName, setFliterName] = useState('');
  const [currentMenuList, setCurrentMenuList] = useState([]);
  const [showSearch, setShowSearch] = useState(false);
  const [searchList, setSearchList] = useState([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [menuList, setMenuList] = useState([]);
  const [isRoomTab, setIsRoomTab] = useState(false);
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />
  // let allList: any = [];
  const [allList, setAllList] = useState<any[]>([]);
  // let finitureList: any = [];
  // let finitureAllList: any = [];



  // finitureList = menuList.filter((item: any) => item.label !== '户型');  /*[i18n:ignore]*/
  // finitureList.map((item: any, index: any) => {
  //   item.child.map((item: any) => {
  //     finitureAllList = finitureAllList.concat(item.figureList);
  //   })
  // });

  const initData = (list: any[]) => {
    handleLabelClick(list[0]);
    setRootName(list[0].label);
    setFliterName(list[0].child[0].label);
    let newlist = [] as any[];
    list[0].child.forEach(item => {
      newlist = newlist.concat(item.figureList);
    });
    setFigureDataList(newlist);
  };

  const isExpanded = (name: string) => {
    return rootName === name;
  };

  const handleLabelClick = (data: any) => {
    setFigureDataList(data.child.figureList);
  };
  const object_id = "FiguresMenu";

  const updateCurrentMenuList = () => {
    let t_menu_list = menuList.filter((data) => !data.label.includes("户型"));
    setCurrentMenuList(t_menu_list);
  }

  const onSearch = (value: string) => {
    setSearchList(allList.filter((item: any) => t(item.title).includes(value)));
  }

  useEffect(() => {
    let list = (LayoutAI_App.IsDebug || store.userStore.beta) ? FigureDataList.filter((item: any) => item.label !== '户型' && item.label !== '视角') : FigureDataList.filter((item: any) => item.label !== '视角' && item.label !== '户型')
    setMenuList(list); /*[i18n:ignore]*/
    let new_list = [] as any[];
    list.map((item) => {
      item.child.map((item: any) => {
        new_list = new_list.concat(item.figureList);
      })
    });
    setAllList(new_list);
    initData(list);

    LayoutAI_App.on_M(EventName.AIDesignModeChanged, object_id, () => {

      updateCurrentMenuList();
    });

    updateCurrentMenuList();

  }, []);

  useEffect(() => {
    setShowSpin(true);
    setTimeout(() => {
      setShowSpin(false);
    }, 200);
  }, [figureDataList]);

  useEffect(() => {
    if (LayoutAI_App.instance._current_handler_mode === AI2DesignBasicModes.HouseDesignMode) return;
  }, [store.homeStore.selectedRoom])



  return (
    <div className={styles.root}>
      <div className={styles.searchInfo}>
        <div className={styles.inputInfo}>
          {inputValue && <Icon
            className={styles.deleteIcon}
            iconClass="iconclosecirle_fill"
            style={{
              fontSize: '16px',
              color: '#6C7175', 
            }}
            onClick={() => {
              setInputValue('');
              initData(menuList);
              setShowSearch(false);
            }}
          >
          </Icon>}
          
          <Icon
            onMouseEnter={() => {
            }}
            onClick={() => {
              onSearch(inputValue);
            }}
            className={styles.Icon}
            iconClass="iconsearch"
            style={{
              fontSize: '16px',
              color: '#6C7175', 
              cursor: 'pointer'
            }}
          />
          <input 
            value={inputValue}
            onKeyDown={(data) => {
              if(data.key != 'Enter') {
                return;
              }
              onSearch(inputValue);
            }}

            onChange={(event) => {
              setShowSearch(true);
              setInputValue(event.currentTarget.value);
              let list = allList.filter((item: any) => item.title.includes(event.currentTarget.value));
              setFigureDataList(list);
              if(event.currentTarget.value === '') {
                initData(menuList);
                setShowSearch(false);
              }
            }}
            onMouseEnter={() => {
            }}
            onMouseLeave={() => {
            }}
            className={styles.container_input} 
            placeholder={t("搜索全部素材")}
          />
        </div>

        <div className={styles.replaceIcon} onClick={() => {
            if(!isRoomTab) {
              let list = (LayoutAI_App.instance as TAppManagerBase).layout_container._room_entities;
              let new_all_list = [] as any[];
              new_all_list = list.reduce((unique, item) => 
                unique.find((obj:TRoomEntity) => obj.name === item.name) ? unique : [...unique, item], 
                [] as any[]
              );
              let tablist = new_all_list.map((item: any) => {
                return {
                  label: item.name,
                  isRoomTab: true,
                  png: `icon/${item.name}.png`,
                  title: item.name
                }
              });
              setMenuList(tablist);
              setIsRoomTab(true);
            } else 
            {
              let list = (LayoutAI_App.IsDebug || store.userStore.beta) ? FigureDataList.filter((item: any) => item.label !== '户型' && item.label !== '视角') : FigureDataList.filter((item: any) => item.label !== '视角' && item.label !== '户型' && item.label !== '定制');
              setMenuList(list);
              setIsRoomTab(false);
            }
           
          }}>
          <IconFont type="icon-change_logo"></IconFont>
          替换
        </div>
      </div>

      {/* 列表页 */}
      {
          <div className={styles.menu_container}>
            <div className={styles.tab_box}>
              {menuList.map((item) => (
                <div
                  key={item.label}
                  className={`item ${isExpanded(item.label) ? 'active' : ''}`}
                  onClick={() => {
                    if(!isRoomTab) {
                      setRootName(item.label);
                      setFliterName(item.child[0].label);
                      let list = [] as any[];
                      item.child.forEach(item => {
                        list = list.concat(item.figureList);
                      });
                      setFigureDataList(list);
                    } else { 
                      setFigureDataList(allList.filter((subItem: any) => subItem.roomType.includes(item.label)));
                    }
                  }}
                >
                  <img src={`https://3vj-fe.3vjia.com/layoutai/figures_imgs/${item.png}?t=${Date.now()}`} alt={(item as any).title} />
                  <span className="label_name">{t(item.label)}</span>
                </div>
              ))}
            </div>
            {/* { !checkIsMobile() && <div className={styles.line}></div>} */}
            {/* <div className={styles.filterlist}>
                  {
                    figureDataList.map((item, index) => (
                      <div 
                        key={item.label}
                        className={`item ${isFilterExpanded(item.label) ? 'active' : ''}`}
                        onClick={() => {
                          handleFilterClick(item);
                          setFliterName(item.label);
                        }}
                      >
                        <div className="title">{t(item.label)}</div>
                      </div>
                    ))
                  }
              </div> */}
            <div className={styles.figure_box}>
              <Spin indicator={antIcon} spinning={showSpin}>
                {figureDataList?.length > 0 && <FigureMenu figureDataList={figureDataList} filterName={filterName} />}
              </Spin>
            </div>
          </div>
          // :
          // <SearchList data={searchList}></SearchList>
      }
    </div>
  );
};
export default observer(FigureLabelList);
