import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
        background-color: #fff;
        height: 100%;
    `,
    menu_box: css`
        width: 100%;
        box-sizing: border-box;
        padding-right: 0;
        transition: all .3s;
      ul,
      li {
        padding: 0;
        margin: 0;
        list-style: none;
      }
      .menu {
        padding: 0 12px;
        /* height: calc(100vh - 220px); */
        &_item {
          /* background: #f2f2f2; */
          padding: 4px 0;
          li {
            padding: 0 16px 0 22px;
            color: rgba(0, 0, 0, 0.65);
            font-size: 13px;
            line-height: 28px;
            height: 28px;
            letter-spacing: 0px;
            text-align: left;
            cursor: pointer;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            user-select:none;
            &:hover {
              background-color: #d9d9d9;
            }
          }
        }
      }
      .icon {
        color: #8B8B8B;
      }
      .label {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 600;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0px;
        text-align: left;
        height: 36px;
        display: flex;
        align-items: center;
        cursor: pointer;
        user-select:none;
        &_name {
          margin-left: 5px;
        }
      }
    `,
    Icon: css`
      position: absolute;
      top: 65px;
      left: 90px;
    `,
    figureListInfo: css`
    `,
    figure_item: css`
      display: flex;
      height: 64px;
      margin: 12px 0px; 
      align-items: center;
      width: 100%;
    `,
    figure_item_title:css`
      height:20px;
      margin-left:5px;
      margin-top:0;
      margin-bottom:0;
      font-size:13px;
      font-weight:700;
      align-items: left;
      text-align:center;
    `,
    figure_item_fg: css`
      margin-top:10px;
      position: relative;
      .ant-image-img{
        width: 64px;
        height: 64px;
        /* background-color: #F2F3F5; */
        margin-right: 8px;
        /* padding: 8px; */
      }
      .clear_btn {
        display:none; 
        width: 60px;
        height: 30px;
        font-size: 11px;
        line-height:18px;
        background: #DDDFE4;
        color :#2b2b2b;
        border-radius: 2px;
        border: 1px solid #DDDFE4; 
        transition: all .3s;
        cursor: pointer;
      }
      :hover .clear_btn {
        display:block;
      }
      :hover .lock_icon {
        display:block;
      }
      .lock_icon.iconlock_fill {
        display:block;
      }

    `,
    figure_item_qt: css`
      .ant-image-img{
        width: 64px;
        height: 64px;
        background-color: #F2F3F5;
        margin-right: 8px;
        padding: 8px;
        transition: all .3s;
      }
      
      .imgTitle {
        position: absolute;
        width: 50px;
        text-align: center;
        height: 20px;
        background: #33333366;
        color: #eee;
        line-height: 20px;
        left: 7px;
        opacity: 0.9;
      }
      :hover{
        background-color: #F2F3F5;
        .imgTitle {
          color:#fff;
          opacity:1;
        }
        .lock_icon {
          display:block;
          right:3px;
          top:-10px;
          font-size:20px;
          z-index:1001;
          :hover {
            color:#aaa;
          }
        }
        .lock_icon.iconlock_fill {
          display:block;
          font-size:20px;
          color:#fff;
          :hover {
            color:#fff;
          }
        
        }
      }
      
    `,
    figure_item_sp: css`
      transition: all .3s;
      cursor: pointer;
      :hover{
        background-color: #F2F3F5;
      }
    `,
    figure_item_checked: css`
      background-color:rgba(206, 184, 249,0.5);
    `,
    figure_item_locked: css`
      :hover {
        background:none;
      }
    `,
    mask_item_locked: css`
      position: absolute;
      width: 95%;
      background-color: #33333377;
      display: block;
      height: 76px;
      z-index: 99;
      opacity:0.8;
  `,
    category: css`
      display: flex; 
      justify-content: flex-start;
      color: #282828;
      font-size: 12px;
      margin-top: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `,
    category_container: css`
      display: flex;
      flex-direction: column; /* 子元素从上到下排列 */
      justify-content: center; /* 垂直方向居中 */
      align-items: left; /* 水平方向居中 */
    `,
    category_span: css`
      margin-right: 5px;
    `,
    room_name: css`
      width: 100%;
      color: black;
      font-weight: bold;
      font-size: 14px;
      flex-grow: 0;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    `,
    room_area: css`
      width: 100%;
      color: black;
      font-size: 12px;
      flex-grow: 0;
      flex-shrink: 0;
    `,
    room_base_info: css`
      display: flex;
      flex-direction: column;
      width: 70px;
      height: 90%;
      justify-content: space-between;
      button:disabled {
          background-color: #cccccc; /* 设置背景颜色为灰色 */
          color: #666666; /* 设置文本颜色为灰色 */
          cursor: not-allowed; /* 更改鼠标指针为不可用状态 */
          opacity: 0.6; /* 设置透明度，使其看起来不可用 */
      }
    }
    `,
    room_clear:css`
    `,
    module_series: css`
      margin-left: 10px;
      width: 65%;
      position: relative;
    `,
    size: css`
      color: #a2a2a5;
      font-size: 12px;  
      line-height: 20px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `,
    lock_icon: css`
      font-size:12px;
      position:absolute;
      right:0;
      color:#aaa;
      display:none;
      cursor:pointer;
    `
  };
});
