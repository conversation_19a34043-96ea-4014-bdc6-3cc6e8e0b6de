import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useEffect, useState, useReducer, useRef } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import SchemeList from "@/components/LeftPanel/components/SchemeList/schemeList";
import SeriesList from "../seriesList/seriesList";
import { ViewCameraList } from "../viewCameraList/viewCameraList";
import FigureLabelList from '../materialList/Menu/figureLabelList';
import AttributeEdit from "../AttributeEdit/attributeEdit";
import IconFont from "@/components/IconFont/iconFont";
import Replace from "@/pages/Mobile/Replace/replace";
import { useStore } from "@/models";
import SearchMaterial from "../searchMaterial/searchMaterial";
import { Button, Modal, Segmented, Select, Tabs } from "@svg/antd";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import SceneModeBtns from "../sceneModeBtns/sceneModeBtns";
import { message } from "@svg/antd";
import { is_dreamer_mini_App, is_standalone_website, mini_APP, workDomainMap } from "@/config";
import { EventName } from "@/Apps/EventSystem";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { AI_PolyTargetType, DrawingFigureMode, IRoomEntityType, SceneViewMode } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { Scene3DEvents } from "@/Apps/LayoutAI/Scene3D/Scene3DEvents";
import { Vector3 } from "three";
import { LightMainEvents } from "../lightMain/lightMain";
import GuideMap from "../GuideMap/GuideMap";
import SideToolbar from "@/components/Statusbars/sideToolbar";
import BatchRender from "../BatchRender/batchRender";
import { useNavigate } from "react-router-dom";
import AiDrawingGallery from "@/components/ImageGallery/aiDrawingGallery";
import PadStatusbar from "../StatusBar/padStatusbar";
import { LayoutPopEvents } from "../layoutPopup/layoutPopup";
import LeftSizeEditor from "../Size/leftSizeEditor";
import Sharebar from "@/components/Sharebar/sharebar";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { CameraViewMode } from "@/Apps/LayoutAI/Scene3D/SceneMode";
import SpaceAreaAttribute from "../SpaceAreaAttribute/SpaceAreaAttribute";
import DrawPicture from "./components/drawPicture/drawPicture";
import { ServerRenderService } from '@/Apps/LayoutAI/Services/ServerRender/ServerRenderService';
import { SdkService } from "@/services/SdkService";
import { LightRuleService } from "@/Apps/LayoutAI/Scene3D/light/rule/LightRuleService";
import { RenderReqOffline } from "@/Apps/LayoutAI/Scene3D/light/req/RenderReqOffline";
import { div, label } from "three/src/nodes/TSL.js";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import Submit from "./components/subMit/submit";
import ExitBar from "./components/ExitBar";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import { CSSTransition } from 'react-transition-group';
import MaterialReplace from "../MaterialReplace/materialReplace";
import Icon from "@/components/Icon/icon";
import ViewSelect from "./components/ViewSelect/viewSelect";
import { If,Then } from "react-if";
import { AI_CadData } from "@/Apps/LayoutAI/AICadData/AI_CadData";
import { TWindowDoorEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWinDoorEntity";
import { Scene3DManager } from "@/Apps/LayoutAI/Scene3D/Scene3DManager";

const stateData = {
    popupType: "Layout",
    sceneMode: "2D",
    prev3DSceneMode: "3D_FirstPerson"
}
type SceneModes = SceneViewMode;

enum drawingPicMode {
    aiDrawing = "aiDrawing",
    render = "render",
}

type BottomButtonConfig = {
    icon?: string, 
    label?: string, 
    onClick?: () => void, 
    display?: "block" | "none", 
    loading?: boolean, 
    opacity?: number | null,
    submit?: boolean
}

function createBottomButton(conf: Partial<BottomButtonConfig>): BottomButtonConfig {
    return {
        icon: '',
        label: '',
        onClick: undefined,
        display: undefined,
        loading: false,
        opacity: 1,
        submit: false,
        ...conf
    }
}

interface SubmitRef {
    clickSubmit: () => Promise<void>;
}

/**
 * 平板左侧的Panel
 * @returns 
 */
const PadPanels: React.FC<{ updateKey: number }> = ({ updateKey: number }) => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const navigate = useNavigate();
    const store = useStore();
    const submitRef = useRef<SubmitRef>(null);
    const { 
        viewMode,
        drawPictureMode, 
        roomEntities,
        isdrawPicture,
        guideMapCurrentRoom,
        setShowDreamerPopup, 
        setIsdrawPicture,
        setDrawPictureMode,
        setViewMode,
        setSelectEntity,
        setShowReplace,
        setShowSaveLayoutSchemeDialog
    } = store.homeStore;
    const [isCollapse, setIsCollapse] = useState<boolean>(false);
    const [isShowShareBar, setIsShowShareBar] = useState<boolean>(false);
    const [sceneMode, setSceneMode] = useState<SceneModes>(viewMode as SceneModes);
    const [popupType, setShowPopupType] = useState<"Layout" | "Matching" | "view" | "material" | "attribute" | "replace" | "searchMaterial" | string>(stateData.popupType);
    const [drawingFigureMode_forComponent, setDrawingFigureModee_forComponent] = useState<DrawingFigureMode>(DrawingFigureMode.Figure2D);
    const [selectedFigureElement, setSelectedFigureElement] = useState<TFigureElement>(null);
    const [bottomBtnItems, setBottomBtnItems] = useState<BottomButtonConfig[]>([]);

    const [leftTabItems,setLeftTabItems] = useState<{label:string,value:string}[]>( [
        {
            value: "Layout",
            label: t('推荐布局'),
        },
        {
            value: "material",
            label: t('编辑布局'),
        }
    ]);
    const object_id = "PadLeftPanel";

    const manager = (LayoutAI_App.instance as TAppManagerBase);
    const layoutContainer = manager.layout_container;
    const scene3D = manager.scene3D as Scene3D;

    const [isNeedCleanLight, setIsNeedCleanLight] = useState<boolean>(false); // 是否需要清除灯光

    const isNarrowMobile = window.innerWidth <= 460;
    /**
     *   由小黑条促发的属性
     */
    const StatusPopUpItems: { [key: string]: { value: string, label: string }[] } = {
        'attribute': [
            {
                value: 'attribute',
                label: t("属性")
            }
        ],
        'sizeEditor': [
            {
                value: 'sizeEditor',
                label: t("尺寸")
            }
        ],
        'SpaceAreaAttribute': [
            {
                value: 'SpaceAreaAttribute',
                label: t("属性")
            }
        ]   
    }
    const topTabItems = [
        {
            value: DrawingFigureMode.Figure2D,
            label: isNarrowMobile ? t('布局') : t('布局模式'),
        },
        {
            value: DrawingFigureMode.Texture,
            label: isNarrowMobile ? t('风格') : t('风格模式'),
        }
    ];

    const rightSceneModeItems: { value: SceneViewMode, label: string }[] = [

        {
            value: "3D_FirstPerson", /*[i18n:ignore]*/
            label: t("漫游")
        },
        {
            value: "3D", /*[i18n:ignore]*/
            label: t("鸟瞰"),
        }
    ]


    const updateLeftTabItems = (needs_check_popup = false) => {
        if (manager.layout_container) {
            if (StatusPopUpItems[popupType]) {
                leftTabItems.length = 0;
                leftTabItems.push(...StatusPopUpItems[popupType]);
            }
            else if (sceneMode === "2D") {
                // console.log(manager.layout_container.drawing_figure_mode);
                if (manager.layout_container.drawing_figure_mode == DrawingFigureMode.Figure2D) {
                    leftTabItems.length = 0;
                    leftTabItems.push(...[
                        {
                            value: "Layout",
                            label: t('推荐布局'),
                        },
                        {
                            value: "material",
                            label: t('编辑布局'),
                        }
                    ]
                    )
                }
                else {
                    leftTabItems.length = 0;
                    leftTabItems.push(...[
                        {
                            value: "Matching",
                            label: t('推荐风格'),
                        },
                        // {
                        //     value: "replace",
                        //     label: t('换搭素材'),
                        // }
                    ]
                    )
                }
            }
            else {
                leftTabItems.length = 0;
                leftTabItems.push(...[
                    {
                        value: "replace",
                        label: t('换搭素材'),
                    }
                ]
                )
            }
        }
        if (!selectedFigureElement) // 如果没有选中图元
        {
            let id = leftTabItems.findIndex(data => data.value === "replace");
            if (id >= 0) leftTabItems.splice(id, 1);
        }
        if (needs_check_popup) {
            let hasData = leftTabItems.find(data => data.value === popupType);
            if (!hasData) {
                onChangePopUpType(leftTabItems[0]?.value || "");
            }
        }
        setLeftTabItems([...leftTabItems]);
    }

    useEffect(() => {
        updateBottomBtnsItems()
    }, [manager.layout_container.drawing_figure_mode])

    const updateBottomBtnsItems = () => {
        if (sceneMode === "2D") {
            setBottomBtnItems([
                createBottomButton({
                    label:  t("上一步"),
                    display: (manager.layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D ? "none" : "block") as "block" | "none",
                    onClick: () => {
                        const currentMode = manager.layout_container.drawing_figure_mode
                        switch(currentMode){
                            case DrawingFigureMode.Figure2D:
                                break
                            case DrawingFigureMode.Texture:
                                changeFigureMode(DrawingFigureMode.Figure2D)

                                // 手动将popupType设置为“推荐布局”（value: "Layout"）
                                const defaultPopupType = "Layout"; // “推荐布局”对应的value
                                onChangePopUpType(defaultPopupType); // 更新popupType状态
                                break
                            default: 
                            console.error("模式错误！currentMode：", currentMode)
                        }
                    },
                }),
                createBottomButton({
                    label: '2D_modes'
                },),
                createBottomButton({
                    label:  t("下一步"),
                    onClick: async () => {
                        const currentMode = manager.layout_container.drawing_figure_mode
                        switch(currentMode){ 
                            case DrawingFigureMode.Figure2D:
                                TSeriesFurnisher.instance.autoApplySeries();
                                changeFigureMode(DrawingFigureMode.Texture)
                                break
                            case DrawingFigureMode.Texture:
                                // manager.layout_container.drawing_figure_mode 
                                if(store.userStore.isHaiEr && !manager.layout_container._rooms[0]?._series_sample_info)
                                {
                                    message.info(t("请先选择套系应用，再进入3D预览!"));
                                }
                                await TSeriesFurnisher.instance.autoApplySeries();
                                setSceneMode(stateData.prev3DSceneMode as any);
                                // 海尔埋点
                                SdkService.enter3DView();
                                break
                            default: 
                                console.error("模式错误！currentMode：", currentMode)
                        }
                    },
                })
            ])
        }
        else if (sceneMode === "3D") {
            setBottomBtnItems([
                createBottomButton({
                    label: t("返回布局"),
                    onClick: () => {
                        setSceneMode("2D");
                    }
                })
            ]);
        }
        else if (sceneMode === "3D_FirstPerson") /*[i18n:ignore]*/ {
            const buttons = [
                createBottomButton({
                    label: t("上一步"),
                    onClick: () => {
                        setSceneMode("2D");
                    },
                    display: (!store.homeStore.isdrawPicture || store.userStore.isHaiEr) ? 'block' : 'none'
                })
            ];
            
            // 海尔项目进入3D默认进入出图模式
            if (store.userStore.isHaiEr) {
                buttons.push(createBottomButton({
                    label: t("提交渲染"),
                    submit: true,
                    onClick: () => {
                        submitRef.current?.clickSubmit();
                    }
                }));
            }
            
            setBottomBtnItems(buttons);
        }
    }


    useEffect(() => {
        updateBottomBtnsItems();
    }, [sceneMode, isdrawPicture]);

    const onChangePopUpType = (type: string) => {
        stateData.popupType = type;
        setShowPopupType(type);
    }

    const changeFigureMode = (targetMode: DrawingFigureMode) => {
        manager.layout_container.drawing_figure_mode = targetMode;
        manager.update();
        setDrawingFigureModee_forComponent(targetMode);
        updateLeftTabItems(true);
    }

    // const onChangeFigureMode = (targetMode: DrawingFigureMode) => {
    //     if (targetMode !== drawingFigureMode_forComponent) {
    //         manager.layout_container.drawing_figure_mode = targetMode;
    //         if(targetMode === DrawingFigureMode.Texture)
    //         {
    //             TSeriesFurnisher.instance.autoApplySeries();
    //         }
    //         manager.update();
    //         setDrawingFigureModee_forComponent(targetMode);
    //         updateLeftTabItems(true);
    //     }
    // }

    const onChangeDrawingMode = (mode: drawingPicMode) => {
        setDrawPictureMode(mode);
        console.log("drawingPicMode", mode);
    }

    useEffect(() => {
        if(isNeedCleanLight){
            console.log("清除灯光效果");
            LightRuleService.cleanLight();  //清除灯光效果
            setIsNeedCleanLight(false);
        }
    }, [isNeedCleanLight])

    const onFigureElementChanged = (figureElement: TFigureElement, type: IRoomEntityType) => {

        if (figureElement && figureElement === selectedFigureElement) return;
        if (layoutContainer.drawing_figure_mode === DrawingFigureMode.Figure2D && sceneMode === "2D") {
            if (type === "Furniture") {
                if (stateData.popupType === "Layout") // 自动跳转到编辑布局
                {
                    onChangePopUpType("material");
                }
            }
            else {
                onChangePopUpType("Layout");
            }

        }
        else {
            if (type === "Furniture" || type === "Door") {
                if (figureElement) {
                    onChangePopUpType("replace");
                }

            }
            else {
                if (figureElement && stateData.popupType == "replace") {
                    // 啥都别干
                    onChangePopUpType("replace");

                }
                else {
                    onChangePopUpType("Matching");
                }
            }
        }


        setSelectedFigureElement(figureElement);
    }
    const onSceneModeChanged = (sceneMode: SceneModes) => {
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let scene3D =  (LayoutAI_App.instance).scene3D as Scene3D;
        let scene3dManager = (LayoutAI_App.instance).scene3DManager as Scene3DManager;

        if (sceneMode === "2D") {
            if (scene3D) {
                scene3D.stopRender();
            }
            if(scene3dManager)
            {
                scene3dManager.currentRoomEntity = null;
            }
            if (window.innerWidth < window.innerHeight * 0.8) {
                LayoutContainerUtils.focusCenterByWholeBox(container, 0.7);
                LayoutAI_App.instance.update();
            }
            else {
                LayoutContainerUtils.focusCenterByWholeBox(container, 0.6);
                LayoutAI_App.instance.update();
            }
            LayoutAI_App.emit_M(LightMainEvents.showLight3DViewer, false);
            setViewMode('2D');
        }
        else if (sceneMode === "3D") {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.Match3dPreviewMaterials, null);
            LayoutAI_App.emit_M(LightMainEvents.showLight3DViewer, true);

            scene3D.setCemeraMode(CameraViewMode.Perspective);
            setViewMode('3D');
            // 进入3D预览的时候, 需要清空选中的图元
            LayoutAI_App.DispatchEvent(LayoutAI_Events.cleanSelect, null);
            // scene3D.setCenter(container.painter.p_center);
            // scene3D.update();
            if (scene3D) {
                scene3D.startRender();
                LayoutAI_App.emit_M(EventName.Scene3DUpdatedDone, false);

            }
        } else if (sceneMode === "3D_FirstPerson") /*[i18n:ignore]*/ {

            scene3D.setCemeraMode(CameraViewMode.FirstPerson);
            // 初次进入: 先计算视角和当前房间, 便于做单空间处理
            if (viewMode == "2D") {
                let currentRoomEntity = container._room_entities.reduce((maxRoom: TRoomEntity | null, currentRoom: TRoomEntity) => {
                    if (!maxRoom) return currentRoom;
                    return currentRoom._area > maxRoom._area ? currentRoom : maxRoom;
                }, null);
                if(container._selected_room)
                {
                    currentRoomEntity =  container._selected_room?._room_entity || currentRoomEntity;
                }

                if (currentRoomEntity) {
                    if(!scene3dManager.currentRoomEntity)
                    {
                        scene3dManager.currentRoomEntity = currentRoomEntity;
                    }
                    let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;

                    if(currentRoomEntity._view_cameras.length == 0)
                    {
                        TViewCameraEntity.updateViewCameraEntities(container,[currentRoomEntity],{methods: 2});
                    }
                    container._selected_room = currentRoomEntity._room;

                    store.homeStore.setCurrentViewCameras(currentRoomEntity._view_cameras);
                    if(currentRoomEntity._view_cameras && currentRoomEntity._view_cameras[0])
                    {
                        scene3D.active_controls.bindViewEntity(currentRoomEntity._view_cameras[0]);

                    }
                    else{
                        scene3D.setCenter(currentRoomEntity?._main_rect?.rect_center || new Vector3(0, 0, 0));

                    }

                    console.log("onSceneModeChanged: ",currentRoomEntity?.roomname);
    
                    // scene3D.update();
                } else {
                    scene3D.setCenter(roomEntities[0]?._main_rect?.rect_center || new Vector3(0, 0, 0));
                    container._selected_room = null;

                }

            }

            // 通知更新所有需要预览的素材:
            //  1.1 先尝试更新白模3D预览
            // LayoutAI_App.DispatchEvent(LayoutAI_Events.Match3dPreviewMaterials, null);

            // 显示3D展示器， 主动更新当前的3D场景---一般是在
            LayoutAI_App.emit_M(Scene3DEvents.showLight3DViewer, true);


            setViewMode('3D_FirstPerson');  /*[i18n:ignore]*/

            if(store.userStore.isHaiEr)
            {
                setIsdrawPicture(true);
            }
            if (scene3D) {
                scene3D.startRender();
                //  通知3D场景已经加载完毕
                LayoutAI_App.emit_M(EventName.Scene3DUpdatedDone, false);

            }

        }
        if (sceneMode && sceneMode !== "2D") {
            stateData.prev3DSceneMode = sceneMode;
        }
        stateData.sceneMode = sceneMode;
        updateLeftTabItems(true);
    }

    // const onExit = () => {
    //     if (!layoutContainer._layout_scheme_id && layoutContainer._room_entities.length > 0) {
    //         setShowSaveLayoutSchemeDialog({show: true, source: 'exitBtn'}); // 先要保存
    //     } else if(layoutContainer._layout_scheme_id) {
    //         LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutScheme, null);
    //         store.homeStore.setIsAutoExit('autoExit');
    //     }
    //     else {
    //         window.parent.postMessage({
    //             origin: 'layoutai.api',
    //             type: 'canClose',
    //             data: {
    //             canClose: true
    //             }
    //         }, '*');
    //         SdkService.exitSDK();
    //         window.location.href = workDomainMap;
    //     }
    // }

    useEffect(() => {
        updateLeftTabItems();
        updateBottomBtnsItems();
        LayoutAI_App.on_M(EventName.FigureElementSelected, object_id, (figure) => {
            let type = AI_CadData.get_polygon_type(figure?.rect);
            if(type == AI_PolyTargetType.Door){
                onFigureElementChanged(figure, AI_PolyTargetType.Door);
            }else{
                onFigureElementChanged(figure, AI_PolyTargetType.Furniture);
            }
        })
        LayoutAI_App.on_M(EventName.SelectingTarget, object_id, (params, event, pp) => {

            if (sceneMode != "2D"){
                if(params?.type == AI_PolyTargetType.RoomArea){
                    let room = (params as TRoomEntity)._room;
                    if (room?.tile) {
                        onFigureElementChanged(room.tile, "RoomArea");
                    }
                }
                return;
            }
            let Entity: TBaseEntity = params || null;

            if ((Entity as TFurnitureEntity)?.figure_element) {
                onFigureElementChanged((Entity as TFurnitureEntity).figure_element, "Furniture");
                SdkService.enterSelection();
            }
            else if (Entity?.type == AI_PolyTargetType.RoomArea) {
                let room = (Entity as TRoomEntity)._room;
                if (room?.tile) {
                    onFigureElementChanged(room.tile, "RoomArea");
                }
                else {
                    onFigureElementChanged(null, "RoomArea");
                }
            }
            else if (Entity?.type == AI_PolyTargetType.Door)  {
                let door = (Entity as TWindowDoorEntity)?._win_figure_element;
                onFigureElementChanged(door, AI_PolyTargetType.Door);
            }else{
                onFigureElementChanged(null, "RoomArea");
            }

            setSelectEntity(Entity);
            if (!Entity) {
                setShowReplace(false);
            }


            let roomEntity = Entity as TRoomEntity;
            if (roomEntity && roomEntity?._room) {

                TSeriesFurnisher.instance.current_rooms = [roomEntity._room];
                TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering({ clickOnRoom: true });

            }
            else {
                if (layoutContainer._rooms) {
                    let rooms = layoutContainer._rooms.filter((room) => room && room.furnitureList.length > 0);
                    TSeriesFurnisher.instance.current_rooms = rooms;
                    TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering({ clickOnRoom: null });
                }
            }
        });

        LayoutAI_App.on_M(LayoutPopEvents.showPopup, object_id, (popupType) => {
            onChangePopUpType(popupType);
        });
        LayoutAI_App.on_M(EventName.SceneModeChanged, object_id, (i_sceneMode: SceneModes) => {
            setSceneMode(i_sceneMode);
        });
        
        LayoutAI_App.on(EventName.Init, () => {
            changeFigureMode(DrawingFigureMode.Figure2D)
            // 手动将popupType设置为“推荐布局”（value: "Layout"）
            const defaultPopupType = "Layout"; // “推荐布局”对应的value
            onChangePopUpType(defaultPopupType); // 更新popupType状态
        });

        // fetchConfig();
        return () => {
            LayoutAI_App.off_M_All({ object_id: object_id });


        }
        
    }, [sceneMode]);

    useEffect(() => {
        if (viewMode === "2D") {
            setSceneMode("2D");
        }
    }, [viewMode]);


    useEffect(() => {
        onSceneModeChanged(sceneMode);
    }, [sceneMode]);

    useEffect(() => {
        // 每当viewMode变化时，更新点击渲染出图按钮的状态为false
        setIsdrawPicture(false);

        if(viewMode == '3D_FirstPerson' && store.userStore.isHaiEr)
        {
            setIsdrawPicture(true);
        }

        setIsNeedCleanLight(true); // 每次切换到2D模式时，需要清除灯光
    },[viewMode]);
    const isLeftPanelOpen = !isCollapse;
    const IsLandscape = window.innerWidth > window.innerHeight;

    const modeBarConfig = [
        { key: 'layout', label: '❶选布局' },
        { key: 'series', label: '❷选套系' },
        { key: 'effect', label: '❸看效果' },
    ];

    const [activeMode, setActiveMode] = useState<string>('layout'); // 用于跟踪当前激活的选项

    useEffect(() => {
        // 根据 drawing_figure_mode 动态更新 activeMode
        if (manager.layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D) {
            setActiveMode('layout');
        } else {
            setActiveMode('series');
        }
    }, [manager.layout_container.drawing_figure_mode]);

    return (
        <>
            <div className={styles.navigation + " topNavigation"}>
                {is_dreamer_mini_App ? 'true' : 'false'}
                {/* {!store.homeStore.isdrawPicture && <div className={`${styles.backBtn} ${sceneMode !== "2D" ? styles.blackColor : ''}`} onClick={() => {
                    // 如果已经是2D模式, 则需要保存
                    // onExit();
                    store.homeStore.setShowEnterPage({show: true, source: 'pad_exit'})
                }}>
                    <Icon style={{fontSize: '16px', marginRight: '4px', color: store.homeStore.viewMode !== "2D" ? '#fff' : '#595959'}} iconClass="icon-a-fangxiangzuo" />
                    {t("退出")}
                </div>} */}
                {/* {!isdrawPicture &&
                    <div className={styles.forwardBtn + " iconfont iconcontrollerrotateleft"} onClick={() => {
                        setIsShowShareBar(!isShowShareBar);
                    }}>
                        {t("分享")}
                    </div>
                } */}

                {/* <div className={styles.closeBtn+" iconfont iconclose1"} onClick={()=>{
                    onExit();
                }}>                
                </div> */}
                {isShowShareBar && <div className="" style={{ top: 50, right: 12, position: 'fixed', zIndex: "999", background: "#fff", padding: "10px" }}>
                    <Sharebar onClose={() => {
                        setIsShowShareBar(false);
                    }}></Sharebar>
                </div>}
                {/* <SceneModeBtns></SceneModeBtns> */}
            </div>
            {/* {sceneMode === "2D" && <div className={styles.topTabs + " topTabs"} style={{marginTop: '45px'}}>
                <div>
                <Segmented value={drawingFigureMode_forComponent} onChange={(targetMode) => {
                    // onChangeFigureMode(~~targetMode);
                    if(targetMode !== drawingFigureMode_forComponent){
                        if(targetMode === DrawingFigureMode.Texture){
                            TSeriesFurnisher.instance.autoApplySeries()
                        }
                        changeFigureMode(targetMode)
                    }
                }} block options={topTabItems} />
                </div>
            </div>} */}
            {/* {(isdrawPicture)  &&  */}
            {isdrawPicture  && 
                <div>
                    <ExitBar></ExitBar>
                </div>
            }
            <div>
                {/* {sceneMode !== "2D" && !isdrawPicture &&
                    <div className={styles.rightSceneModeTabs + " rightSceneModeTabs"}>
                        <Segmented value={sceneMode} onChange={(val) => {
                            setSceneMode(val as any);
                        }} block options={rightSceneModeItems} />
                    </div>
                } */}
                <GuideMap rightOffset={12} topOffset={20}></GuideMap>
            </div>

            <div className={styles.sideToolbarContainer + " sideToolbar " + (sceneMode != "2D" ? "is_3d_mode" : "")}>
                <SideToolbar setSceneMode={setSceneMode}></SideToolbar>
            </div>

            {/* 渲染出图左侧面板不显示 */}
            {sceneMode == '2D' && 
                <div id="pad_left_panel" className={styles.leftPanelRoot + " leftPanelRoot " + (!isLeftPanelOpen ? "panel_hide" : "")} >
                    {isLeftPanelOpen && <div className="closeBtn iconfont iconclose1" onClick={() => setIsCollapse(true)}></div>}
                    {isLeftPanelOpen && leftTabItems.length > 1 &&
                        <div className={styles.tabBar}>
                            <Segmented value={popupType} onChange={(val) => {
                                onChangePopUpType(val);
                            }} block options={leftTabItems} />
                        </div>}
                    <div className={styles.popupContainer + " side_pannel"}>
                        <div className={styles.listContainer} style={{ display: (popupType === "Layout" ? 'block' : 'none') }}> 
                            <SchemeList width={400} showSchemeName={false} isLightMobile={true}></SchemeList> 
                        </div>
                        <div className={styles.listContainer} style={{ display: (popupType === "Matching" ? 'block' : 'none') }}> <SeriesList ></SeriesList> </div>
                        <div className={styles.listContainer} style={{ display: (popupType === "material" ? 'block' : 'none') }}> <FigureLabelList ></FigureLabelList> </div>
                        <div className={styles.listContainer} style={{ display: (popupType === "attribute" ? 'block' : 'none') }}> <AttributeEdit></AttributeEdit> </div>
                        <div className={styles.listContainer} style={{ display: (popupType === "replace" ? 'block' : 'none') }}> <Replace selectedFigureElement={selectedFigureElement}></Replace> </div>
                        <div className={styles.listContainer} style={{ display: (popupType === "searchMaterial" ? 'block' : 'none') }}> <SearchMaterial></SearchMaterial> </div>
                        <div className={styles.listContainer} style={{ display: (popupType === "sizeEditor" ? 'block' : 'none') }}> <LeftSizeEditor></LeftSizeEditor> </div>
                        <div className={styles.listContainer} style={{ display: (popupType === "SpaceAreaAttribute" ? 'block' : 'none') }}> <SpaceAreaAttribute ></SpaceAreaAttribute> </div>  
                    </div>
                </div>
            }
            {(leftTabItems.length > 0) && sceneMode=='2D' && <div className={styles.collapseBtn + (!isLeftPanelOpen ? " panel_hide iconfont iconfill_right" : " iconfont iconfill_left")} onClick={() => {
                setIsCollapse(!isCollapse);
            }}>

            </div>}
            <CSSTransition 
                in={viewMode != '2D' && (!isdrawPicture || store.userStore.isHaiEr) && selectedFigureElement} 
                timeout={300} 
                classNames={{
                    enter: 'fadeEnter',
                    enterActive: 'fadeEnterActive',
                    exit: 'fadeExit',
                    exitActive: 'fadeExitActive'
                }}
                unmountOnExit
                >
                <div 
                    id="pad_left_panel" 
                    className={`${styles.leftPanelRoot} ${viewMode != '2D' ? styles.materialReplace : ''}`}
                    style={viewMode !== '2D' ? { overflow: 'hidden'} : null}
                >
                    <MaterialReplace selectedFigureElement={selectedFigureElement} />
                </div>
            </CSSTransition>
            <PadStatusbar></PadStatusbar>
            {(isdrawPicture) && <Submit ref={submitRef}></Submit>}

            <div className={styles.bottomButtons + " bottomBtns" + (isLeftPanelOpen ? " showLeftPanel" : "")} >
                
                {
                    bottomBtnItems && bottomBtnItems.length > 0 && bottomBtnItems.map((item, index) => (
                        item.label === '2D_modes' ? 
                            // <div
                            //     className={'btnForModeBar'}
                            //     key={"bottomBtn_" + index}
                            //     onClick={item?.onClick}
                            //     style={{
                            //         opacity: item.opacity,
                            //         ...(item.display !== undefined ?{display: item.display} : {})
                            //     }}
                            // >
                            //     {modeBarConfig.map((config) => (
                            //         <span 
                            //             key={config.key}
                            //             className={`modeBarItem ${activeMode === config.key ? 'active' : ''}`}>
                            //             {config.label}
                            //         </span>
                            //     ))}
                            // </div>
                            null
                        :
                            <div
                            className={'btn' + (sceneMode !== "2D" ? ' blackColor' : '') + (item.submit ? ' submit' : '')}
                            key={"bottomBtn_" + index}
                            onClick={item?.onClick}
                            style={{
                                opacity: item.opacity,
                                ...(item.display !== undefined ?{display: item.display} : {})
                            }}
                            >
                                {item?.label}
                            </div>
                    ))
                }
                <If condition={viewMode == "3D_FirstPerson" && (!store.homeStore.isdrawPicture || store.userStore.isHaiEr)}>
                    <Then>
                        <ViewSelect />
                    </Then>
                </If>
            </div>
            <AiDrawingGallery></AiDrawingGallery>
            {(isdrawPicture) && <DrawPicture />}
            <BatchRender></BatchRender>
        </>

    )

};


export default observer(PadPanels);