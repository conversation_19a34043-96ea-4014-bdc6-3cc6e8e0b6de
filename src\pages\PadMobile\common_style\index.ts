
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {


  return {
    root: css`
      width: 180px;
      position: fixed;
      right: 12px;
      top: 12px;
      z-index: 9;
      .ant-segmented
      {
        /* background-color: #EAEBEA; */
        /* color: #282828 !important; */
        @media screen and (max-width: 450px) {
          height: 28px;
        }
      }
      .ant-segmented-item-label
      {
        @media screen and (max-width: 450px) {
          height: 28px;
          line-height: 28px !important;
          font-size: 12px !important;
        }
      }
    `,
    mobile_root:css`
      right: 50px;
    `,
    state_btn:css`
      width: 50px;
      text-align:center;
      line-height:40px;
      font-size:16px;
      color : #777;
      background-color: #fff;
      border: 1px solid #fff;
      &.active {
        background: #147FFA;
        color: #fff;
      }

    `
  }
});
