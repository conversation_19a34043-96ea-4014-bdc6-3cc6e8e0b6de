import { ViewCameraRuler, ViewCameraRulerType } from "../ViewCameraRuler";
import { TViewCameraEntity, IViewCameraGenerateOptions } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { RoomTargetViewHandler } from "./RoomTargetViewHandler";
import { EdgeTargetViewHandler } from "./EdgeTargetViewHandler";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";

export interface ViewCameraRulerHandler {
    handle(
        ruler: ViewCameraRuler,
        roomEntity: TRoomEntity,
        options: IViewCameraGenerateOptions,
        areaEntitys?: TSubSpaceAreaEntity[]
    ): TViewCameraEntity[];
}

const handlers = new Map<ViewCameraRulerType, ViewCameraRulerHandler>();
let isInit: Boolean = false;
const handlerMappings: [ViewCameraRulerType[], new () => ViewCameraRulerHandler][] = [
    [[
        ViewCameraRulerType.SofaView,
        ViewCameraRulerType.DiningCabinetView,
        ViewCameraRulerType.BedView,
        ViewCameraRulerType.KitchenPanoView,
        ViewCameraRulerType.BathroomPanoView,
        ViewCameraRulerType.StudyroomPanoView,
        ViewCameraRulerType.TearoomPanoView
    ], RoomTargetViewHandler],
    [[
        ViewCameraRulerType.LivingSideView,
        ViewCameraRulerType.DiningSideView,
    ], EdgeTargetViewHandler],
];

export class ViewCameraRulerFactory {
    public static init(): void {
        if(isInit) return;
        for (const [types, HandlerClass] of handlerMappings) {
            const handler = new HandlerClass();
            for (const type of types) {
                this.registerHandler(type, handler);
            }
        }
        isInit = true;
    }
    public static registerHandler(type: ViewCameraRulerType, handler: ViewCameraRulerHandler): void {
        handlers.set(type, handler);
    }

    public static getHandler(type: ViewCameraRulerType): ViewCameraRulerHandler {
        return handlers.get(type);
    }
}