import { I_Window } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TRoomLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TRoomLayoutScheme";
import { TBaseRoomToolUtil } from "@/Apps/LayoutAI/Layout/TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { ZEdge } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { TGroupTemplate } from "../../../TGroupTemplate/TGroupTemplate";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";

const xDir: Vector3 = new Vector3(1, 0, 0);
const yDir: Vector3 = new Vector3(0, 1, 0);

export class TSimpleRoomRelationToolUtil {
    private static _instance: TSimpleRoomRelationToolUtil;

    public static curtainGroupCode: string = LayoutAI_App.t("窗帘区");

    public static cabinetGroupKeyCode: string = LayoutAI_App.t("柜");

    public static curtainDepth: number = 100;
    
    private constructor()
    {}

    public static get instance(): TSimpleRoomRelationToolUtil {
        if (!TSimpleRoomRelationToolUtil._instance) {
            TSimpleRoomRelationToolUtil._instance = new TSimpleRoomRelationToolUtil();
        }
        return TSimpleRoomRelationToolUtil._instance;
    }

    public postProcessResultSchemeList(resultSchemeList: TRoomLayoutScheme[], room: TRoom)
    {
        if(room.name == LayoutAI_App.t("厨房") || room.name == LayoutAI_App.t("入户花园") || room.name == LayoutAI_App.t("阳台"))
        {
            return;
        }
        // 1. 添加窗帘
        for(let resultScheme of resultSchemeList)
        {
            addCurtainToScheme(resultScheme, room);
        }
    }
}

function addCurtainToScheme(resultScheme: TRoomLayoutScheme, room: TRoom)
{
    if(resultScheme.group_templates.length == 0)
    {
        return;
    }
    let curtainRectFromResults: ZRect[] = getRectFromResultScheme(resultScheme, TSimpleRoomRelationToolUtil.curtainGroupCode);
    let cabinetRectFromResults: ZRect[] = getRectFromResultScheme(resultScheme, TSimpleRoomRelationToolUtil.cabinetGroupKeyCode);
    let windows: I_Window[] = room.windows.filter(window => (window.type == "Window" && (!window.room_names.includes(LayoutAI_App.t("厨房")))) 
        || (window.realType == "SlidingDoor" && window.room_names.includes(LayoutAI_App.t("阳台"))));
    windows = windows.filter(window => !(room.roomname == "卫生间" && window.room_names.length > 1));
    //TODO 重新整合窗户信息
    let wallThickness: number = 200;
    let WindowDistTol: number = 2000;
    let targetRectWindows: ZRect[] = [];

    let findNearWindowsIndex = (currentWindow: I_Window, nearWindows: I_Window[][]) =>
    {
        for(let i = 0; i < nearWindows.length; i++)
        {
            for(let j = 0; j < nearWindows[i].length; j++)
            {
                if(nearWindows[i][j] == currentWindow)
                {
                    return i;
                }
            }
        }
        return -1;
    };
    for(let roomEdge of room.room_shape._poly.edges)
    {
        let layonWindows: I_Window[] = [];
        for(let window of windows)
        {
            if(window.rect.frontEdge.islayOn(roomEdge, wallThickness, 0.1) || window.rect.backEdge.islayOn(roomEdge, wallThickness, 0.1))
            {
                layonWindows.push(window);
            }
        }
        // 窗户之间相近的则进行合并
        let nearWindows: I_Window[][] = [];
        for(let i = 0; i < layonWindows.length; i++)
        {
            let currentWindow: I_Window = layonWindows[i];
            let index: number = findNearWindowsIndex(currentWindow, nearWindows);
            let tempWindow: I_Window[] = index == -1 ? [currentWindow] : nearWindows[index];
            if(index == -1)
            {
                nearWindows.push(tempWindow);
            }
            for(let j = i + 1; j < layonWindows.length; j++)
            {
                let otherWindow: I_Window = layonWindows[j];
                let dist: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(currentWindow.rect, otherWindow.rect);
                if(dist < WindowDistTol)
                {
                    if(!tempWindow.includes(otherWindow))
                    {
                        tempWindow.push(otherWindow);
                    }
                }
            }
           
        }
        for(let windows of nearWindows)
        {
            let winXMin: number = null;
            let winXMax: number = null;
            let winYMin: number = null;
            let winYMax: number = null;
            for(let window of windows)
            {
                let range: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(window.rect);
                if(winXMin == null || range.xMin < winXMin)
                {
                    winXMin = range.xMin;
                }
                if(winXMax == null || range.xMax > winXMax)
                {
                    winXMax = range.xMax;
                }
                if(winYMin == null || range.yMin < winYMin)
                {
                    winYMin = range.yMin;
                }
                if(winYMax == null || range.yMax > winYMax)
                {
                    winYMax = range.yMax;
                }
            }
            if(winXMin == null)
            {
                continue;
            }
            let winRange: any = {xMin: winXMin, xMax: winXMax, yMin: winYMin, yMax: winYMax};
            let tempWinRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(winRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(winRange));
            if(Math.abs(tempWinRect.nor.dot(windows[0].nor)) < 0.9)
            {
                tempWinRect.swapWidthAndHeight();
            }
            let oldCenter: Vector3 = tempWinRect.rect_center.clone();
            tempWinRect.nor = windows[0].nor.clone();
            tempWinRect.rect_center = oldCenter;
            tempWinRect.updateRect();
            targetRectWindows.push(tempWinRect);
        }
    }

    for(let windowRect of targetRectWindows)
    {
        let isExistCurtain: boolean = isWindowExistCurtain(windowRect, curtainRectFromResults);
        if(isExistCurtain)
        {
            continue;
        }
        let layonRoomInfo: any = getWindowLayonRoomEdge(windowRect, room);
        if(!layonRoomInfo)
        {
            continue;
        }
        let curtainNor: Vector3 = layonRoomInfo.roomEdge.nor.clone().negate();
        let curtainCenter: Vector3 = layonRoomInfo.windowEdge.center.clone();
        let roomEdgeProjectInfo: any = (layonRoomInfo.roomEdge as ZEdge).projectEdge2d({x: curtainCenter.x, y: curtainCenter.y});
        curtainCenter  = (layonRoomInfo.roomEdge as ZEdge).unprojectEdge2d({x: roomEdgeProjectInfo.x, y: 0});
        let curtainLen: number = layonRoomInfo.windowEdge.length;
        let curtainDepth: number = TSimpleRoomRelationToolUtil.curtainDepth;
        let curtainRect: ZRect = new ZRect(curtainLen, curtainDepth);
        curtainRect.nor = curtainNor;
        curtainRect.back_center = curtainCenter;
        curtainRect.updateRect();
        // TODO 重新修正窗帘数据
        if(!(room?.roomname &&room.roomname.includes("卫生间")))
        {
            modifyCurtainRect(curtainRect, layonRoomInfo.roomEdge, cabinetRectFromResults);
        }

        // 创建窗帘模板
        let curtainGroupTemplate: TGroupTemplate = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(
            TSimpleRoomRelationToolUtil.curtainGroupCode, room.name, curtainRect, {consider_depth:false});
        if(curtainGroupTemplate)
        {
            // 布局方案没有进行同步更新
            resultScheme.group_templates.push(curtainGroupTemplate);
            resultScheme._from_graph._group_template_list = resultScheme.group_templates;
            resultScheme.recordFigures(resultScheme._from_graph.result_figure_list);
        }
    }
}

function getCurtainFromResultScheme(resultScheme: TRoomLayoutScheme)
{
    let  curtainRects: ZRect[] = [];
    for(let groupTemplat of resultScheme.group_templates)
    {
        if(groupTemplat.group_space_category != TSimpleRoomRelationToolUtil.curtainGroupCode)
        {
            continue;
        }
        curtainRects.push(groupTemplat._target_rect.clone());
    }
    return curtainRects;
}

function getRectFromResultScheme(resultScheme: TRoomLayoutScheme, spaceGroupCode: string)
{
    let  targetRects: ZRect[] = [];
    for(let groupTemplat of resultScheme.group_templates)
    {
        if(!groupTemplat.group_space_category.includes(spaceGroupCode))
        {
            continue;
        }
        targetRects.push(groupTemplat._target_rect.clone());
    }
    return targetRects;
}


function isWindowExistCurtain(windowRect: ZRect, curtainRects: ZRect[]): boolean
{
    for(let curtainRect of curtainRects)
    {
        if(TBaseRoomToolUtil.instance.isLayOnPolygons(windowRect, curtainRect, null))
        {
            return true;
        }
    }
    return false;
}

function getWindowLayonRoomEdge(windowRect: ZRect, room: TRoom): any
{
    let windowFrontEdge: ZEdge = windowRect.frontEdge;
    let windowBackEdge: ZEdge = windowRect.backEdge;
    let windowEdges: ZEdge[] = [windowFrontEdge, windowBackEdge];
    for(let windowEdge of windowEdges)
    {
        let layonRoomEdge: ZEdge = TBaseRoomToolUtil.instance.edgeOnPolygon(windowEdge, room.room_shape._poly, 200, 0.1);
        if(layonRoomEdge)
        {
            return {windowEdge: windowEdge,  roomEdge: layonRoomEdge};
        }
    }
    return null;
}

function modifyCurtainRect(curtainRect: ZRect, layonRoomEdge: ZEdge, cabinetRects: ZRect[], extendDist: number = 100, toLayonWallEndDistTol: number = 1000)
{
    let curtainBackEdge: ZEdge = curtainRect.backEdge;
    let projectInfo1: any = layonRoomEdge.projectEdge2d({x: curtainBackEdge.v0.pos.x, y: curtainBackEdge.v0.pos.y});
    let projectInfo2: any = layonRoomEdge.projectEdge2d({x: curtainBackEdge.v1.pos.x, y: curtainBackEdge.v1.pos.y});
    let projectXs: number[] = [projectInfo1.x, projectInfo2.x];
    projectXs.sort((a, b) => a - b);
    let projectStart: Vector3 = layonRoomEdge.unprojectEdge2d({x: projectXs[0], y: 0});
    let projectEnd: Vector3 = layonRoomEdge.unprojectEdge2d({x: projectXs[1], y: 0});
    let startSubVec: Vector3 = projectStart.clone().sub(layonRoomEdge.v0.pos);
    let endSubVec: Vector3 = layonRoomEdge.v1.pos.clone().sub(projectEnd);
    let roomEdgeDir: Vector3 = layonRoomEdge.dv.clone();
    let startDot: number = startSubVec.clone().dot(roomEdgeDir);
    let endDot: number = endSubVec.clone().dot(roomEdgeDir);
    if(startDot > 0)
    {
        if(startDot > toLayonWallEndDistTol)
        {
            startDot = extendDist; 
        }
        projectStart.add(roomEdgeDir.clone().multiplyScalar(-startDot));
    }
    if(endDot > 0)
    {
        if(endDot > toLayonWallEndDistTol)
        {
            endDot = extendDist;
        }
        projectEnd.add(roomEdgeDir.clone().multiplyScalar(endDot));
    }

    let xMin: number = Math.min(projectStart.x, projectEnd.x);
    let xMax: number = Math.max(projectStart.x, projectEnd.x);
    let yMin: number = Math.min(projectStart.y, projectEnd.y);
    let yMax: number = Math.max(projectStart.y, projectEnd.y);


    let curtainRectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(curtainRect);
    let roomEdgeXDot: number = Math.abs(roomEdgeDir.clone().dot(xDir));
    let roomEdgeyDot: number = Math.abs(roomEdgeDir.clone().dot(yDir));
    if(roomEdgeXDot > 0.9)
    {
        curtainRectRange.xMin = xMin;
        curtainRectRange.xMax = xMax;
    }
    if(roomEdgeyDot > 0.9)
    {
        curtainRectRange.yMin = yMin;
        curtainRectRange.yMax = yMax;
    }

    // 柜体避让
    for(let cabinetRect of cabinetRects)
    {
        if(TBaseRoomToolUtil.instance.isParallelTwoEdges(cabinetRect.backEdge,  curtainRect.backEdge))
        {
            continue;
        }
        let cabinetRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(cabinetRect);
        if(!TBaseRoomToolUtil.instance.isOverlayRange2ds(cabinetRange, curtainRectRange, false))
        {
            continue;
        }
        let cabinetNor: Vector3 = cabinetRect.nor;
        let cabinetXDot: number = cabinetNor.clone().dot(xDir);
        let cabinetYDot: number = cabinetNor.clone().dot(yDir);
        if(cabinetXDot > 0.9)
        {
            curtainRectRange.xMin = cabinetRange.xMax;
        }
        else if(cabinetXDot < -0.9)
        {
            curtainRectRange.xMax = cabinetRange.xMin;
        }

        if(cabinetYDot > 0.9)
        {
            curtainRectRange.yMin = cabinetRange.yMax;
        }
        else if(cabinetYDot < -0.9)
        {
            curtainRectRange.yMax = cabinetRange.yMin;
        }
    }

    let curtainRectNor: Vector3 = curtainRect.nor.clone();
    let newCurtainRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(curtainRectRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(curtainRectRange));
    let newCurtainCenter: Vector3 = newCurtainRect.rect_center.clone();
    if(Math.abs(newCurtainRect.nor.clone().dot(curtainRectNor)) > 0.9)
    {
        newCurtainRect.nor = curtainRectNor.clone();
    }
    else
    {
        let newCurtainRectLen: number = newCurtainRect.length;
        let newCurtainRectDepth: number = newCurtainRect.depth;
        newCurtainRect.length = newCurtainRectDepth;
        newCurtainRect.depth = newCurtainRectLen;
        newCurtainRect.nor = curtainRectNor.clone();
    }
    newCurtainRect.rect_center = newCurtainCenter.clone();
    newCurtainRect.updateRect();
    curtainRect.copy(newCurtainRect);
}