import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      position: fixed;
      left: 0;
      bottom: 15px;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0); /* 透明蒙层 */
      display: flex;
      justify-content: center;
      align-items: flex-end; /* 从底部对齐 */
      transition: transform 0.3s ease;
      transform: translateY(100%); /* 初始状态在视口外 */
      &.show {
          transform: translateY(0); /* 显示状态 */
      }
      @media screen and (orientation: landscape) {
        top: 0px;
        bottom: auto;
        right: 0px;
        left: auto;
        max-height: calc(var(--vh, 1vh) * 100);
        transform: translateX(100%);
        justify-content: flex-end;
        &.show {
          transform: translateX(0);
        }

      }
    `,
    container: css`
      background-color: white;
      width: 90%;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      @media screen and (orientation: landscape) {
        height: 100%;
        width: 100%;
        max-width: 225px;
        &.leftSizeEditor {
          position:absolute;
          left:0;
          top:0;
        }
      }
    `,
    title: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: semibold;
      font-size: 20px;
      line-height: 1.4;
      font-weight: 600;
      margin-bottom: 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      @media screen and (orientation: landscape) {
        font-size: 16px;
        margin-bottom: 35px;

      }
    `,
    geometricCheck:css`
      display:none;
      font-size:12px;
      line-height:20px;
      font-weight:500;
      margin-left:30px;
      input[type='radio'], input[type='checkbox'] {
        box-sizing: border-box;
        padding: 0;
      }
      span {
        position:relative;
        bottom:2px;
      }
      @media screen and (orientation: landscape) {
        display:inline;


      }
    `,
    resetBtn: css`
      border-radius: 12px;
      border: 1px solid #00000026;
      padding: 10px 20px;
      background-color: white;
      color: #282828;
      width: 92px;
      height: 24px;
      font-size: 12px;
      align-items: center;
      display: flex;
      justify-content: center;
      @media screen and (orientation: landscape) {
        width: 80px;
        height: 20px;
        font-size: 12px;
        padding: 3px 3px;
        position:absolute;
        left : 122px;
        top : 50px;
        margin-bottom:10px;

      }
    `,
    sliderContainer: css`
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      justify-content: space-between;
    `,
    slider: css`
        flex: 1;
        margin-right: 20px;
        margin-left: 20px;
        @media screen and (orientation: landscape) {
          margin-right: 0px;
          margin-left: 0px;
        }
    `,
    input: css`
        width: 124px;
        @media screen and (orientation: landscape) {
          width: 80px;
          font-size : 12px;
        }
    `,
  }
});
