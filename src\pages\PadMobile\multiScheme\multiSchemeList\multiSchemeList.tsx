import { LayoutSchemeData, LayoutSchemeService } from '@/Apps/LayoutAI/Services/Basic/LayoutSchemeService';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { EventName } from '@/Apps/EventSystem';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import Icon from '@/components/Icon/icon';
import { panoHost } from '@/config/host';
import { Empty, Image, message, Spin, Tooltip } from '@svg/antd';
import { t } from 'i18next';
import { observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import { LayoutSchemeMultResult, multiSchemeListService } from './multiSchemeList.service';
import useStyles from './style';
import IconFont from '@/components/IconFont/iconFont';
import { useStore } from '@/models';
import { useTranslation } from 'react-i18next';

// 最多只能关联5个方案 下面的6 因为还有原始方案
const MAX_INIT_SCHEME_COUNT = 6;
// 多方案列表
const MultiSchemeList: React.FC = () => {
    const store = useStore();
    const { t } = useTranslation();
    const { styles, cx } = useStyles();
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [currentSchemeId, setCurrentSchemeId] = useState<string>('');
    const [originalSchemeId, setOriginalSchemeId] = useState<string>('');
    const [originalSchemeName, setOriginalSchemeName] = useState<string>('');
    const layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const [schemeList, setSchemeList] = useState<LayoutSchemeMultResult[]>([]);

    useEffect(() => {
        let handlerVisibleChange = async (params: boolean) => {
            if (params == null || (params == visible)) {
                return;
            }
            setVisible(!!params);
            const currentId = layout_container._layout_scheme_id;
            setCurrentSchemeId(currentId);
        };
        // 监听页面显示状态
        LayoutAI_App.on(EventName.setMultiSchemeListVisible, handlerVisibleChange);

        let handlerLayoutChange = async (): Promise<void> => {
            const newSchemeId = layout_container._layout_scheme_id;
            if (!newSchemeId) {
                setVisible(false);
                return;
            }
            if (newSchemeId != currentSchemeId) {
                setCurrentSchemeId(newSchemeId);
            }
        };
        // 监听布局方案变化
        LayoutAI_App.on(EventName.RoomList, handlerLayoutChange);

        return () => {
            LayoutAI_App.off(EventName.setMultiSchemeListVisible);
            LayoutAI_App.off(EventName.RoomList);
        };
    });

    useEffect(() => {
        if (!visible) {
            setSchemeList([]);
            setOriginalSchemeId('');
            setOriginalSchemeName('');
            setCurrentSchemeId('');
        } else {
            fetchSchemeList(currentSchemeId);
        }
    }, [currentSchemeId, visible]);

    useEffect(() => {
        if (store.homeStore.showWelcomePage) {
            setVisible(false);
        }
    }, [store.homeStore.showWelcomePage]);


    // 获取当前方案关联的多方案
    const fetchSchemeList = async (curSchemeId: string) => {
        let currentScheme = await LayoutSchemeService.getLayoutSchemeById(curSchemeId);
        if (currentScheme?.projectId) {
            setOriginalSchemeId(currentScheme.projectId);
            await loadSchemeList(currentScheme.projectId);

        } else {
            setSchemeList([]);
        }
    };

    //带加载动画的加载方案列表
    const loadSchemeList = async (originSchemeId: string) => {
        setLoading(true);
        // 默认加载当前方案关联的多方案
        try {
            const list = await multiSchemeListService.loadSchemeList(originSchemeId);

            let scheme = await LayoutSchemeService.getLayoutSchemeById(originSchemeId);
            if (scheme) {
                // 补上原始方案
                let originScheme = {
                    id: scheme.id,
                    originSchemeId: scheme.projectId,
                    refSchemeId: scheme.projectId,
                    refSchemeName: scheme.layoutSchemeName,
                    imageUrl: new URL(scheme.coverImage).pathname.replace(/^\/+/, '')
                };
                setOriginalSchemeName(scheme.layoutSchemeName);
                list.push(originScheme);
            }
            setSchemeList(list);
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setVisible(false);
    };

    const handleDelete = async (id: string) => {
        let result = await multiSchemeListService.deleteScheme(id);
        if (result) {
            message.success({
                content: t('删除成功').toString(),
                style: { marginTop: '4vh' }
            });
            // 方案关联成功后，更新当前方案Id 同时会触发loadSchemeList
            let schemeId = layout_container._layout_scheme_id;
            fetchSchemeList(schemeId);
        } else {
            message.error({
                content: t('删除失败').toString(),
                style: { marginTop: '4vh' }
            });
        }
    };

    const handleAddScheme = async () => {
        if (schemeList.length >= MAX_INIT_SCHEME_COUNT) {
            message.warning({
                content: t('最多只能关联5个方案').toString(),
                style: { marginTop: '4vh' }
            });
            return;
        }
        if (!layout_container._layout_scheme_id) {
            message.warning({
                content: t('请先保存方案').toString(),
                style: { marginTop: '4vh' }
            });
            return;
        }
        if (layout_container._room_entities.length == 0) {
            message.warning({
                content: t('当前方案为空，无法保存！').toString(),
                style: { marginTop: '4vh' }
            });
            return;
        }
        // 如果原始方案Id为空，则使用当前方案Id
        let baseSchemeId = originalSchemeId || layout_container._layout_scheme_id;

        // 生成副本方案名称
        let schemeName = generateSchemeName();
        // 监听保存方案成功事件
        LayoutAI_App.on(EventName.SaveProgress, async (params: { progress: string, id?: string, name?: string }) => {
            if (params?.progress == 'ongoing') return;

            if (params?.progress == 'fail') {
                message.error({
                    content: t('方案保存失败').toString(),
                    style: { marginTop: '4vh' }
                });
                return;
            }

            let insertResult = await multiSchemeListService.insertScheme(baseSchemeId, params.id, params.name);
            if (insertResult) {
                message.success({
                    content: t('方案关联成功').toString(),
                    style: { marginTop: '4vh' }
                });
            } else {
                message.error({
                    content: t('方案关联失败').toString(),
                    style: { marginTop: '4vh' }
                });
            }
            // 方案关联成功后，更新当前方案Id 同时会触发loadSchemeList
            let schemeId = layout_container._layout_scheme_id;
            setCurrentSchemeId(schemeId);
        });
        let params: any = {
            schemename: schemeName,
            username: '',
            telephone: '',
            address: ''
        };
        await layout_container.saveSchemeLayout2JsonAs(params);
        LayoutAI_App.off(EventName.SaveProgress);


    };
    // 生成副本方案名称
    const generateSchemeName = () => {
        let schemeName = originalSchemeName || layout_container._layout_scheme_name;
        const schemeCount = schemeList.length > 0 ? schemeList.length : 1;
        // schemeList中还有原始方案
        return t('副本') + (schemeCount) + "-" + schemeName;
    };

    // 添加点击处理函数
    const handleSchemeClick = async (scheme: LayoutSchemeMultResult) => {

        LayoutAI_App.on(EventName.SaveProgress, async (params: { progress: string, id?: string, name?: string }) => {
            if (params?.progress == 'ongoing') {
                return;
            }
            if (params?.progress == 'fail') {
                message.error({
                    content: t('方案保存失败').toString(),
                    style: { marginTop: '4vh' }
                });
                LayoutAI_App.off(EventName.SaveProgress);
                return;
            }
            message.success({
                content: t('方案自动保存成功').toString(),
                style: { marginTop: '4vh' },
                duration: 1
            });
            let schemeData: LayoutSchemeData = await LayoutSchemeService.getLayoutSchemeById(scheme.refSchemeId);
            LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, schemeData);
            LayoutAI_App.off(EventName.SaveProgress);
        });
        await layout_container.saveSchemeLayout2Json();

    };

    if (!visible) return null;

    return (
        <div className={styles.schemeListContainer}>
            <div className={styles.title}>
                {t('布局方案')}
                <Icon
                    iconClass="iconclose1"
                    onClick={handleClose}
                    className={styles.closeIcon}
                />
            </div>
            <div className={styles.schemeList}>
                <Spin spinning={loading} size="large">
                    <div className={styles.schemeItem}>
                        <div
                            className={styles.addSchemeImage}
                            onClick={handleAddScheme}
                        >
                            <IconFont className={styles.addIcon} type="icon-tianjiasucai" />
                            <span className={styles.addSchemeText}>{t('新增方案')}</span>
                        </div>

                    </div>

                    {schemeList.length > 0 ? (
                        schemeList.map((scheme: LayoutSchemeMultResult) => (
                            <div
                                key={scheme.id}
                                className={styles.schemeItem}
                            >
                                <div
                                    className={cx(
                                        styles.schemeImage,
                                        {
                                            [styles.selectedSchemeImage]: scheme.refSchemeId === layout_container._layout_scheme_id
                                        }
                                    )}
                                    onClick={() => handleSchemeClick(scheme)}
                                    style={{ cursor: 'pointer' }}
                                >
                                    {scheme.imageUrl ? (
                                        <Image
                                            preview={false}
                                            src={new URL(scheme.imageUrl, panoHost).href}
                                            width={180}
                                            height={140}
                                            style={{
                                                objectFit: 'contain',
                                                padding: '10px',
                                                userSelect: 'none',
                                                pointerEvents: 'none',
                                                WebkitUserSelect: 'none',
                                                WebkitTouchCallout: 'none'
                                            }}
                                        />
                                    ) : (
                                        <Image
                                            width={180}
                                            height={140}
                                            src="error"
                                            style={{
                                                objectFit: 'contain',
                                                padding: '10px',
                                                userSelect: 'none',
                                                pointerEvents: 'none',
                                                WebkitUserSelect: 'none',
                                                WebkitTouchCallout: 'none'
                                            }}
                                        />
                                    )}
                                </div>
                                <div className={styles.schemeBottom}>
                                    <Tooltip title={scheme.refSchemeName}>
                                        <span className={`${styles.schemeName} ${styles.textEllipsis}`}>
                                            {scheme.refSchemeId == originalSchemeId ? t('原始方案') : scheme.refSchemeName}
                                        </span>
                                    </Tooltip>
                                    {scheme.refSchemeId != originalSchemeId && <Icon
                                        iconClass="icondelete"
                                        onClick={(e: any) => {
                                            e.stopPropagation();  // 阻止事件冒泡
                                            handleDelete(scheme.id);
                                        }}
                                        className={styles.deleteIcon}
                                    />}
                                </div>
                            </div>
                        ))
                    ) : (
                        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description={t('暂无关联方案')}
                        />
                    )}
                </Spin>
            </div>
        </div>
    );
};

export default observer(MultiSchemeList);
