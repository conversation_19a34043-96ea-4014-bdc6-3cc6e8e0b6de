import React, { useEffect, useState } from 'react';
import { Color, ColorPicker, Select, Slider } from '@svg/antd';
import useStyles from './SpaceAreaAttribute.style';
import { roomSubAreaService } from '@/Apps/LayoutAI/Services/Basic/RoomSubAreaService';
import { AI_PolyTargetType, IRoomSpaceAreaType, RoomSpaceAreaType } from '@/Apps/LayoutAI/Layout/IRoomInterface';
import { useStore } from '@/models';
import { LayoutPopEvents } from '@/pages/LightMobile/layoutPopup/layoutPopup';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { TSubSpaceAreaEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity';

// 空间分区pad端属性面板
const SpaceAreaAttribute: React.FC = ({  }) => {
    const { styles } = useStyles();
    const store = useStore();
    // 状态管理
    const [spaceAreaType, setSpaceAreaType] = useState<string>('客厅区');
    const [color, setColor] = useState<string>('#FFA500');
    const [options, setOptions] = useState<any[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [subArea, setSubArea] = useState<TSubSpaceAreaEntity | null>(null);

    useEffect(() => {
        let roomSubAreaMenuData = roomSubAreaService.getRoomSubAreaMenuData();
        let options = roomSubAreaMenuData.map((item: any) => {
            return {
                value: item.id,
                label: item.text
            }
        })
        setOptions(options)
        LayoutAI_App.on_M(LayoutPopEvents.showPopup, 'SpaceAreaAttribute', (popupType) => {
            if(popupType !== 'SpaceAreaAttribute' && visible){
                setVisible(false)
                return;
            }
            if(store.homeStore.selectEntity?.type === AI_PolyTargetType.RoomSubArea){
                setVisible(true);
                let subArea = store.homeStore.selectEntity as TSubSpaceAreaEntity;
                setSubArea(subArea);
                let spaceAreaType = subArea.space_area_type;
                let text = options.find((item: any) => item.value === spaceAreaType);
                setSpaceAreaType(text);
                setColor(subArea.color_style);
            }else{
                setVisible(false)
            }
        });
    }, [visible])
    
    // 处理选择分类变化
    const handleCategoryChange = (value: string) => {
        setSpaceAreaType(value);
        if(subArea && value in RoomSpaceAreaType){
            LayoutAI_App.DispatchEvent(LayoutAI_Events.UpdateRoomSubAreaType, value as IRoomSpaceAreaType);
        }
    };

    // 处理颜色变化
    const onColorChangeComplete = (color: Color) => {
        setColor(color.toHexString());
        if(subArea){
            roomSubAreaService.updateSubAreaColor(subArea, color.toHexString());
            LayoutAI_App.instance.update();
        }
    }

    // 删除分区
    const onDeleteSubArea = () => {
        if(subArea){
            LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteFurniture);
        }
    }
    return (visible &&
        <div className={styles.container}>

            <div className={styles.formItem}>
                <div className={styles.label}>分区类型</div>
                <Select
                    className={styles.select}
                    value={spaceAreaType}
                    onChange={handleCategoryChange}
                    options={options}
                />
            </div>

            <div className={styles.formItem}>
                <div className={styles.colorRow}>
                    <span className={styles.label}>颜色</span>
                    <ColorPicker
                        value={color}
                        onChangeComplete={onColorChangeComplete}
                        
                    />
                </div>
            </div>

            <div className={styles.footer}>
                <button className={styles.footerButton} onClick={onDeleteSubArea}>
                    删除分区
                </button>
            </div>
        </div>
    );
};

export default SpaceAreaAttribute;
