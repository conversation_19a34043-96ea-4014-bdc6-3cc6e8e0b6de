
import { TRoom } from "../../../../TRoom";
import { TGroupTemplate } from "../../../TGroupTemplate/TGroupTemplate";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { I_OnLineFigureInterval } from "../../TLayoutOptimizer/TLayoutOptimizer";
import { WPolygon } from "../../../../TFeatureShape/WPolygon";
import {  TGraphBasicConfigs } from "../../../TGraphBasicConfigs";
import { KitchenAreaType } from "../../../TGraphConfigureInterface";

import { TSimpleRoomPartionGraph } from "../TSimpleRoomPartitionGraph";

import { TLayoutOptmizerOnWallRules } from "../../TLayoutOptimizer/TLayoutOptimizerOnWallRules";
import { compareNames, range_substract } from "@layoutai/z_polygon";
import { TPostProcessLayout } from "../../TLayoutOptimizer/TPostProcessLayout";

import { TKitchenPartRelation } from "./TKitchenPartRelation";
import { TRoomShape } from "../../../../../../LayoutAI/Layout/TRoomShape";
import { LayoutAI_Configs } from "@/Apps/LayoutAI/Layout/TLayoutEntities/configures/LayoutAIConfigs";
import { DefaultKitchenLayoutRules } from "../../../TGraphConfigs/DefaultKitchenLayoutRules";
import { MainGroupFigureConfigs } from "../../../TGraphConfigs/MainGroupFigureConfigs";
import { FigureZValRangeType } from "@/Apps/LayoutAI/Layout/IRoomInterface";

interface I_MainRectSubEdges {
    /**
     *  主矩形的边id
     */
    main_rect_edge_id: number;

    /**
     *  主矩形上的边
     */
    r_edge: ZEdge;

    /**
     *   WPoly上的最长边
     */
    t_edge: ZEdge;
    /**
     *   裁剪后 剩下的边长
     */
    sub_edges: ZEdge[];
    /**
     *  裁剪后 的剩余长度
     */
    sum_length: number;
    /**
     *  是否有窗
     */
    has_window: boolean
};
const AttachedHangingEdge = "AttachedHangingEdgeS";
const ExPropEdgeCornerState = "ExPropEdgeCornerState";

enum EdgeCornerState
{
    None = 0,
    PrevCorner = 1,
    NextCorner = 2,
    StartCorner = 4,
    EndCorner = 8
}
export class TKitchenPart2Relation extends TKitchenPartRelation {

    _configs = {
        floor_cabinet_depth : 580,
        hanging_cabinet_depth : 375
    }
    
    constructor() {
        super();
    }


    get graph() {
        return this._graph as any as TSimpleRoomPartionGraph;
    }

    precompute(): void {

        /**
         *  计算前, 默认配置预处理
         */
        if(LayoutAI_Configs.Configs.kitchen_perfer_high_cabient)
        {
            DefaultKitchenLayoutRules["白电区"] = {
                suitable_size: { length: 830, depth: 600 },
                initial_rules: [
                    {
                        main_rect_edge_id: 1, // 一般是带窗侧
                        group_array: [
                            {
                                group_space_category: "冰箱高柜",
                                length: 830,
                                depth: 600,
                                zero_differ_weight:100000                                
                            },{
                                group_space_category: "单门地柜",
                                length: 300,
                                depth: 600
                            },                    
                            {
                                group_space_category: "双门地柜",   //等价碗碟收纳
                                length: 600,
                                depth: 560
                            },
                            {
                                group_space_category: "单门地柜",
                                length: 300,
                                depth: 600
                            }
                        ]
                    }
                ]
            }
            DefaultKitchenLayoutRules["烹饪区"] = {
                
                    suitable_size: { length: 800, depth: 900 },
                    initial_rules: [
                        {
                            main_rect_edge_id: 0,
                            window_check: -1, // 不可以有窗
                            group_array: [
                                {
                                    group_space_category: "单门地柜",   // 碗碟收纳
                                    length: 350,
                                    depth: 560,
                                },
                                {
                                    group_space_category: "炉灶地柜",   // 碗碟收纳
                                    length: 1000,
                                    depth: 560,
                                    zero_differ_weight: 100000,
                                },
                                {
                                    group_space_category: "微波炉地柜",
                                    length: 600,
                                    depth: 560,
                                    zero_differ_weight: 100,
            
                                },
                                {
                                    group_space_category: "单门地柜",   // 碗碟收纳
                                    length: 300,
                                    depth: 560,
                                    
                                },
                                {
                                    group_space_category: "吊柜",
                                    length: 200,
                                    depth: 375,
                                    start_val: '0',
            
                                },
                                {
                                    group_space_category: "烟机吊柜",
                                    length: 900,
                                    depth: 375,
                                    zero_differ_weight: 100000,
                                },
                                {
                                    group_space_category: "吊柜",
                                    length: 200,
                                    depth: 375,
            
                                }
                            ]
                        }
                    ]
            }
            MainGroupFigureConfigs["厨房"]["烟机吊柜"] = {
                main_figure_names: ["烟机吊柜"],
                main_figure_category :"吊柜-烟机吊柜(侧吸)",
                main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
                group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
                sub_figure_names: [],
                backwall_max_dist: 1500,
                opt_rules: [
                    {
                        ruleType: "BackToWall",
                        params: {
                            adsorb_back_target_dist: '' + 1000, // 如果背靠区域小于600, 才会执行这个优化
                            target_back_dist_val: '0'    // 目标的贴墙距离为0
                        }
                    }
                ],
                group_length_levels: {
                    values: [
                        900
                    ]
                },
                differ_weight: 0.5,
                place_order: 1,
                priority: 3,
                zval_range_type: FigureZValRangeType.OnHalfWall
    
            }
        }
        else{
            DefaultKitchenLayoutRules["白电区"] = {
                suitable_size: { length: 1000, depth: 1200 },
                initial_rules: [
                    {
                        main_rect_edge_id: 1, // 一般是带窗侧
                        group_array: [
                            {
                                group_space_category: "冰箱",
                                length: 1000,
                                depth: 800
                            }
                        ]
                    }
                ]
            }
            DefaultKitchenLayoutRules["烹饪区"] = {
                
                suitable_size: { length: 800, depth: 900 },
                initial_rules: [
                    {
                        main_rect_edge_id: 0,
                        window_check: -1, // 不可以有窗
                        group_array: [
                            {
                                group_space_category: "单门地柜",   // 碗碟收纳
                                length: 350,
                                depth: 560,
                            },
                            {
                                group_space_category: "炉灶地柜",   // 碗碟收纳
                                length: 1000,
                                depth: 560,
                                zero_differ_weight: 100000,
                            },
                            {
                                group_space_category: "单门地柜",   // 碗碟收纳
                                length: 300,
                                depth: 560,
                                
                            },
                            {
                                group_space_category: "吊柜",
                                length: 200,
                                depth: 375,
                                start_val: '0',
        
                            },
                            {
                                group_space_category: "烟机吊柜",
                                length: 800,
                                depth: 375,
                                zero_differ_weight: 100000,
                            },
                            {
                                group_space_category: "吊柜",
                                length: 200,
                                depth: 375,
        
                            }
                        ]
                    }
                ]
        }
        }

        const kitchen_perfer_high_cabient = LayoutAI_Configs.Configs.kitchen_perfer_high_cabient || false;
        
        /**
         *  0. 先用默认的相似样板间匹配的手段
         */
        this._candidate_figure_list = [];
        // super.precompute();


        // 分成地柜 和 吊柜两种情况处理
        // 1、先把墙 拆成线段

        let w_poly = this._room.room_shape._feature_shape._w_poly;

        let basic_edge = w_poly.edges[0]; // 初始edge

        let main_rect = this._room.max_R_shape.getRect();

        /**
         *  如果包含最大L形, 则使用最大L形最大包围盒矩形
         */
        if (this._room.max_L_shape) {
            let bbox = this._room.max_L_shape._poly.computeBBox();

            let rect = ZRect.fromBox3(bbox, main_rect.nor);
            rect.u_dv = main_rect.dv;
            rect.updateRect();
            main_rect = rect;

        }
        let t_edge_list :I_MainRectSubEdges[] = [];

        let n_edge = basic_edge;

        let pillar_len = 450;
        let gap_len = 1200;
        let hallway_depth = 900 + 600 * 2;
        // 厨房的顺序, 一般0是最长边
        for (let i = 0; i <= 3; i++) {
            let ii = i;
            if (ii == 3) ii = -1;
            let r_edge = main_rect.edges[(ii + main_rect.edges.length -1) % main_rect.edges.length];

        
            let t_edge : ZEdge = null;
            let sub_edges : ZEdge[] = [];
            for(let edge of w_poly.edges)
            {
                if(!r_edge.checkSameNormal(edge.nor,false)) continue;

                let win = WPolygon.getWindowOnEdge(edge);
                if(win && win.type === "Door") continue;
                let t_dist = Math.abs(edge.projectEdge2d(r_edge.center).y);

                if(t_dist <= gap_len && edge.length > pillar_len)
                {
                    sub_edges.push(edge);
                }
            }

            let sum_length = 0;
            for(let edge of sub_edges)
            {
                sum_length += edge.length;
                if(!t_edge || edge.length > t_edge.length) t_edge = edge;
            }

            sub_edges.sort((a,b)=>r_edge.projectEdge2d(a.center).x - r_edge.projectEdge2d(b.center).x);
            t_edge_list.push({
                main_rect_edge_id: ii,
                r_edge: main_rect.edges[(ii + main_rect.edges.length - 1) % main_rect.edges.length],
                t_edge: t_edge,
                sub_edges: sub_edges,
                sum_length: sum_length,
                has_window: WPolygon.getWindowOnEdge(t_edge) !== null
            })
        }

        // 三点定位法, 转角|炉灶|水槽
        // 约束: 
        //   1、转角柜的中心 离上一面墙要留出 580的举例 (有一半的门要开)
        //   2、炉灶要远离窗的中心（因为要放烟机吊柜)
        //   3、水槽要接近窗的中心

        //  三种大策略: 一般转角柜| 转角水槽柜 | 转角灶具柜 --- 先考虑一般转角柜

  
        let all_intervals :{[key:string]: I_OnLineFigureInterval} = {};

        for(let key in TGraphBasicConfigs.MainGroupFigureConfigs["厨房"])
        {
            all_intervals[key]=(TLayoutOptmizerOnWallRules.make_interval_of_group_category(key, "厨房"));
        }

        let target_intervals : I_OnLineFigureInterval[] = [];



        let has_flue = this._room.flues && this._room.flues.length > 0;
        // 四布局方式: I字型、II字型、L型、U型
            // 每种的可能性有 正向、逆向

        // 厨房布局的准出和评分方式
           //  

        let strategies: {
            name: string;
            init_rules: {
                area_array ?: KitchenAreaType[],
                wall_id?: number,
                margin_end?: number,  // 末尾留白
                margin_start?: number,  // 开头留白
                hanging_margin_end?:number;
                hanging_margin_start?:number;
                alignment ?: "align_left"|"align_right"|"align_center",
                u_dv_flag ?: number;
                corner_area ?: "转角水槽"|"转角炉灶",
                need_add_boards ?: "default"|"left-side"|"right-side"|"none",
                auto_filling ?: boolean;
                min_depth ?: number,
                min_length ?: number,
                corner_state ?: number
            }[]
        }[] = [
            {
                name:"L型-1-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["盲柜区","水槽区","主操作区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_start:0,
                        alignment: "align_right",
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","烹饪区"],
                        margin_start : this._configs.floor_cabinet_depth,
                        margin_end : 0,
                        hanging_margin_start : this._configs.hanging_cabinet_depth + 20,
                        need_add_boards :"left-side",
                        corner_area : "转角炉灶"
                    },
                    {
                        wall_id : 3,
                        area_array:
                            ["白电区"],
                        margin_start : 0,
                        auto_filling : false,
                        margin_end :  kitchen_perfer_high_cabient? this._configs.floor_cabinet_depth:800,
                        need_add_boards : kitchen_perfer_high_cabient?"left-side":"none",
                        hanging_margin_start:0,
                        hanging_margin_end:this._configs.hanging_cabinet_depth + 20,
                        alignment: "align_left",
                        min_depth : 1300,  
                    }
                ],  
            },
            {
                name:"L型-1-1",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","主操作区","水槽区"],
                        margin_start :  0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_start:0,
                        hanging_margin_end:this._configs.hanging_cabinet_depth + 20,
                        alignment: "align_left",
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","烹饪区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end : 0,
                        need_add_boards :"left-side",
                        corner_area : "转角炉灶"
                    }
                ],  
            },
            {
                name:"L型-2-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["盲柜区","烹饪区","主操作区"],
                        margin_start : this._configs.floor_cabinet_depth,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_start:0,
                        hanging_margin_end:this._configs.hanging_cabinet_depth + 20,
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","水槽区"],
                        margin_start : 600,
                        margin_end : 0,
                        corner_area : "转角水槽"

                    },
                    {
                        wall_id : 3,
                        area_array:
                            ["白电区"],
                        margin_start : 0,
                        margin_end : kitchen_perfer_high_cabient? this._configs.floor_cabinet_depth:1000,
                        hanging_margin_start:0,
                        hanging_margin_end:this._configs.hanging_cabinet_depth + 20,
                        auto_filling:false,
                        need_add_boards :"left-side",
                        alignment: "align_left",
                        min_depth : 1300,          
                    }
                ],
                
            },
            {
                name:"L型-2-1",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","主操作区","烹饪区"],
                        margin_start : 0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_start:0,
                        hanging_margin_end:this._configs.hanging_cabinet_depth + 20,
                        need_add_boards:"right-side"
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","水槽区"],
                        margin_start : this._configs.floor_cabinet_depth,
                        margin_end : 0,
                        corner_area : "转角水槽"

                    }
                ],
                
            },
            {
                name:"L型-2-2",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","主操作区","烹饪区"],
                        margin_start : 0,
                        margin_end : this._configs.floor_cabinet_depth,
                        min_length : 3450
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","水槽区"],
                        margin_start : 600,
                        margin_end : 0,
                        corner_area : "转角水槽"

                    }
                ],
                
            },
            {
                name:"U型-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["盲柜区","主操作区","水槽区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end : this._configs.floor_cabinet_depth,
                        
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","辅助操作区","白电区"],
                        margin_start : this._configs.floor_cabinet_depth,
                        margin_end : 0,
                        hanging_margin_start:this._configs.hanging_cabinet_depth + 20,
                        hanging_margin_end:0,
                        need_add_boards:"none",
                        corner_area : "转角水槽"

                    },
                    {
                        wall_id : 3,
                        area_array:
                            ["辅助操作区","烹饪区"],
                        margin_start : 0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_start:0,
                        hanging_margin_end:this._configs.hanging_cabinet_depth + 20,
                    }
                ],
                
            },
            {
                name:"U型-1",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["盲柜区","主操作区","烹饪区"],
                        margin_start :  600,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_start:0,
                        hanging_margin_end:0,
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","辅助操作区","白电区"],
                        margin_start : 600,
                        hanging_margin_start : this._configs.hanging_cabinet_depth+20,
                        margin_end : 0,
                        need_add_boards:"none",
                        corner_area : "转角水槽"

                    },
                    {
                        wall_id : 3,
                        area_array:
                            ["辅助操作区","水槽区"],
                        margin_start : 0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_start:0,
                        hanging_margin_end:this._configs.hanging_cabinet_depth + 20,
                    }
                ],
                
            },
            {
                name:"U型-1-1",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","主操作区","水槽区"],
                        margin_start :  0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_end : this._configs.hanging_cabinet_depth+20,
                        alignment: "align_left",
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","辅助操作区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end :  this._configs.floor_cabinet_depth,
                        need_add_boards :"right-side",
                        corner_area : "转角炉灶"
                    },
                    {
                        wall_id : 2,
                        area_array:
                            ["盲柜区","烹饪区","辅助操作区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        hanging_margin_start : this._configs.hanging_cabinet_depth+20,
                        margin_end : 0,
                    }
                ],  
                
            },
            {
                name:"U型-1-2",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","辅助操作区","烹饪区"],
                        margin_start :  0,
                        margin_end : this._configs.floor_cabinet_depth,
                        alignment: "align_left",
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","辅助操作区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end :  this._configs.floor_cabinet_depth,
                        need_add_boards :"right-side",
                        corner_area : "转角炉灶"
                    },
                    {
                        wall_id : 2,
                        area_array:
                            ["盲柜区","水槽区","主操作区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end : 0,
                    }
                ],  
                
            },            
            {
                name:"U型-1-3",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","辅助操作区","烹饪区"],
                        margin_start :  0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_end:this._configs.hanging_cabinet_depth+20,
                        alignment: "align_left",
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","水槽区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end :  this._configs.floor_cabinet_depth,
                        need_add_boards :"right-side",
                        corner_area : "转角炉灶"
                    },
                    {
                        wall_id : 2,
                        area_array:
                            ["盲柜区","主操作区","辅助操作区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end : 0,
                    }
                ],  
                
            },
            {
                name:"I型-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","水槽区","主操作区","烹饪区"],
                        margin_start : 0,
                        margin_end : 0,
                        need_add_boards:"right-side"
                    }
                ],
                
            },
            {
                name:"II型-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","水槽区","主操作区"],
                        margin_start : 0,
                        margin_end : 0,
                        auto_filling:true,
                        need_add_boards:"right-side"
                    },
                    {
                        wall_id : 2,
                        area_array:
                            ["烹饪区","辅助操作区"],
                        margin_start : 0,
                        margin_end : 0,
                        auto_filling:true,
                        alignment:"align_left"
                    },
                ],
                
            },
            {
                name:"II型-1",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["白电区","主操作区"],
                        margin_start : 0,
                        margin_end : 0,
                        auto_filling:true,
                        need_add_boards:"right-side"
                    },
                    {
                        wall_id : 2,
                        area_array:
                            ["水槽区","烹饪区"],
                        margin_start : 0,
                        margin_end : 0,
                        alignment:"align_left"
                    },
                ],
                
            },
            {
                name:"II型-2",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                        ["白电区","辅助操作区","烹饪区"],
                        margin_start : 0,
                        margin_end : 0,
                        auto_filling:true,
                        need_add_boards:"right-side"
                    },
                    {
                        wall_id : 2,
                        area_array:
                            ["水槽区","主操作区"],
                        margin_start : 0,
                        margin_end : 0,
                        alignment:"align_left"
                    },
                ],
                
            },
            // 无冰箱布局
            {
                name:"L型[无冰箱]-1-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["辅助操作区","水槽区","主操作区"],
                        margin_start :  0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_end : this._configs.hanging_cabinet_depth+20,
                        alignment: "align_right",
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","烹饪区"],
                        margin_start : this._configs.floor_cabinet_depth,
                        margin_end : 0,
                        need_add_boards :"left-side",
                        corner_area : "转角炉灶"
                    }
                ],  
            },
            {
                name:"L型[无冰箱]-1-1",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["主操作区","水槽区"],
                        margin_start :  0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_end : this._configs.hanging_cabinet_depth+20,
                        alignment: "align_left",
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","烹饪区"],
                        margin_start :  this._configs.floor_cabinet_depth,
                        margin_end : 0,
                        need_add_boards :"left-side",
                        corner_area : "转角炉灶"
                    }
                ],  
            },
            {
                name:"L型[无冰箱]-2-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["烹饪区","主操作区"],
                        margin_start :0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_start:0,
                        hanging_margin_end:this._configs.hanging_cabinet_depth + 20,
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","水槽区"],
                        margin_start : 600,
                        margin_end : 0,
                        corner_area : "转角水槽"

                    }
                ],
                
            },
            {
                name:"L型[无冰箱]-2-1",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["主操作区","烹饪区"],
                        margin_start : 0,
                        margin_end : this._configs.floor_cabinet_depth,
                        hanging_margin_end:this._configs.hanging_cabinet_depth+20
                    },
                    {
                        wall_id : 1,
                        area_array:
                            ["盲柜区","水槽区"],
                        margin_start : this._configs.floor_cabinet_depth,
                        margin_end : 0,
                        corner_area : "转角水槽"

                    }
                ],
                
            },
            {
                name:"I型[无冰箱]-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["水槽区","主操作区","烹饪区"],
                        margin_start : 0,
                        margin_end : 0,                    
                    }
                ],
                
            },
            {
                name:"II型[无冰箱]-0",
                init_rules : [
                    {
                        wall_id : 0,
                        area_array:
                            ["主操作区","水槽区"],
                        margin_start : 0,
                        margin_end : 0,
                    },
                    {
                        wall_id : 2,
                        area_array:
                            ["烹饪区"],
                        margin_start : 0,
                        margin_end : 0,
                        alignment:"align_left"
                    },
                ],
                
            },
            {
                name:"II型[无冰箱]-3",
                init_rules : [
                    {
                        wall_id : 1,
                        area_array:
                            ["烹饪区"],
                        margin_start : 0,
                        margin_end : 0,
                    },
                    {
                        wall_id : 3,
                        area_array:
                            ["水槽区","主操作区"],
                        margin_start : 0,
                        margin_end : 0,
                        alignment:"align_left"
                    },
                ],
                
            }
        ];

        let inv_strategies = [];
        for(let strategy of strategies)
        {
            let name = strategy.name+"^"; 
            let init_rules = strategy.init_rules;
            let target_rules = [];

            for(let rule of init_rules)
            {
                if(!rule.need_add_boards)
                {
                    rule.need_add_boards ="default";
                }
                let t_rule = {...rule};
                t_rule.area_array = [];
                let wall_id = rule.wall_id;
                if(wall_id !== 1 && wall_id !== 3)
                {
                    wall_id = (wall_id + 4 + 2) % 4;

                }
                t_rule.wall_id = wall_id;
                for(let i in rule.area_array)
                {
                    t_rule.area_array.push(rule.area_array[rule.area_array.length-1-~~i]);
                }
                if(t_rule.alignment === "align_right")
                {
                    t_rule.alignment = "align_left";
                }
                else{
                    t_rule.alignment = "align_right";
                }

                let tmp = t_rule.margin_start; t_rule.margin_start = t_rule.margin_end; t_rule.margin_end = tmp;
                if(t_rule.hanging_margin_start || t_rule.hanging_margin_end)
                {
                    t_rule.hanging_margin_start = t_rule.hanging_margin_start || 0;
                    t_rule.hanging_margin_end = t_rule.hanging_margin_end || 0;

                    let tmp = t_rule.hanging_margin_start; t_rule.hanging_margin_start = t_rule.hanging_margin_end; t_rule.hanging_margin_end = tmp;
                }
                target_rules.push(t_rule);
            }
            inv_strategies.push({
                name : name,
                init_rules : target_rules
            });            
        }
        strategies.push(...inv_strategies);

        for(let strategy of strategies)
        {
            for(let rule of strategy.init_rules)
            {
                if(rule.need_add_boards =="none") continue;

                let corners = rule.area_array.filter((val)=>val==="盲柜区");
                let ices = rule.area_array.filter((val)=>val==="白电区");
                let conter_count = corners.length + ices.length;
                if(conter_count>= 2)
                {
                    rule.need_add_boards = "none";
                }
                else if(conter_count== 0)
                {
                    rule.need_add_boards = "default";
                }
                else if(corners.length==1)
                {
                    let id = rule.area_array.indexOf(corners[0]);

                    if(id==0)
                    {
                        rule.need_add_boards = "right-side";
                    }
                    else{
                        rule.need_add_boards = "left-side";
                    }
                }
                else if(ices.length == 1)
                {
                    let id = rule.area_array.indexOf(ices[0]);

                    if(id==0)
                    {
                        rule.need_add_boards = "right-side";
                    }
                    else{
                        rule.need_add_boards = "left-side";
                    }
                }

                

            }
        }
        for(let strategy of strategies)
        {
            let prev_corner_wall_id_dict : {[key:number]:number} = {};
            let next_corner_wall_id_dict : {[key:number]:number} = {};
            for(let rule of strategy.init_rules)
            {
                rule.corner_state = EdgeCornerState.None;

                let corner_id = rule.area_array.indexOf("盲柜区");
                if(corner_id >= 0)
                {
                    if(corner_id == 0)
                    {
                        rule.corner_state = EdgeCornerState.StartCorner;
                        let wall_id = (rule.wall_id - 1 + 4) % 4;
                        next_corner_wall_id_dict[wall_id] = 1;
                    }
                    else{
                        rule.corner_state = EdgeCornerState.EndCorner;
                        let wall_id = (rule.wall_id + 1 + 4) % 4;
                        prev_corner_wall_id_dict[wall_id] = 1;
                    }
                }
            }
            for(let rule of strategy.init_rules)
            {
                let wall_id = (rule.wall_id + 4) % 4;

                if(prev_corner_wall_id_dict[wall_id])
                {
                    rule.corner_state = rule.corner_state | EdgeCornerState.PrevCorner;
                }

                if(next_corner_wall_id_dict[wall_id])
                {
                    rule.corner_state = rule.corner_state | EdgeCornerState.NextCorner;
                }
            }
        }
        // console.log(t_edge_list);
        for(let strategy of strategies)
        {
            let name = strategy.name;
            let result_edge_areas :  {edge:ZEdge, areas:KitchenAreaType[], intervals:I_OnLineFigureInterval[],hanging_intervals?:I_OnLineFigureInterval[],sum_length?:number,edge_length?:number}[] = [];
            for(let rule of strategy.init_rules)
            {
                let sub_edge_data = t_edge_list[rule.wall_id];
                let margin_start = rule.margin_start || 0;
                let margin_end = rule.margin_end || 0;


                let r_edge = sub_edge_data.r_edge;  // 暂时用最大矩形来标识墙距
                let valid_sub_edges : ZEdge[] = [];

                if(rule.min_depth && main_rect.min_hh < rule.min_depth)
                {
                    continue;
                }
                for(let ei in sub_edge_data.sub_edges)
                {
                    let edge = sub_edge_data.sub_edges[ei];

                    let t_edge = edge._deep_clone();

                    let ll = r_edge.projectEdge2d(t_edge.v0.pos).x;
                    let rr = r_edge.projectEdge2d(t_edge.v1.pos).x;

                    for(let door of this._room.windows)
                    {
                        if(door.type === "Door" && door.rect && door.rect.w < 1500)
                        {
                            if(door.rect.checkSameDirection(r_edge.nor.clone()))
                            {
                                
                                let pp0 = r_edge.projectEdge2d(door.rect.leftEdge.center);
                                let pp1 = r_edge.projectEdge2d(door.rect.rightEdge.center);

                                let tp0 = pp0;

                                if(Math.abs(pp0.y)>Math.abs(pp1.y))
                                {
                                    tp0 = pp1;
                                }
                                if(Math.abs(tp0.y) < 200)
                                {
                                    let door_offset = 600;
                                    if(tp0.x > r_edge.length / 2)
                                    {
                                        rr =Math.min(tp0.x-door.rect.h/2 - door_offset, rr);
                                    }
                                    else{
                                        ll = Math.max(tp0.x+door.rect.h/2+door_offset,ll);
                                    }
                                }
                            }
                        }
                    }

                    let floor_ll = Math.max(ll,margin_start);
                    let floor_rr = Math.min(rr, r_edge.length - margin_end);

                    if(floor_rr < floor_ll + 100) continue;
                    let corner_state = this.checkEdgeCornerState( rule.corner_state || 0, ~~ei, sub_edge_data.sub_edges.length);





                    let y = r_edge.projectEdge2d(t_edge.center).y;


                    let floor_edge = r_edge.makeSubEdge(floor_ll,floor_rr,y);
                    let door_board_offset = 20;
                    let hanging_margin_start = rule.hanging_margin_start||0;
                    let hanging_margin_end =  rule.hanging_margin_end||0;

                    let hanging_ll = Math.max(ll, hanging_margin_start);
                    let hanging_rr = Math.min(rr, r_edge.length - hanging_margin_end);

                    let hanging_edge = r_edge.makeSubEdge(hanging_ll,hanging_rr,y);

                    floor_edge.bindEdge(AttachedHangingEdge,hanging_edge);
                    floor_edge._ex_props[ExPropEdgeCornerState] = corner_state;


                    if(floor_edge.length > 450)
                    {
                        valid_sub_edges.push(floor_edge);
                    }

                }

                let total_length = 0;
                valid_sub_edges.forEach((val)=>total_length+=val.length);

                valid_sub_edges.forEach((edge)=>{
                    TRoomShape.initWindowOfEdge(edge);

                    for(let win of this._room.windows)
                    {
                        if(!win.rect) continue;
                        if(win.type !== "Window") continue;
                        if(!edge.checkSameNormal(win.rect.nor)) continue;
                        

                        let pp = edge.projectEdge2d(win.rect.rect_center);
                        if(pp.x + win.rect.w/2 + 100 < 0 || pp.x - win.rect.w/2 -100 > edge.length) continue;
                        if(Math.abs(pp.y) > 600) continue;
                        TRoomShape.pushWindowToEdge(edge,win);
                    }
                });
                if(rule.min_length && total_length < rule.min_length)
                {
                    continue;
                }

                /**
                 *  queue 排列组合 线段 和 分区的可能
                 */
                let queue : {
                    total_length : number,
                    fit_error ?:number,
                    edge_areas: {edge:ZEdge, areas:KitchenAreaType[], intervals:I_OnLineFigureInterval[],hanging_intervals?:I_OnLineFigureInterval[], sum_length?:number,edge_length?:number}[]
                }[] = [];

   
                let area_dict :{[key:string]:number} = {};


                let visit_area_rule = (area_id:number, last_id:number) =>{
                    if(area_id === rule.area_array.length)
                    {
                        let edge_areas : {edge:ZEdge, areas:KitchenAreaType[], intervals:I_OnLineFigureInterval[]}[] = [];
                        for(let edge of valid_sub_edges)
                        {
                            edge_areas.push({
                                edge:edge,
                                areas : [],
                                intervals: []
                            });
                        }
                        for(let key in area_dict)
                        {
                            let e_id = area_dict[key];
                            edge_areas[e_id].areas.push(key as any);

                        }

                        queue.push({
                            fit_error:0,
                            total_length : 0,
                            edge_areas :edge_areas
                        })
                        return;
                    }

                    let area_name = rule.area_array[area_id];

                    for(let id=last_id; id<valid_sub_edges.length; id++)
                    {
                        area_dict[area_name] = id;

                        visit_area_rule(area_id+1, id);
                    }
                    
                }
                visit_area_rule(0,0);

                for(let q_data of queue)
                {
                    let sum_length = 0;
                    for(let edge_area of q_data.edge_areas)
                    {
                        if(edge_area.areas.length == 0) continue;

                        let intervals : I_OnLineFigureInterval[] = [];
                        if(rule.need_add_boards ==="default" || rule.need_add_boards=="left-side")
                        {
                            intervals.push( TLayoutOptmizerOnWallRules.make_interval_of_group_category("地柜收口板", "厨房"));

                        }
                        for(let area_name of edge_area.areas)
                        {
                            let u_dv_flag = 1;
                            if(compareNames([area_name],["盲柜","水槽","主操作区"])>0) // 对转角地柜进行处理
                            {
                                let a_id = rule.area_array.indexOf(area_name);
                                if(a_id  >= Math.floor(rule.area_array.length/2))
                                {
                                    u_dv_flag = 1;
                                }
                                else{
                                    u_dv_flag = -1;
                                }
                            }

                            let config = TGraphBasicConfigs.KitchenLayoutRules[area_name];
                            let sub_intervals : I_OnLineFigureInterval[] = [];
                            for(let  sub_rule of config.initial_rules[0].group_array)
                            {
                                if(sub_rule.group_space_category.indexOf("吊柜")>=0) continue;
                      
                                let interval = TLayoutOptmizerOnWallRules.make_interval_of_group_category(sub_rule.group_space_category, "厨房");
                            
                                sub_intervals.push(interval);
                                interval.u_dv_flag = u_dv_flag;

       
                            }
                            if(u_dv_flag < 0)
                            {
                                for(let i=0; i < sub_intervals.length; i++)
                                {
                                    intervals.push(sub_intervals[sub_intervals.length-1-i]);
                                }
                            }
                            else{
                                intervals.push(...sub_intervals);
                            }
                            sub_intervals = null;

                        }
                        if(rule.need_add_boards ==="default" || rule.need_add_boards=="right-side")
                        {
                            let board_interval = TLayoutOptmizerOnWallRules.make_interval_of_group_category("地柜收口板", "厨房");
                            board_interval.u_dv_flag = -1;
                            intervals.push( board_interval);
                        }

                        let t_length = edge_area.edge.length;
                        t_length = Math.floor(t_length/10) * 10;
                        TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(intervals,t_length,true,10);
                        // this.adjustIntervals(edge_area.edge,intervals);
                        

   
                        edge_area.intervals = [...intervals];
                        edge_area.sum_length = 0;
                        edge_area.edge_length = edge_area.edge.length;
                        intervals.forEach((val)=>{sum_length+=val.result_length; edge_area.sum_length += val.result_length;});

                        let offset_len =edge_area.edge.length - edge_area.sum_length;

            
                        if(rule.alignment === "align_right")
                        {
                            intervals.forEach((val)=>val.center_x+=offset_len);
                        }
                    }
                    q_data.fit_error = total_length - sum_length;
                    q_data.total_length = sum_length;


    
                    

                }

                // 取最长的那一段
                queue.sort((a,b)=>b.total_length - a.total_length);

                if(queue[0])
                {
                    result_edge_areas.push(...queue[0].edge_areas);
                }

            }

            // console.log(strategy.name, result_edge_areas);
            for(let edge_area of result_edge_areas)
            {
                let intervals = edge_area.intervals || [];
                let offset_len = edge_area.edge.length - (edge_area.sum_length||0);

                let needs_filling = true;
                let ice_interval = intervals.find((val)=>compareNames([val.name],["冰箱","高柜"]));
                if(ice_interval) needs_filling = false;
                if(offset_len > 200 && needs_filling)
                {
                    let double_num = Math.floor(offset_len / 600);
                    let single_num = 1;
                    let insert_intervals : I_OnLineFigureInterval[] = [];
                    for(let i=0; i < double_num; i++)
                    {
                        insert_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category("双门地柜","厨房"));
                    }
                    for(let i=0; i < single_num; i++)
                    {
                        insert_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category("单门地柜","厨房"));
                    }


                    let target_intervals = intervals.length >= 2? intervals.slice(0,-2):[];
                    target_intervals.push(...insert_intervals);
                    intervals.length >= 2 && target_intervals.push(intervals[intervals.length-1]);

                    if(intervals.length ==0 )
                    {
                        let board = TLayoutOptmizerOnWallRules.make_interval_of_group_category("地柜收口板","厨房");
                        target_intervals= [board,...target_intervals];
                        target_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category("地柜收口板","厨房"));

                    }
                    let t_length =Math.ceil(edge_area.edge.length/10-0.01)*10;
                    TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(target_intervals,t_length,true,10);

                    edge_area.intervals = [...target_intervals];
                    edge_area.sum_length = 0;
                    edge_area.edge_length = edge_area.edge.length;
                    
                    edge_area.intervals.forEach((val)=>{ edge_area.sum_length += val.result_length;});

                }

            }
            for(let edge_areas of result_edge_areas)
            {
                if(edge_areas.intervals.length <= 1) continue;

                let floor_edge = edge_areas.edge;
                let hanging_edge = floor_edge.getBindingEdge(AttachedHangingEdge) || floor_edge;
                let corner_state = floor_edge._ex_props[ExPropEdgeCornerState];
                edge_areas.hanging_intervals = [];
                let hanging_offset_x = floor_edge.projectEdge2d(hanging_edge.v0.pos).x;
                // 先简单处理

                let wins = TRoomShape.getWindowsOfEdge(floor_edge) || [];
                let unvalid_pairs : number[][] = [];
                let hood_hanging_pair : number[] = [];
              
                let win_offset_len = 50; // 200mm的偏移
                let min_hanging_length = 500; // 要足够安装一个双门吊柜
                for(let win of wins)
                {
                    let px = hanging_edge.projectEdge2d(win.rect.rect_center).x;

                    let ll = px - win.rect.w/2 - win_offset_len;
                    let rr = px + win.rect.w/2 + win_offset_len;
                    ll = Math.max(ll,0);
                    rr = Math.min(rr,hanging_edge.length);
                    let len = rr - ll;

                    if((rr - ll) < 50) continue;
                    unvalid_pairs.push([ll,rr]);

                    let center_x = (ll+rr) /2;
                    let win_interval =  TLayoutOptmizerOnWallRules.make_interval_of_group_category("窗前吊柜", "厨房");
                    win_interval.target_center_x = center_x;
                    win_interval.center_x = center_x;
                    win_interval.target_length = len;
                    win_interval.result_length = len;
                    win_interval.center_diff_weight = 10000;
                    win_interval.length_differ_weight = 10000;
                    win_interval.length_values = [len];
                    edge_areas.hanging_intervals.push(win_interval);
                }


                let white_interval = edge_areas.intervals.find((val)=>compareNames([val.name],["冰箱"])&& val.result_length > 1.);
                if(white_interval)
                {
                    let center_x = white_interval.center_x - hanging_offset_x;
                    let len = white_interval.result_length;
                    let ice_interval =  TLayoutOptmizerOnWallRules.make_interval_of_group_category(white_interval.name, "厨房");
                    let isStart = edge_areas.intervals.indexOf((white_interval))==0;
                    let isEnd = edge_areas.intervals.indexOf(white_interval)===edge_areas.intervals.length-1;
                    if(isStart)
                    {
                        len = white_interval.result_length/2 + center_x;
                        center_x = len / 2;
                    }

                    ice_interval.target_center_x = center_x;
                    ice_interval.center_x = center_x;
                    ice_interval.target_length = len;
                    ice_interval.result_length = len;
                    ice_interval.length_values =[len];
                    ice_interval.center_diff_weight = 10000;
                    ice_interval.length_differ_weight = 10000;
                    unvalid_pairs.push([center_x-len/2,center_x+len/2]);
                    edge_areas.hanging_intervals.push(ice_interval);
                }
                let cooker_interval = edge_areas.intervals.find((val)=>compareNames([val.name],["炉灶地柜","转角炉灶地柜"]) && val.result_length>1.);
                
                if(cooker_interval)
                {
                    let hood_hanging_interval =  TLayoutOptmizerOnWallRules.make_interval_of_group_category("烟机吊柜", "厨房");
                    hood_hanging_interval.center_x = -hanging_offset_x + cooker_interval.center_x;
                    hood_hanging_interval.target_center_x = hood_hanging_interval.center_x;
                    let tx = hood_hanging_interval.result_length;
                    for(let lx of hood_hanging_interval.length_values)
                    {
                        if(Math.abs(cooker_interval.result_length - lx) < Math.abs(cooker_interval.result_length - tx))
                        {
                            tx = lx;
                        }
                    }
                    hood_hanging_interval.result_length = tx;
                    hood_hanging_interval.center_diff_weight = 1000;
                    edge_areas.hanging_intervals.push(hood_hanging_interval);

                    let ll =  hood_hanging_interval.center_x-hood_hanging_interval.result_length/2;
                    let rr =  hood_hanging_interval.center_x+hood_hanging_interval.result_length/2;
                    hood_hanging_pair = [ll,rr];
                    unvalid_pairs.push([ll,rr])
                }
                let valid_pairs = range_substract(hanging_edge.length,unvalid_pairs);



                for(let pair of valid_pairs)
                {

                    let s_x = 30;
                    let e_x = 30;

                    if(pair[1] === hood_hanging_pair[0])
                    {
                        e_x = 0;
                    }
                    if(pair[0] === hood_hanging_pair[1])
                    {
                        s_x = 0;
                    }
                    let t_len = pair[1] - pair[0] - s_x - e_x;

                    if(t_len < 0  || (e_x > 1 && s_x > 1 && t_len < min_hanging_length) ) continue;
                    let double_door_cabinet_length = 800;
                    let double_num = Math.floor(t_len / double_door_cabinet_length) + 1;

                    let cabinet_intervals :I_OnLineFigureInterval[] = [];
                    if(s_x > 1 )
                    {
                        let interval =  TLayoutOptmizerOnWallRules.make_interval_of_group_category("吊柜收口板", "厨房");
                        cabinet_intervals.push(interval);
                    }
                    for(let i=0; i < double_num; i++)
                    {
                        let interval =  TLayoutOptmizerOnWallRules.make_interval_of_group_category("吊柜", "厨房");
                        interval.target_length = double_door_cabinet_length;
                        cabinet_intervals.push(interval);
                    }
                    if(e_x > 1)
                    {
                        let interval =  TLayoutOptmizerOnWallRules.make_interval_of_group_category("吊柜收口板", "厨房");
                        interval.u_dv_flag = -1;
                        cabinet_intervals.push(interval);
                    }
                    TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(cabinet_intervals,t_len + s_x + e_x,true,10);

                    let sum_length = 0;
                    cabinet_intervals.forEach((val)=>sum_length+=val.result_length);
                    let d_x = t_len + s_x + e_x - sum_length;



                    for(let interval of cabinet_intervals)
                    {
                        interval.center_x += pair[0] + (e_x < 1?d_x:0);
                        if(interval.result_length > 1)
                        {
                            edge_areas.hanging_intervals.push(interval);
                        }
                    }
                }
                edge_areas.hanging_intervals.sort((a,b)=>a.center_x - b.center_x);
                if(edge_areas.hanging_intervals.length >= 1)
                {
                    if(!compareNames([edge_areas.hanging_intervals[0].name],["吊柜收口板","冰箱","窗前吊柜"]))
                    {
                        let interval =  TLayoutOptmizerOnWallRules.make_interval_of_group_category("吊柜收口板", "厨房");
                        edge_areas.hanging_intervals = [interval,...edge_areas.hanging_intervals];
                    }
                    if(!compareNames([edge_areas.hanging_intervals[edge_areas.hanging_intervals.length-1].name],
                        ["吊柜收口板","冰箱","窗前吊柜"]))
                    {
                        let interval =  TLayoutOptmizerOnWallRules.make_interval_of_group_category("吊柜收口板", "厨房");
                        edge_areas.hanging_intervals.push(interval);
                    }
                }

                TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(edge_areas.hanging_intervals,hanging_edge.length,true,10);

                // if(white_interval)
                // {
                //     console.log(strategy.name, edge_areas.hanging_intervals);
                // }
                
            }

            // console.log(strategy.name, result_edge_areas);

            let group_templates : TGroupTemplate[] = [];
            for(let edge_ereas of result_edge_areas)
            {
                let t_edge = edge_ereas.edge;
                for(let id in edge_ereas.intervals)
                {
                    let interval = edge_ereas.intervals[id];
                    if(interval.result_length < 0.1) continue;
                    let u_dv_flag = interval.u_dv_flag || 1;
                    let default_depth = 560;
                    if(compareNames([interval.name],["冰箱"])) default_depth = 760;
                    if(compareNames([interval.name],["高柜"])) default_depth = 600;
                    let target_rect = new ZRect(interval.result_length,default_depth);


                    let pos = t_edge.unprojectEdge2d({ x: interval.center_x, y: 0 });
                    target_rect.back_center = pos;
                    target_rect.nor = t_edge.nor.clone().negate();

                    target_rect.u_dv = t_edge.dv.clone().multiplyScalar(u_dv_flag);
                    target_rect.updateRect();

                    let name = interval.name;

                    if(name=="转角单门地柜")
                    {
                        name = "转角地柜";
                        let tw = target_rect._w * 2;


                        let t_x = (tw - target_rect._w) / 2;
                        let center_pos = target_rect.unproject({x:t_x,y:0});
                        target_rect._w = tw;
                        target_rect.rect_center = center_pos;
                    }
                    let group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(name, "厨房", target_rect);

                    
                    if(group_template.updateByTargetRect())
                    {
                        group_templates.push(group_template);

                    }
                }

                let hanging_intervals = edge_ereas.hanging_intervals || [];
                let hanging_edge = t_edge.getBindingEdge(AttachedHangingEdge) || t_edge;
                for(let interval of hanging_intervals)
                {
                    if(interval.name.includes("冰箱")) continue;
                    if(interval.name.includes("窗前吊柜")) continue;
                    let default_depth = this._configs.hanging_cabinet_depth;
                    let u_dv_flag = interval.u_dv_flag || 1;
                    let target_rect = new ZRect(interval.result_length,default_depth);

                    let pos = hanging_edge.unprojectEdge2d({ x: interval.center_x, y: 0 });
                    target_rect.back_center = pos;
                    target_rect.nor = hanging_edge.nor.clone().negate();

                    target_rect.u_dv = hanging_edge.dv.clone().multiplyScalar(u_dv_flag);
                    target_rect.updateRect();
                    let group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(interval.name, "厨房", target_rect);

                    if(group_template.updateByTargetRect())
                    {
                        group_templates.push(group_template);

                    }
                }
            }

            // 裁剪掉地柜可能多余的部分
            for(let group_template of group_templates)
            {   
                if(!compareNames([group_template.group_code],["转角地柜"])) continue;
                let target_rect : ZRect = group_template._target_rect;
                let nor = target_rect.nor.clone();
                let u_dv_flag = target_rect.u_dv_flag;
                for(let edge of w_poly.edges)
                {
                    let c_rect = new ZRect(edge.length, 300);
                    c_rect.nor = edge.nor;
                    c_rect.back_center = edge.center;
                    c_rect.updateRect();

                    let r_rect = target_rect.clip_side_rect(c_rect,nor);

                    target_rect = ZRect.fromPoints(r_rect.positions,nor);
                    
                }

                target_rect._u_dv_flag = u_dv_flag;
                target_rect.updateRect();
                group_template._target_rect.copy(target_rect);
                
                group_template.updateByTargetRect();
                // console.log(group_template.group_code, group_template._target_rect.w,group_template._target_rect.h,group_template.current_s_group.group_rect.w);

                let dw = group_template._target_rect.w - group_template.current_s_group.group_rect.w;

                let pos = group_template._target_rect.unproject({x:-dw/2,y:0});;

                group_template._target_rect._w -= dw;
                group_template._target_rect.rect_center =pos;
                group_template._target_rect.updateRect();

                group_template.updateByTargetRect();

                // console.log(group_template.group_code, group_template._target_rect.w,group_template._target_rect.h,group_template.current_s_group.group_rect.w);

            }


            // console.log(strategy.name);
            if(!this.checkOcclusion(this._room, group_templates))
            {
                continue;
            }
            if(this.graph && !this.graph.checkIntegrity(this._room,group_templates))
            {
                continue;
            }
            this.post_proccess_aligment(w_poly,group_templates);
            this.post_proccss_on_boards(w_poly,group_templates);

            TPostProcessLayout.post_process_kitchen_orientaion(this._room,group_templates);

            // console.log(strategy.name);
            this.post_append_corner_boards(w_poly,group_templates,has_flue);


            let score = this.computeGroupTemplateScore(this._room,group_templates);
            this._candidate_figure_list.push({
                group_templates: group_templates, debug_data: { rule: strategy, scheme_name: "逻辑-"+strategy.name, score:score }
            });
            


        }

        this._candidate_figure_list.sort((a,b)=>b.debug_data.score - a.debug_data.score);

        if(this._candidate_figure_list.length > 10) this._candidate_figure_list.length = 10;

        // console.log(this._candidate_figure_list);

        /**
         *  后处理橱柜的尺寸: 设置成默认配置的尺寸
         */
        this.post_process_kitchen_cupboard_size();
        console.log(this._candidate_figure_list);
        this._attempt_num = this._candidate_figure_list.length;
    }

    computeGroupTemplateScore(room:TRoom, group_templates:TGroupTemplate[])
    {
        let has_ice = group_templates.find((val)=>val.group_space_category.indexOf("冰箱")>=0);
        let score = 0;

        let min_hallway_score = 0; // 最小过道得分
        let floor_cabinets = group_templates.filter((val)=>val.group_space_category.endsWith("地柜"));

        floor_cabinets.forEach(cabinet0=>{
            floor_cabinets.forEach(cabinet1=>{
                if((cabinet0 === cabinet1)) return;

                // 必须是正对着的地柜
                if(!(cabinet0._target_rect.checkSameNormal(cabinet1._target_rect.nor.clone().negate(),false))) return;

                if(cabinet0._target_rect.frontEdge.islayOn(cabinet1._target_rect.frontEdge,900,0.25))
                {
                    min_hallway_score -= 60;
                }



            });
        })

        score += min_hallway_score;
        if(score < 0) return score;
        // 冰箱得分
        let ice_score =0;
        if(has_ice)
        {
            ice_score = 10;
        }
        score += ice_score;
        let area = room.room_shape._area;

        let has_corner = group_templates.find((val)=>val.group_space_category.indexOf("转角")>=0);
        let corner_score = 0;
        if(has_corner)
        {
            corner_score += 5.;
        }

        score += corner_score;
        let flues = room.flues;


        // 烟道距离得分
        let flue_distance_score = 0;

        let cooker_template = group_templates.find((val)=>val.group_space_category.indexOf("炉灶")>=0)
        if(!cooker_template) return -10;
        if(flues)
        {
            let target_flue = flues.find((flue)=>{
                let pp = cooker_template._target_rect.project(flue.rect.rect_center,true);

                if(Math.abs(pp.y)>1000)  return false;

                let xx = Math.abs(pp.x) - cooker_template._target_rect.w/2 - flue.rect.max_hh/2;

                if(xx < 600) return true;

                return false;
            });
            if(target_flue)
            {
                flue_distance_score = 20;
            }
        }
        score += flue_distance_score;

        let hood_distance_socre = 0;
        let hood_template = group_templates.find((val)=>val.group_space_category.indexOf("烟机")>=0)


        if(!hood_template)
        {
            hood_distance_socre -= 30;
        }
        else{
            if(hood_template._target_rect)
            {
                let pp = cooker_template._target_rect.project(hood_template._target_rect._back_center,true);
                if(Math.abs(pp.x) > 300)
                {
                    hood_distance_socre -=30;
                }
            }
        }

        score+= hood_distance_socre;
        let sum_area = 0;
        let area_score = 0;
        group_templates.forEach((group_template)=>sum_area+=group_template._target_rect.w * group_template._target_rect.h/(1000*1000));
        area_score = (60) * (sum_area/area);

        score += area_score;

        return score;
    }

    checkEdgeCornerState(state:number, id:number, sub_edges_count:number)
    {

        let ans_state = 0;
        if(id == 0)
        {
            ans_state = ans_state | (state & EdgeCornerState.PrevCorner);
            ans_state = ans_state | (state & EdgeCornerState.StartCorner);
        }
        if(id == (sub_edges_count-1))
        {
            ans_state = ans_state | (state & EdgeCornerState.NextCorner);
            ans_state = ans_state | (state & EdgeCornerState.EndCorner);
        }
        return ans_state;
    }

    adjustIntervals(edge:ZEdge, intervals:I_OnLineFigureInterval[])
    {
        let wins = TRoomShape.getWindowsOfEdge(edge);
        if(!wins || wins.length == 0) return;

        let px = -1;
        let plen = -1;
        for(let win of wins)
        {
            if(win.type !== "Window") continue;
            let pp = edge.projectEdge2d(win.rect.rect_center);
            if(win.length /2 > plen)
            {
                px = pp.x;
                plen = win.length/2;
            }
        }

        if(px < 0) return;

        let sink_interval_id = intervals.findIndex((val)=>val.name==="水槽地柜");


        if(sink_interval_id >=0)
        {
            let sink_interval = intervals[sink_interval_id];
            if(sink_interval.center_x < px)
            {
                let next_interval = intervals[sink_interval_id+1];
                if(next_interval && next_interval.name.indexOf("转角")<0) // 不可以是转角地柜
                {
                    // 交换后, 二者的位置
                    let s_x = sink_interval.center_x - sink_interval.result_length/2;
                    let n_x = s_x + next_interval.result_length + sink_interval.result_length/2;

                    if(Math.abs(n_x-px) < Math.abs(sink_interval.center_x -px))
                    {
                        sink_interval.center_x = n_x;
                        next_interval.center_x = s_x + next_interval.result_length/2;

                        let tmp = intervals[sink_interval_id];
                        intervals[sink_interval_id] = intervals[sink_interval_id+1];
                        intervals[sink_interval_id+1] = tmp; 
                    }
                }
            }
            else if(sink_interval.center_x > px)
            {
                let prev_interval = intervals[sink_interval_id-1];
                if(prev_interval && prev_interval.name.indexOf("转角")<0) // 不可以是转角地柜
                {
                    // 交换后, 二者的位置
                    let s_x = sink_interval.center_x + sink_interval.result_length/2;
                    let n_x = s_x - prev_interval.result_length - sink_interval.result_length/2;

                    if(Math.abs(n_x-px) < Math.abs(sink_interval.center_x -px))
                    {
                        sink_interval.center_x = n_x;
                        prev_interval.center_x = s_x - prev_interval.result_length/2;

                        let tmp = intervals[sink_interval_id];
                        intervals[sink_interval_id] = intervals[sink_interval_id-1];
                        intervals[sink_interval_id-1] = tmp; 
                    }
                }
            }


        }

        let cook_interval_id = intervals.findIndex((val)=>val.name==="炉灶地柜");


    }
    updateHaningIntervalsByCornerState(hanging_intervals : I_OnLineFigureInterval[], corner_state:number, edge:ZEdge)
    {
        let tol = 800;
        if(corner_state & EdgeCornerState.StartCorner)
        {
            let start_interval = hanging_intervals[1];
            if(start_interval && compareNames([start_interval.name],["双门吊柜"])
                && start_interval.result_length >= 700-0.1 
                && Math.abs(start_interval.center_x-start_interval.result_length/2) < tol)
            {
                start_interval.name = "转角吊柜";
                start_interval.u_dv_flag = -1;
            }
        }
        if(corner_state & EdgeCornerState.EndCorner)
        {
            let end_interval = hanging_intervals[hanging_intervals.length-2];
            if(end_interval && compareNames([end_interval.name],["双门吊柜"])&& end_interval.result_length >= 700-0.1
            && Math.abs(end_interval.center_x+end_interval.result_length/2 - edge.length) < tol)
            {
                end_interval.name = "转角吊柜";
                end_interval.u_dv_flag = 1;
            }
        }

        if(corner_state & EdgeCornerState.PrevCorner)
        {
            let start_interval = hanging_intervals[1];
            if(start_interval && compareNames([start_interval.name],["双门吊柜"])&& start_interval.result_length >= 700-0.1
            && Math.abs(start_interval.center_x-start_interval.result_length/2) < tol)
            {
                start_interval.name = "翻门吊柜";
            }
        }

        if(corner_state & EdgeCornerState.NextCorner)
        {
            let end_interval = hanging_intervals[hanging_intervals.length-2];
            if(end_interval && compareNames([end_interval.name],["双门吊柜"]) && end_interval.result_length >= 700-0.1
            && Math.abs(end_interval.center_x+end_interval.result_length/2 - edge.length) < tol)
            {
                end_interval.name = "翻门吊柜";
                end_interval.u_dv_flag = 1;
            }
        }
    }

    /**
     *  主要是炉灶地柜 和 窗的遮挡关系
     */
    checkOcclusion(room:TRoom, group_templates:TGroupTemplate[])
    {
        // 遮挡关系主要是---炉灶地柜

        let windows = room?.windows || [];
        for(let win of windows)
        {
            let rect = win.rect;
            if(!rect) continue;
            rect = rect.clone();
            let r_center = rect.rect_center;
            rect._h += 300;
            rect.rect_center = r_center;
            let occlusion_length = 0;
            for(let group_template of group_templates)
            {
                if(group_template.group_space_category.indexOf("炉灶")<0 && group_template.group_space_category.indexOf("冰箱")<0)
                {
                    continue;
                }
                let t_rect = group_template._target_rect.clone();
                t_rect.reOrderByOrientation(true);

                
                rect.reOrderByOrientation(true);
    
                let intersect = rect.intersect_rect(t_rect);
    
                if(intersect && intersect.length > 0)
                {
                    if(intersect.checkSameNormal(rect.dv))
                    {
                        intersect.swapWidthAndHeight();
                    }
                    occlusion_length += intersect.w;
                  
                }

            }
            if(rect.w - occlusion_length < Math.min(rect.w - 200,800))
            {
                return false;
            }

        }



        return true;
    }




    post_proccess_aligment(w_poly:ZPolygon,group_templates:TGroupTemplate[])
    {
        let floor_cabinet_templates = group_templates.filter(template=>compareNames([template.group_code],["地柜"]));
        let floor_boards = floor_cabinet_templates.filter((val)=>compareNames([val.group_code],["收口板"]));

        let corner_cabinets = floor_cabinet_templates.filter((val)=>compareNames([val.group_code],["转角"]));


        for(let board of floor_boards)
        {
            let neighbor_corner : TGroupTemplate = null;

            let m_offset_x = 0;

            // 找相邻的转角地柜
            corner_cabinets.forEach((cabinet)=>{
                if(!board._target_rect.checkSameNormal(cabinet._target_rect.dv)) return;
                let front_center = cabinet._target_rect.front_center;

                let pp = board._target_rect.project(front_center);
                let px = pp.x;

                let py = pp.y - cabinet._target_rect.w/2;
                if(py > board._target_rect.h/2) return;


                if(Math.abs(px) - board._target_rect.w/2 > 200) return;


                if(!neighbor_corner || Math.abs(px) < Math.abs(m_offset_x))
                {
                    neighbor_corner = cabinet;
                    m_offset_x = px;
                }   
            });

            if(neighbor_corner)
            {
                let offset_x = Math.abs(m_offset_x) - board._target_rect.w/2 - 20;

                if(Math.abs(offset_x) < 0.5) continue;
                if(offset_x < 0)
                {
                    if(board._target_rect.w < 50-0.1) continue;

                    let tlen = 10;
                    let tw = board._target_rect._w - tlen;
                    let flag = m_offset_x >0?-1:1;
                    let pos = board._target_rect.unproject({x:tlen/2*flag,y:0});
                    board._target_rect._w = tw;
                    board._target_rect.rect_center = pos;
                    offset_x += tlen;
                    if(offset_x < 0) continue;
                }

                let neighbor_templates = floor_cabinet_templates.filter((val)=>{
                    let t_rect = val._target_rect;

                    if(!t_rect.checkSameNormal(board._target_rect.nor,false)) return false;

                    if(Math.abs(board._target_rect.frontEdge.projectEdge2d(val._target_rect.front_center).y) > 50) return false;

                    return true;
                });

                let offset_dv = board._target_rect.dv.clone().multiplyScalar(offset_x);
                if(m_offset_x < 0) offset_dv.negate();

                neighbor_templates.forEach(val=>{
                    val._target_rect.rect_center = val._target_rect.rect_center.add(offset_dv);
                    val.updateByTargetRect();
                })

            }
        }


    }
    post_proccss_on_boards(w_poly:ZPolygon, group_templates:TGroupTemplate[])
    {
        let boards = group_templates.filter((val)=>compareNames([val.group_code],["收口板"]));

        let replace_boards : TGroupTemplate[] = [];
        let align_dist = 50;
        for(let id in boards)
        {
            let board = boards[id];
            if(board.group_code.indexOf("吊柜")<0) continue;

            let left_side_dist =10000;
            let right_side_dist = 10000;
            let rect =board._target_rect;

            let left_edge = rect.leftEdge;
            let right_edge = rect.rightEdge;

            w_poly.edges.forEach((edge)=>{
                if(edge.islayOn(left_edge,align_dist,0.3))
                {
                    left_side_dist = 0;
                }
                if(edge.islayOn(right_edge,align_dist,0.3))
                {
                    right_side_dist = 0;
                }
            });

            group_templates.forEach((template)=>{
                if(template === board) return;
                if(board.group_code.indexOf("吊柜")>=0 && template.group_code.indexOf("吊柜")<0) return;
                if(board.group_code.indexOf("地柜")>=0 && template.group_code.indexOf("地柜")<0) return;

                if(board.group_code === template.group_code) return;

                for(let edge of template._target_rect.edges)
                {
                    if(edge.islayOn(left_edge,align_dist,0.3))
                    {
                        if(left_edge.projectEdge2d(edge.center).y > -1)
                        {
                            left_side_dist = Math.min(left_edge.projectEdge2d(edge.center).y,left_side_dist);

                        }
                    }
                    if(edge.islayOn(right_edge,align_dist,0.3))
                    {
                        if(right_edge.projectEdge2d(edge.center).y > -1)
                        {
                            right_side_dist = Math.min(right_edge.projectEdge2d(edge.center).y,right_side_dist);

                        }
                    }
                }
            });

            if(left_side_dist > align_dist || right_side_dist>align_dist)
            {
                board._target_rect.ex_prop['attached_side'] = ''+(left_side_dist<right_side_dist?-1:1);


                replace_boards.push(board);

            }
        }


        for(let board of replace_boards)
        {
            let id = group_templates.indexOf(board);
            if(id<0) continue;


            let attached_side = eval(board._target_rect.ex_prop['attached_side']);
            let bw = 18;
            let rect = board._target_rect.clone();
            let pos = rect.unproject({x:(rect._w/2-bw/2) * attached_side ,y:0});
            rect._w = bw;
            rect.rect_center = pos;

            let group_code = board.group_code.replace("收口板","见光板");
            let t_group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(group_code, "厨房",rect);
            group_templates[id] = t_group_template;
        }
    }

    post_append_corner_boards(w_poly:ZPolygon,group_templates:TGroupTemplate[],has_flue:boolean=false)
    {
        let corner_cabinets = group_templates.filter((val)=>compareNames([val.group_code],["转角地柜"]));

        corner_cabinets.forEach((cabinet)=>{

            let target_rect = cabinet._target_rect;

            // console.log(target_rect._attached_elements);
            let w_edges = w_poly.edges.filter((edge)=>{
                return edge.islayOn(target_rect.rightEdge,600,0.5);
            });

            if(w_edges && w_edges.length > 0)
            {
                w_edges.sort((a,b)=>{
                    return target_rect.rightEdge.projectEdge2d(a.center).y - target_rect.rightEdge.projectEdge2d(b.center).y;
                });

                let t_edge = w_edges[0];

                let dist =  target_rect.rightEdge.projectEdge2d(t_edge.center).y;

                if(dist < 50) return;
                if(dist > 400) return;

                
                let lx = 0;
                while(dist >= 50)
                {
                    let lw = Math.min(120,dist);

                    let board_rect = new ZRect(lw,target_rect.h);
                    board_rect.nor = target_rect.nor;
                    board_rect._u_dv_flag = target_rect.u_dv_flag;
                    board_rect.rect_center = target_rect.rightEdge.unprojectEdge2d({x:target_rect.rightEdge.length/2,y:lx+lw/2});

                    group_templates.push(TGroupTemplate.makeGroupTemplateByGroupSpaceCategory("地柜收口板","厨房",board_rect));
                    lx += lw;
                    dist -= 120;
                }


    
            }
        })

    }

    
}
