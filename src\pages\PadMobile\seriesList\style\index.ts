import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    container: css`
      background: #FFF;
      height: 100%;
      z-index: 999;
      padding: 0 0 0 16px;
      /* overflow: hidden; */
      position: fixed;
      top: 48px;
      left: 0;
    `,
    titleContainer: css`
      display: flex;
      justify-content: space-between;
      align-items: center;
    `,
    title:css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: semibold;
      font-size: 20px;
      line-height: 1.4;
      letter-spacing: 0px;
      text-align: left;
      font-weight: 600;
      margin: 16px 0px;
    `,
    container_input: css`
      border-radius: 30px;
      background: #F2F3F5;
      color: #000;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
      min-width: 70%;
      height: 32px;
      border: none;
      margin: 16px 0 0 0px;
      padding-left: 30px;
      :focus {
        border-color: none; /* 取消聚焦边框 */
        box-shadow: none; /* 取消聚焦阴影效果 */
        outline: none; /* 取消聚焦时的外边框效果 */
      }
    `,
    Icon: css`
      position: absolute;
      top: 113px;
      left: 7px;
    `,
    IconDelete: css`
      position: absolute;
      top: 97px;
      right: 38%;
      cursor: pointer;
    `,
    selectInfo: css`
      width: 24%;
      margin: 16px 8px 0px 0;
      display: flex;
      justify-items: baseline;
      align-items: center;
      a {
        color: #ffffff;
        padding: 5px;
        line-height: 23px;
        height: 34px;
      }
      a:hover {
        color: #3D9EFF;
        border-radius: 10px;
        background: #BFD8FF14;
        transition: all .3s;
      }
    `,
    findInfo: css`
      display: flex;
      justify-content: space-between;
      padding-right: 28px;
      margin-bottom: 16px;
    `,
    roomListBar: css`
      height: auto;
      @media screen and (orientation: landscape) {
        padding-bottom: 16px;
      }
    `,
    bottomPanel:css`
      @media screen and (orientation: landscape) {
        width: 224px;
      }
    `,
    topSelect: css`
      width: 180px;
      height: 28px;
      display: flex;
      justify-content: center;
      color: #282828;
      font-family: PingFang SC;
      font-weight: semibold;
      font-size: 20px;
      font-weight: 600;
      color: #959598;
      z-index: 9;
      align-items: center;
      width: 100%;
      padding: 0px 16px;
      margin: 16px 0px;
      position: relative;
      div{
        margin-right: 8px;
      }
      @media screen and (max-width: 450px) { // 手机宽度
        width: auto;
        font-size: 16px;
        top: 16px;
      }
      @media screen and (orientation: landscape) {
        font-size: 16px;
        justify-content: space-between;
      }
      @media screen and (orientation: portrait) {
        top : 0px;
        left: 12px;
        width: 50%;
        justify-content: start;
      }
      .checked:after
      {
        content: '';
        display: block;
        width: 20px;
        height: 3px;
        border-radius: 10px;
        background-color: #282828;
        margin-top: 5px;
        margin-left: 23px;
        position: absolute;
      }
    `,
    container_listInfo:css`
      overflow-y: hidden;
      width:100%;
      display:flex;
      overflow-x: auto;
      height: 230px;
      &::-webkit-scrollbar {
        display: none;
      }
      scrollbar-width: none;
      -ms-overflow-style: none;
      @media screen and (orientation: landscape) {
        height: calc(var(--vh, 1vh) * 100 - 140px);
        width: 100%;
        display: block;
        overflow-y: auto;
      }
      @media screen and (orientation: portrait) {
        margin-top: 10px;
      }
    `,
    type: css`
      margin-top: 50px;
      @media screen and (orientation: landscape) {
        height: calc(var(--vh, 1vh) * 100 - 80px) !important;
      }
    `,
    container_box :css`
      float:left;
      @media screen and (orientation: landscape) {
        float: none;
      }
    `,
    container_data: css`
      width: 270px;
      height: 170px;
      box-sizing: border-box;
      position: relative;
      margin:10px;
      @media screen and (orientation: landscape) {
        width: 100%;
        margin: 0;
        padding: 0 12px;
        height: 137px;
        margin-bottom: 40px;
      }
    `,
    textInfo: css`
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0 4px 0;
      @media screen and (orientation: landscape) {
        margin-top: 4px;
      }
    `,
    container_title: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: medium;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 78%;
    `,
    container_desc: css`
      color: #5B5E60;
      border-radius: 12px;
      border: 1px solid #0000000F;
      padding: 4px 8px;
    `,
    seriesStyle: css`
      color: #5B5E60;
      font-family: PingFang SC;
      font-size: 12px;
      letter-spacing: 0px;
      text-align: left;
      background-color: #F2F3F5;
      width: auto;
      border-radius: 2px;
      padding: 2px 8px;
      display: block;
      white-space: nowrap;
    `,
    noScroll: css`
      overflow-y: hidden !important;
    `,
    side_list:css`
      width: 100%;
      height: 98vh;
      overflow: auto;
      position: absolute;
      left: 0;
      canvas {
        margin-left:5px;
        margin-top:5px;
        cursor : pointer;
      }
    `,
    line: css`
      height: 100%;
      position: absolute;
      right: 0;
      top: 0;
      width: 4px;
      cursor: col-resize;
      z-index: 998;
    `,
    emptyInfo: css`
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      img
      {
        width: 120px;
        height: 120px;
      }
      .desc
      {
        text-align: center;
        margin-top: 10px;
        color: #A2A2A5;
        font-size: 12px;
      }
    `,
    Popover_hoverInfo: css`
      border-radius: 4px;
      height: 100%;
      overflow: hidden;
      @media screen and (orientation: landscape) {
      }
      img {
        transition: all .5s;
        width: 100%;
        border-radius: 4px;
      }
    `,
    Popover_hoverInfo_type: css`
      border: 2px solid #9242FB !important;
    `,
    tag_label: css`
      position: absolute;
      top: 4px;
      right: 16px;
      border-radius: 4px;
      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
      color: #fff;
      padding: 4px 8px;
      border-radius: 4px;
    `,
    applyBtn: css`
      
      
    `,
    panel:css`
      width: 560px !important;
      @media screen and (max-width: 450px) {
        width: 300px !important;
      }
      @media screen and (orientation: landscape) {
        width: 900px !important;
      }
    `,
    panelContent: css`
      padding: 20px;
      @media screen and (max-width: 450px) {
        padding: 14px;
      }
      @media screen and (orientation: landscape) {
        display: flex;
        .ant-carousel {
          max-width: 420px;
          margin: auto 0;
          margin-right: 40px;
        }
      }
    `,
    panelContentLeft: css`
      display: flex;
      flex-direction: column;
      flex: 1
    `,
    panelContentRight: css`
    flex: 1
    `,
    panelContainer: css`
      .swj-baseComponent-Containersbox-title{
        background-color: #fff !important;
      }
      .swj-baseComponent-Containersbox-body
      {
        > div:first-child {
          height: 760px !important; /* 只影响第一个子 div */
          @media screen and (max-width: 450px) {
            height: 500px !important;
          }
          @media screen and (orientation: landscape) {
            height: 475px !important;
            margin-top: -1px;
          }
        }
       
      }
      
    `,
    materialList: css`
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      height: 760px;
      overflow-y: auto;
      max-height: 320px;
      @media screen and (max-width: 450px) { // 手机宽度
        height: 205px;
        gap: 12px;
      }
      @media screen and (orientation: landscape) {
        gap: 18px;
      }
      ::-webkit-scrollbar-thumb
      {
        display: none;
      }
      scrollbar-width: none;
      -ms-overflow-style: none;
    `,
    roomImg: css`
      width: 100%;
      
      height: 290px;
      @media screen and (max-width: 450px) { // 手机宽度
        width: 300px;
        height: 160px;
      }
      @media screen and (orientation: landscape) {
        border-radius: 8px;
        height: auto;
        /* min-width: 520px; */
      }
    `,
    applyBtnInfo: css`
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      @media screen and (orientation: landscape) {
        margin-top: 20px;
      }
      button{
        width: 50%;
        height: 32px;
        margin-right: 10px;
      }
      .leftBtn{
        color: #000;
        border-radius: 10px;
        border: none;
        border-radius: 6px;
        background: #EAEAEB;
      }
      .rightBtn{
        border-radius: 6px;
        border: none;
        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
        color: #fff;
      }
    `
  }
});

