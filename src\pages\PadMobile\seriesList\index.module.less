.container{
    :global{
        .ant-spin-nested-loading .ant-spin-container::after {
            opacity: 0;
        }
        .ant-input {
            color: #6C7175;
            background-color:#292d32 ;
        }
        input::-webkit-input-placeholder {
            color: #6C7175;
        }
            
        .ant-popconfirm-message{
            padding: 10px 10px 0 10px;
        }
        .ant-popconfirm-buttons{
            padding: 0 10px 10px 10px;
        }
        .ant-btn-primary:disabled{
            background: #25282D;
            border: 1px solid #42464B;
            color: #42464B;
            font-family: PingFangSC-Regular, sans-serif;
            font-weight: semibold;
        }
        .ant-progress .ant-progress-text{
            display: none;
        }
        .ant-progress-inner{
            background: #BFD8FF14 !important;
        }
        .ant-skeleton{
            background: #ebf0f80a;
            border-radius: 16px;
            margin-right: 4px;
        }
        .ant-segmented{
            background: #BFD8FF14;
            .ant-segmented-item{
            color: #93989F;
            }
            .ant-segmented-item-selected {
            background: #147FFA !important;
            color: #FFFFFF;
            }
            .ant-segmented-item:hover:not(.ant-segmented-item-selected):not(.ant-segmented-item-disabled) {
            color: #93989F;
            background: none !important;
            }
            .ant-segmented-thumb{
            background: #147FFA !important;
            }
        }
        .ant-spin-nested-loading {
            height: 100%;
        }
        .ant-spin-container{
            height: 100%;
        } 
        .ant-radio-group {
            width: 100% !important;
            padding-right: 22px;
        }
        .ant-radio-button-wrapper{
            width: 50% !important;
            text-align: center;
        }
    } 
}

.apply_category_button_container {
    display: flex;
    position: absolute;
    bottom: 0px;
    left:12px;
    right:12px;
    border-radius: 0 0 4px 4px;
    overflow: hidden;
    justify-content: space-between;
}
.apply_category_button {
    flex: 1; 
    height: 30px;
    line-height: 30px;
    font-size:14px;
    text-align: center;
    color:#fff;
    background-color: rgba(29, 29, 29, 0.4);
    border: 0px rgba(29, 29, 29, 0.4);
    opacity: 0.6;

}
.apply_category_button:hover {
    cursor: pointer;
    opacity: 1;
}
.apply_category_button.checked {
    cursor: pointer;
    opacity: 1;
    background: linear-gradient(to bottom, #C280FF,#1790FF);
}
.apply_all_button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 140px;
    height: 70px;
    background-color: rgba(29, 29, 29, 0.4);
    border: 0px rgba(29, 29, 29, 0.4);
    border-radius: 12px;
    color: #FFFFFF;
    font-size: 18px;
    font-weight: bold; 
    opacity: 0.6;
}
.apply_all_button:hover {
    cursor: pointer; /* 鼠标悬停时光标变成手形 */
    opacity: 1;

}