.cardContainer {
  display: flex;
  flex-wrap: wrap;
  height: auto;
  margin: 0 -20px;
}

.card {
  margin: 10px 20px !important;
  border-radius: 8px;
  overflow: hidden;
}

@media screen and (min-width: 896px) {
  .card {
    width: calc((100% / 2) - 40px) !important;
  }
}

@media screen and (min-width: 1224px) {
  .card {
    width: calc((100% / 3) - 40px) !important;
  }
}

@media screen and (min-width: 1552px) {
  .card {
    width: calc((100% / 4) - 40px) !important;
  }
}

@media screen and (min-width: 1880px) {
  .card {
    width: calc((100% / 5) - 40px) !important;
  }
}

@media screen and (min-width: 2208px) {
  .card {
    width: calc((100% / 6) - 40px) !important;
  }
}

.content {
  display: flex;
  width: auto;
  img {
    width: 48px;
    margin-top: -3px;
  }
}

.right {
  margin-left: 8px;
  justify-content: center;
  .title {
    color: #282828;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
  }
  .desc {
    color: #959598;
    font-family: PingFang SC;
    font-weight: normal;
    font-size: 12px;
    line-height: 1.67;
    letter-spacing: 0px;
    text-align: left;
  }
}

.insertBtn {
  border: none;
  padding: 0 8px;
  margin-left: 30px;
  height: 32px;
  background-color: #f4f5f5;
  color: #282828;
  font-family: PingFang SC;
  font-weight: normal;
  font-size: 14px;
  line-height: 1.57;
  letter-spacing: 0px;
  text-align: left;

  &:hover {
    color: #282828 !important;
    background-color: #f4f5f5 !important;
  }
}

.rotatedIcon {
  transform: rotate(45deg); // 旋转45°
}