import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
        root: css`
            height: 200px;
    
            @media screen and (orientation: landscape) {
                height: calc(var(--vh, 1vh) * 100);

            }

            .container_box {
                width:160px;
                height:160px;
                display: flex;
                margin:20px;
                border-radius:5px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, .2);
                font-size:16px;
                color:#777;
                cursor:pointer;
                box-sizing: border-box;
                justify-content:center;
                align-items:center;
                flex-wrap:wrap;
                img {
                    width: 160px;
                    height: 70%;
                }
                @media screen and (orientation: landscape) {
                    width: 100%;
                    height: 160px;
                    margin: 0 0 20px 0;

                }
            }
        `,
        listContainer: css`
            overflow-x: auto;
            overflow-y: hidden;
            display: flex;
            @media screen and (orientation: landscape) {
                height: calc(var(--vh, 1vh) * 100 - 50px);
                width: 224px;
                flex-direction: column;
                overflow-y: auto;
                padding: 0 20px;
            }
        `

    }
})