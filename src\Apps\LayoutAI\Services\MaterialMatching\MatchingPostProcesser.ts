import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { ENV, is_standalone_website } from "@/config/env";
import SunvegaAPI from "@api/clouddesign";
import { Vec3toMeta, ZRect, compareNames } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { StyleBrush, StyleComposeItem } from "../../AICadData/SwjLayoutData";
import { I_MaterialMatchingItem, toStringForMaterialMatchingItem } from "../../Layout/IMaterialInterface";
import { FigureCategoryManager } from "../../Layout/TFigureElements/FigureCategoryManager";
import { TFigureElement } from "../../Layout/TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../../Layout/TLayoutEntities/TBaseGroupEntity";
import { TGroupTemplateEntity } from "../../Layout/TLayoutEntities/TGroupTemplateEntity";
import { TGraphBasicConfigs } from "../../Layout/TLayoutGraph/TGraphBasicConfigs";
import { TRoom } from "../../Layout/TRoom";
import { TSeriesSample } from "../../Layout/TSeriesSample";
import { FigureTopViewer } from "../../Scene3D/process/FigureTopViewer";
import { Logger } from "../../Utils/logger";
import { CabinetStyleService } from "./CabinetStyleService";
import { MaterialService } from "./MaterialService";
import { TMaterialMatchingConfigs } from "./TMaterialMatchingConfigs";
export class MatchingPostProcesser {

    // 相似模型位映射，用于隐藏重复素材
    private static similarModellocMap: Map<string, string[]> = new Map([
        ["挂画", ["挂画", "墙饰"]],
        ["墙饰", ["挂画", "墙饰"]],
    ]);

    private static get storey_height() {
        return (LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height || TMaterialMatchingConfigs._storey_height;
    }

    public static forceSaveCabinetStyle: boolean = false;

    /**
     *  调整背景墙相关的图元：
     *  1. 限制背景墙素材的深度最大为120mm
     *  2. 调整背景墙相关的家具图元，使其与背景墙对齐
     *  @param room 房间对象
     */
    public static adjust_backgroundwall_related(room: TRoom) {
        // 用于记录匹配到素材的背景墙图元
        let backgroundWalls: TFigureElement[] = [];
        room._furniture_list.forEach((fe) => {
            if (fe.modelLoc.indexOf("背景墙") >= 0 && fe.haveMatchedMaterial()) {
                backgroundWalls.push(fe);
            }
        });
        let allFigureElements:TFigureElement[] = [];
        allFigureElements.push(...room._furniture_list);
        if(room.decoration_elements)
        {
            allFigureElements.push(...room.decoration_elements);
        }
        
        // let logContent:string = "";
        // 遍历所有匹配到背景墙素材的图元
        for (let backgroudwall of backgroundWalls) {
            // logContent += "Backgroud Wall: " + backgroudwall.toString();

            // 限制背景墙素材的深度最大为120mm
            let max_background_wall_depth = Math.min(backgroudwall._matched_material.width, 120);
            let depthOffset: number = max_background_wall_depth - backgroudwall.rect.depth;
            let background_wall_rect = backgroudwall?.matched_rect?.clone() || backgroudwall.rect.clone();
            background_wall_rect._h = max_background_wall_depth;
            background_wall_rect.updateRect();

            // logContent += "\n   depthOffset=" + depthOffset;

            // 遍历房间内的所有已经匹配到素材的家具图元，若家具图元与背景墙图元相交，则调整家具图元位置：
            //    将家具往远离墙方向推出去一点，即将家具图元的位置沿背景墙图元的法向方向移动一个特定的偏移量（相交区域的深度），以便对齐背景墙
             allFigureElements.forEach((other) => {
                if (backgroudwall == other) return;
                if (!other.haveMatchedMaterial() || !other.matched_rect) return;
                if (!other.matched_rect.checkSameNormal(backgroudwall.rect.nor)) {
                    return;
                }
                let int_rect = other.matched_rect.intersect_rect(background_wall_rect);

                if (int_rect) {
                    int_rect = ZRect.fromPoints(int_rect.positions, background_wall_rect.nor);
                    let moveVec: Vector3 = background_wall_rect.nor.clone();
                    moveVec.multiplyScalar(int_rect.h);
                    other.matched_rect.rect_center = other.matched_rect.rect_center.clone().add(moveVec);
                    other.rect.back_center = other.matched_rect.back_center;
                    other.rect.updateRect();
                }
            });
        }
        // console.info(logContent);
    }

    /**
     *  隐藏同属于同一种模型位的重复素材（采用设置素材可见性为false的方式）
     *  @param targetModelloc 目标模型位
     *  @param singleFigureElements 所有单图元的列表
     *  @param groupMemberFigureElements 所有组合内子图元的列表
     *  @param logger 日志记录器
     */
    private static async _hideDuplicatedMaterialOfSameModelloc(targetModelloc: string, singleFigureElements: TFigureElement[], groupMemberFigureElements: TFigureElement[], shouldCheckIntersection: boolean = true) {
        // 获取目标模型位的相似模型位列表
        let targetModellocs : string[] = MatchingPostProcesser.similarModellocMap.get(targetModelloc);
        if (!targetModellocs || targetModellocs.length == 0) {
            targetModellocs = [targetModelloc];
        };
        let singleCarpetElements = singleFigureElements.filter((fe) => {
            return compareNames([fe.modelLoc], targetModellocs) && fe.haveMatchedMaterial();
        });
        let groupElements = singleFigureElements.filter((ge) => {
            return (ge.furnitureEntity instanceof TGroupTemplateEntity || ge.furnitureEntity instanceof TBaseGroupEntity) && ge.haveMatchedMaterial();
        });
        let groupCarpetElements = groupMemberFigureElements.filter((fe) => {
            return compareNames([fe.modelLoc], targetModellocs) && fe.haveMatchedMaterial();
        });
        singleCarpetElements.forEach(async (singleCarpet) => {
            if (shouldCheckIntersection) {
                singleCarpet.markMaterialAsVisible();
                groupCarpetElements.forEach((groupCarpet) => {
                    if (singleCarpet.rect.checkIntersection(groupCarpet.rect)) {
                        Logger.instance.log("Hide Duplicated Material: " + singleCarpet.toString() + "\n   " + toStringForMaterialMatchingItem(singleCarpet._matched_material));
                        singleCarpet.markMaterialAsInvisible();
                    }
                });
                await groupElements.forEach(async (group) => {
                    if (singleCarpet.rect.checkIntersection(group.rect)) {
                        let memberMaterials: I_MaterialMatchingItem[] = await MaterialService.getGroupMaterialDetail(group.matchedMaterialId);
                        let hasCarpetMember = memberMaterials.find((mat: I_MaterialMatchingItem) => compareNames([mat.modelLoc], targetModellocs));
                        if (hasCarpetMember) {
                            Logger.instance.log("Hide Duplicated Material: " + singleCarpet.toString() + "\n   " + toStringForMaterialMatchingItem(singleCarpet._matched_material));
                            singleCarpet.markMaterialAsInvisible();
                        }
                        if (LayoutAI_App.instance) {
                            LayoutAI_App.instance.update();
                        }
                    }
                });
            } else {
                Logger.instance.log("Hide Duplicated Material: " + singleCarpet.toString() + "\n   " + toStringForMaterialMatchingItem(singleCarpet._matched_material));
                singleCarpet.markMaterialAsInvisible();
            }
        });
    }

    /**
     *  隐藏同属于一种模型位的重复素材
     *  @param singleFigureElements 所有单图元的列表
     *  @param groupMemberFigureElements 所有组合内子图元的列表
     *  @param logger 日志记录器
     */
    public static async hideDuplicatedMaterialOfSameModelloc(singleFigureElements: TFigureElement[], groupMemberFigureElements: TFigureElement[]) {
        // 隐藏地毯复素材
        await MatchingPostProcesser._hideDuplicatedMaterialOfSameModelloc("地毯", singleFigureElements, groupMemberFigureElements);
        // 隐藏挂画重复素材
        await MatchingPostProcesser._hideDuplicatedMaterialOfSameModelloc("挂画", singleFigureElements, groupMemberFigureElements, false);
        // 隐藏背景墙重复素材
        await MatchingPostProcesser._hideDuplicatedMaterialOfSameModelloc("背景墙", singleFigureElements, groupMemberFigureElements);
    }

    /**
     *  若台面家具匹配到组合素材，则移除其装饰图元的素材（采用清空图元素材的方式）
     *  @param toBeMatchedFigureElements 需要处理的图元列表
    */
    public static removeRedundantDecorations(toBeMatchedFigureElements: TFigureElement[]): void {
        toBeMatchedFigureElements.forEach((fe) => {
            if (fe.modelLoc.endsWith("饰品")) {
                if (fe.getAttatchedTargetElement()?.haveMatchedGroupMaterial()) {
                    Logger.instance.info("Remove Redundant Decoration: " + fe.toString());
                    fe.clearAllMatchedMaterials();
                }
            }
        });
    }

    /*
    针对指定的图元列表，根据图元匹配到的素材生成顶视图Image对象。
    排除掉硬装图元（地面、墙面、墙体、天花板），硬装图元不需要生成顶视图。
    */
    public static async createTopViewTexturesForProductMaterial(roomItem: TRoom, toBeMatchedFigureElements: TFigureElement[]) {

        let promises: Promise<any>[] = [];
        let hardFigures = [...roomItem._ceilling_list, roomItem.tile, roomItem.wallTexture, ...roomItem._door_figure_list];

        const makePromise = (fe: TFigureElement, type: number) => {
            if (!fe._matched_material) return;
            let targetImageUrl = type == 0 ? fe._matched_material.topViewImage + '?x-oss-process=image/resize,m_fixed,w_250,h_250' : fe._matched_material.imageUrl;
            if (targetImageUrl) {
                let promise = new Promise<void>((resolve, reject) => {
                    let img = new Image();
                    img.crossOrigin = "Anonymous";
                    img.src = targetImageUrl;
                    img.onload = () => {
                        fe.pictureViewImg = img;
                        if(LayoutAI_App.instance.scene3DManager)
                        {
                            LayoutAI_App.instance.scene3DManager.onElementUpdate(fe,{updateTexture:true});
                        }
        
                        resolve();
                    };
                    img.onerror = reject;
                });
                promises.push(promise);
            }
        }
        toBeMatchedFigureElements.forEach((fe) => {
            if (hardFigures.includes(fe)) return;
            makePromise(fe, 0);
        });

        hardFigures.forEach((fe) => {
            // if(toBeMatchedFigureElements.includes(fe)) return;
            makePromise(fe, 1);
        })
        Promise.allSettled(promises).then(() => {
            if (LayoutAI_App.instance) {
                LayoutAI_App.instance.update();
            }
        }).catch((error) => {
            console.error('Error loading images:', error);
        });
    }

    /**
     * 对软装图元生成线框图材质
     * @param toBeMatchedFigureElements 需要处理的图元列表
     */
    public static async createWireframeTexturesForProductMaterial(roomItem: TRoom, toBeMatchedFigureElements: TFigureElement[]) {
        
        // await TDesignMaterialUpdater.instance.updateFurnituresDesignMaterials(toBeMatchedFigureElements, true);
        let promises: Promise<any>[] = [];
        const makePromise = (fe: TFigureElement, type: number) => {
            if (!fe._matched_material) return;
            let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;
            if (scene3d) {
                let promise = new Promise<void>((resolve, reject) => {
                    FigureTopViewer.instance.updateFigureWireFrameImage(fe);
                    resolve();
                });
                promises.push(promise);
            }
        }
        toBeMatchedFigureElements.forEach((fe) => {
            makePromise(fe, 0);
        });

        Promise.allSettled(promises).then(() => {
            if (LayoutAI_App.instance) {
                LayoutAI_App.instance.update();
            }
        }).catch((error) => {
            console.error('Error loading images:', error);
        });
    }

    /**
     * @description 单独生成图元的顶视图
     * @param toBeMatchedFigureElements 需要处理的图元列表
     */
    public static async createTopViewTextureForSingleFigureElement(toBeMatchedFigureElements: TFigureElement[]) {
        toBeMatchedFigureElements.forEach((fe) => {
            if (fe.haveMatchedMaterial()) {
                let img = new Image();
                img.src = fe._matched_material.topViewImage || fe._matched_material.imageUrl;
                img.crossOrigin = "Anonymous";
                img.onload = () => {
                    fe.pictureViewImg = img;
                    if(LayoutAI_App.instance.scene3DManager)
                    {
                        LayoutAI_App.instance.scene3DManager.onElementUpdate(fe,{updateTexture:true});
                    }
                };
                
            }
        });
       
    }

    public static async createTopViewTexturesForCabinetMatatrial(cabinetElements: TFigureElement[], seriesSampleItem: TSeriesSample, logger: Logger) {
        if (!cabinetElements || cabinetElements.length == 0 || !seriesSampleItem) return;
        try {
            
            let cabinetStyleId = MaterialService.cabinetStyleIdMap.get(seriesSampleItem.seriesKgId.toString());
            if (cabinetStyleId != null) {
                let styleImage = MaterialService.cabinetStyleTextureMap.get(cabinetStyleId);
                if (styleImage) {
                    MatchingPostProcesser._updateRoomCabinetTopViewImage(cabinetElements, styleImage);
                }
                else {
                    let cabinetTextureUrl = await CabinetStyleService.getCabinetStyleBodyTextureUrl(cabinetStyleId);
                    if (cabinetTextureUrl == null || ENV == "hws" || ENV == "pre" || ENV == "dev" || MatchingPostProcesser.forceSaveCabinetStyle) {
                        // 如果不是独立站点，就优先从3D取风格刷信息
                        if (!is_standalone_website) {
                            SunvegaAPI.WholeHouse.CabinetStyle.bindFetchCabinetStyleXmlCallBack(async (data: SunvegaAPI.WholeHouse.CabinetStyle.FetchCabinetStyleXmlCallBackInput) => {
                                const parser = new DOMParser();
                                const styleXml = parser.parseFromString(data.styleXml, "text/xml");
                                const StyleComposeXmlNodes = styleXml.getElementsByTagName("StyleCompose");
                                if (StyleComposeXmlNodes == null || StyleComposeXmlNodes.length == 0) return;
                                const styleCompose = StyleComposeXmlNodes[0];
                                const items = styleCompose.getElementsByTagName("item");
                                const styleBrush: StyleBrush = { items: [] } as StyleBrush;
                                for (const item of items) {
                                    const styleComposeItem: StyleComposeItem = {} as StyleComposeItem;
                                    styleBrush.items.push(styleComposeItem);
                                    const name = item.getAttribute("name");
                                    if (name == "柜体材质") {
                                        const materialImgPath = "https://img3.admin.3vjia.com" + item.getAttribute("materialImgPath");
                                        CabinetStyleService.saveCabinetStyle(cabinetStyleId, materialImgPath);
                                        MaterialService.cabinetStyleTextureMap.set(cabinetStyleId, materialImgPath);
                                        MatchingPostProcesser._updateRoomCabinetTopViewImage(cabinetElements, materialImgPath);
                                    }
                                    styleComposeItem.name = name;
                                    styleComposeItem.materialImgPath = item.getAttribute("materialImgPath");
                                    styleComposeItem.id = item.getAttribute("id");
                                    styleComposeItem.materialId = item.getAttribute("materialId");
                                    styleComposeItem.materialName = item.getAttribute("materialName");
                                    styleComposeItem.categoryCode = item.getAttribute("categoryCode");
                                    styleComposeItem.isRotate = item.getAttribute("isRotate") == "true";
                                    styleComposeItem.picHeight = parseInt(item.getAttribute("picHeight"));
                                    styleComposeItem.picWidth = parseInt(item.getAttribute("picWidth"));
                                    styleComposeItem.applyCondition = item.getAttribute("applyCondition");
                                    styleComposeItem.useType = parseInt(item.getAttribute("useType"));
                                }
                                styleBrush.id = cabinetStyleId;
                                styleBrush.applyCondition = styleCompose.getAttribute("applyCondition");
                                styleBrush.materials = styleCompose.getAttribute("materials").split(",");
                                styleBrush.defaultTab = styleCompose.getAttribute("defaultTab").split(",");
                                styleBrush.isCanEnable = styleCompose.getAttribute("isCanEnable") == "true";
                                MaterialService.saveStyleBrush(cabinetStyleId, styleBrush);
                            });
                            SunvegaAPI.WholeHouse.CabinetStyle.asyncFetchCabinetStyleXml(cabinetStyleId as any);
                        }
                    }
                    if(cabinetTextureUrl) 
                    {
                        MatchingPostProcesser._updateRoomCabinetTopViewImage(cabinetElements, cabinetTextureUrl);
                        MaterialService.cabinetStyleTextureMap.set(cabinetStyleId, cabinetTextureUrl);
                    }

                    // debug && this.log_figure_elements(null, cabinetElements, logger, seriesSampleItem.seriesName + seriesSampleItem.ruleId + " cabinet top view url: " + cabinetTextureUrl + "\n Cabinets ");
                }
            }
            else {
                logger.info("There is no cabinet style brush for this series: " + seriesSampleItem.seriesName);
                const defaultCabinetTopViewImage = "https://3vj-designmaterial.3vjia.com/UpFile/C00000022/PMC/DesignMaterial/202301/184619913/T445-53%E8%8A%B1%E6%A0%B7%E5%B9%B4%E5%8D%8E.jpg";
                MatchingPostProcesser._updateRoomCabinetTopViewImage(cabinetElements, defaultCabinetTopViewImage);
            }
        } catch (e) {
            console.error(e);
        }
    }

    public static async createTopViewTexuturesForHardMaterial(roomItem: TRoom, seriesSampleItem: TSeriesSample, hard_elements: TFigureElement[]) {
        if (!hard_elements) hard_elements = roomItem.getHardDecorationList();

        hard_elements.forEach((target_figure) => {
            let matchedMaterial = target_figure._matched_material;
            if (matchedMaterial) {
                if (compareNames([target_figure.sub_category], ["地面", "墙面"])) {
                    let img = new Image();
                    img.src = matchedMaterial.imageUrl;
                    img.crossOrigin = "Anonymous";
                    img.onload = () => {
                        if (target_figure) {
                            target_figure.pictureViewImg = img;
                            if (LayoutAI_App.instance) {
                                LayoutAI_App.instance.update();
                            }
                        }
                    }
                }
            }

        })
    }

    private static async _updateRoomCabinetTopViewImage(cabinetElements: TFigureElement[], bodyTextureUrl: string) {
        if (!cabinetElements || !bodyTextureUrl) return;

        cabinetElements.forEach((fe) => {
            if (fe.haveMatchedCustomCabinet() && fe._matched_material.topViewImage == null) {
                fe._matched_material.topViewImage = bodyTextureUrl;
                let img = new Image();
                img.src = fe._matched_material.topViewImage;
                fe.pictureViewImgUrl = fe._matched_material.topViewImage;
                img.crossOrigin = "Anonymous";
                img.onload = () => {
                    fe.pictureViewImg = img;
                    if(LayoutAI_App.instance.scene3DManager)
                    {
                        LayoutAI_App.instance.scene3DManager.onElementUpdate(fe,{updateTexture:true});
                    }
    
                    LayoutAI_App.instance.update();
                };
                fe._candidate_materials.forEach((item) => {
                    if (item.modelFlag !== '1') {
                        item.topViewImage = fe._matched_material.topViewImage;
                    }
                });
            }
        });
    };

    public static postMatchingElectricityElement(fixture_candidates: { [key: string]: { seriesKgId: string, seriesKgName?: string, figure_element: TFigureElement }[] }, figure_element: TFigureElement, seriesKgId: string) {
        if (fixture_candidates[figure_element.category]) {
            let target_result = fixture_candidates[figure_element.category].find((val) => val.seriesKgId == seriesKgId);
            if (target_result && target_result.figure_element) {
                let candidate_materials = target_result.figure_element._candidate_materials || [];
                figure_element._candidate_materials = candidate_materials.map(material => { return { ...material } });

                let tags = [...figure_element.tags, figure_element.sub_category];
                let candidate_score = (item: I_MaterialMatchingItem) => {
                    if (compareNames([item.name], tags, false)) {
                        return 2;
                    }
                    if (compareNames([item.name], tags, true)) {
                        return 1;
                    }
                }

                figure_element._candidate_materials.sort((a, b) => candidate_score(b) - candidate_score(a));
                figure_element._candidate_materials.forEach((mat) => mat.figureElement = figure_element);
                figure_element._matched_material = figure_element._candidate_materials[0];

                if (!figure_element._matched_material) return;
                figure_element.matched_rect = figure_element.rect.clone();
                figure_element.matched_rect.cornerDepth = figure_element.rect.cornerDepth;
                figure_element.matched_rect.cornerWidth = figure_element.rect.cornerWidth;
                let r_center = figure_element.matched_rect.rect_center;
                figure_element.matched_rect._w = figure_element._matched_material.length;
                figure_element.matched_rect._h = figure_element._matched_material.width;
                figure_element.matched_rect.rect_center = r_center;

                figure_element._matched_material.targetPosition = Vec3toMeta(figure_element.matched_rect.rect_center_3d);
                figure_element._matched_material.targetRotation = { x: 0, y: 0, z: figure_element.rect.rotation_z };


            }

        }
    }

    /**
     *  对未匹配到素材的图元添加高亮标记(用于在绘制时标红边框)
     *  @param toBeMatchedFigureElements 需要处理的图元列表
     *  @param matchedMaterials 已匹配到的素材列表
     */
    public static spotlightingUnmatchedFigureElements(toBeMatchedFigureElements: TFigureElement[], matchedMaterials: I_MaterialMatchingItem[]) {
        // 遍历所有需要匹配套系素材的图元，如果图元没有匹配到素材，也没有默认素材，则设置图元为高亮状态（绘制效果就是标红）
        for (let fi = 0; fi < toBeMatchedFigureElements.length; fi++) {
            let found = false;
            for (const material of matchedMaterials) {
                if (material.index == fi) {
                    found = true;
                    break;
                }
            }
            if (!found && !toBeMatchedFigureElements[fi].haveDefaultMaterial()) {
                toBeMatchedFigureElements[fi].spotlight = true;
                if (toBeMatchedFigureElements[fi]._is_decoration === true || toBeMatchedFigureElements[fi]._is_sub_board === true) {
                    toBeMatchedFigureElements[fi].spotlight = false;
                }
            }
        }
    }

    /**
     *  如果台面家具匹配到组合素材，则隐藏附加在台面家具的装饰图元
     *  @param toBeMatchedFigureElements 需要处理的图元列表
     *  @param logger 日志记录器
     */
    public static hideDecorationMaterialIfNeeded(toBeMatchedFigureElements: TFigureElement[]) {
        // 遍历所有需要匹配套系素材的图元，如果是属于依附在台面家具的装饰图元，则进一步判断装饰图元所依附的台面家具图元是否匹配到组合素材，
        // 如果是，则将装饰图元的素材设置为不可见
        toBeMatchedFigureElements.forEach((fe: TFigureElement) => {
            if (fe.haveMatchedMaterial()) {
                if (fe.modelLoc.endsWith("饰品")) {
                    if (fe.getAttatchedTargetElement()?.haveMatchedGroupMaterial()) {
                        Logger.instance.info("Mark this decoration as invisible:\n    " + fe.toString());
                        fe.markMaterialAsInvisible();
                    }
                }
            }
        });
    }

    /**
     * 修改素材的目标布置位置和尺寸
     * 主要功能:
     * 1. 处理特定家具的旋转问题,如果图元需要镜像旋转,则调整旋转角度
     * 2. 设置素材的目标布置位置为图元的中心点位置
     * 3. 设置素材的目标旋转角度为图元的旋转角度
     * 4. 根据不同类型的素材(组合、可缩放长宽高、可缩放长宽等)设置目标尺寸:
     *    - 组合素材:使用素材原始尺寸
     *    - 可缩放长宽高素材:使用图元的长宽高
     *    - 可缩放长宽素材:使用图元的长宽,高度使用素材原始高度
     *    - 可缩放长高素材:使用图元的长度和高度,宽度使用素材原始宽度
     *    - 可缩放长度素材:使用图元的长度,宽高使用素材原始尺寸
     *    - 其他素材:使用素材原始尺寸
     * 5. 对定制柜类型的素材进行特殊尺寸调整
     * 6. 限制部分素材（窗帘）的最大宽度
     * 7. 对厨房柜子类素材使用图元的实际尺寸
     * 8. 最后检查素材高度，限制素材高度不超过层高
     * @param figure_element 图元对象
     * @param material 素材对象
     */
    public static modifyMaterialPositionSize(figure_element: TFigureElement, material: I_MaterialMatchingItem) {
        // 先处理部分旋转的问题
        if (compareNames([figure_element.sub_category], TMaterialMatchingConfigs.NoMirrorWithRotationModelLocs)) {
            if (figure_element.rect.u_dv_flag < 0) {

                let target_rect = figure_element.rect;
                let r_center = target_rect.rect_center;
                target_rect.rotation_z -= Math.PI / 2;;
                target_rect._u_dv_flag = 1;
                target_rect.rect_center = r_center;

            }

        }
        material.targetPosition = figure_element.rect.rect_center;
        material.targetRotation = { x: 0, y: 0, z: figure_element.rect.rotation_z };

        TMaterialMatchingConfigs._storey_height = MatchingPostProcesser.storey_height;
        if (material.modelLoc.endsWith("组合")) {
            material.targetSize = { length: material.length, width: material.width, height: material.height };

        }
        else if (TMaterialMatchingConfigs.lengthWidthHeightScaleableModelLocs.indexOf(material.modelLoc) > -1) {
            material.targetSize = { length: figure_element.rect.w, width: figure_element.rect.h, height: figure_element.params.height };
        }
        else if (TMaterialMatchingConfigs.lengthWidthScaleableModelLocs.indexOf(material.modelLoc) > -1) {
            material.targetSize = { length: figure_element.rect.w, width: figure_element.rect.h, height: material.height };
        }
        else if (TMaterialMatchingConfigs.lengthHeightScaleableModelLocs.indexOf(material.modelLoc) > -1) {
            material.targetSize = { length: figure_element.rect.w, width: material.width, height: figure_element.params.height };
        }
        else if (TMaterialMatchingConfigs.lengthScaleableModelLocs.indexOf(material.modelLoc) > -1) {
            material.targetSize = { length: figure_element.rect.w, width: material.width, height: material.height };
        }
        else {
            material.targetSize = { length: material.length, width: material.width, height: material.height };
        }

        if (FigureCategoryManager.isCustomCabinet(figure_element)) {
            // 调整定制柜的尺寸
            MatchingPostProcesser._adjustCabinetSize(figure_element, material);
        }
        if (TMaterialMatchingConfigs.modelLoc2MaxWidthMap.has(material.modelLoc)) {
            // 限制部分素材（窗帘）的最大宽度
            let maxWidth = TMaterialMatchingConfigs.modelLoc2MaxWidthMap.get(material.modelLoc);
            if (material.targetSize.width > maxWidth) {
                material.targetSize.width = maxWidth;
            }
        }


        // 如果是厨房的柜子
        if (compareNames([figure_element.sub_category,figure_element.category], TGraphBasicConfigs.KitchenCabinetsCategories)
            &&  figure_element.sub_category != "冰箱") {
            material.targetSize = { length: figure_element.length, width: figure_element.depth, height: figure_element.height };
        }

        if (material.targetSize.height > TMaterialMatchingConfigs._storey_height) material.targetSize.height = TMaterialMatchingConfigs._storey_height - material.targetPosition.z;
    }

    private static _adjustCabinetSize(figure_ele: TFigureElement, matchedMaterial: I_MaterialMatchingItem) {
    }

    public static adjustMatchedMaterialsAndFigureElements(room: TRoom, allMatchedMaterial: I_MaterialMatchingItem[]) {
        if (!allMatchedMaterial) return;

        for (let material of allMatchedMaterial) {
            //将图元和素材进行关联
            let feIndex = material["index"];
            let figure_element = material.figureElement;
            if (figure_element == null || !figure_element) {
                console.error("Error: invalid index value " + feIndex)
                continue;
            }
            figure_element._matched_material = material;

            if (!material.modelId) {
                // console.error("Error: invalid material id, " + JSON.stringify(material))
                continue;
            }
            figure_element.params.materialId = material.modelId;

            // 修改素材的目标布置位置和尺寸
            MatchingPostProcesser.modifyMaterialPositionSize(figure_element, material);

            if (compareNames([material?.figureElement?.category], ["吊顶"])) {
                //将吊顶匹配素材的目标布置位置和尺寸，设置为当前图元的目标布置位置和尺寸
                material.figureElement.matched_rect = material.figureElement.rect.clone();
            } else {
                // 将素材的位置尺寸信息同步到图元的matchedRect（表示匹配到素材后的图元位置和尺寸）中,
                // 并修改素材的目标放置z值
                MatchingPostProcesser.syncMatchedRectAndModifyMaterialPositionZ(material, room);
            }
        }
    }

    /**
     *  将素材的位置尺寸信息同步到图元的matchedRect（表示匹配到素材后的图元位置和尺寸）中,
     *  并修改素材的目标放置z值
     */
    private static syncMatchedRectAndModifyMaterialPositionZ(material: I_MaterialMatchingItem, room: TRoom) {
        let figure_element = material.figureElement;

        if (!material.modelId) return;

        // 同步matched_rect 数据
        let sync_rect_pos = (from_rect: ZRect, to_rect: ZRect) => {
            // 如果图元属于背靠墙的类型，则设置目标矩形to_rect）的back_center为源矩形from_rect的back_center
            if (compareNames([figure_element.category, figure_element.sub_category], TMaterialMatchingConfigs._backto_wall_modelLoc_list) || (compareNames([material.modelLoc], ["地柜", "吊柜"]))) {
                to_rect.back_center = from_rect.back_center;
                to_rect.updateRect();
            }
            // 否则默认居中
            else {
                to_rect.rect_center = from_rect.rect_center;
            }

            // 如果图元属于侧靠墙的类型，则设置目标矩形to_rect的back_center为源矩形from_rect的back_center
            if (TMaterialMatchingConfigs._sideto_wall_modelLoc_list.has(material.modelLoc)) {
                // 计算家具要贴墙时,需要在水平方向移动多少距离
                let t_x = -from_rect.w / 2 + to_rect._w / 2;
                // 将计算好的位置转换成实际的三维空间坐标
                let pos = from_rect.unproject({ x: t_x, y: -from_rect.h / 2 });
                to_rect.back_center = pos;
                to_rect.updateRect();
            }
        }

        if (!figure_element.matched_rect) {
            figure_element.matched_rect = figure_element.rect.clone();
            figure_element.matched_rect.cornerDepth = figure_element.rect.cornerDepth;
            figure_element.matched_rect.cornerWidth = figure_element.rect.cornerWidth;
        }
        else {
            // 将图元的matchedRect的值同步到图元的rect中
            sync_rect_pos(figure_element.matched_rect, figure_element.rect);
        }



        // 修改matched_rect的尺寸
        if(!FigureCategoryManager.isCustomCabinet(figure_element))
        {
            figure_element.matched_rect.length = material.targetSize.length;
            figure_element.matched_rect.depth = material.targetSize.width;
        }

        figure_element.matched_rect.updateRect();
        // console.log(material.modelLoc, material.targetSize, material.length,material.width,material.height,figure_element.matched_rect.w,figure_element.matched_rect.h);

        sync_rect_pos(figure_element.rect, figure_element.matched_rect);


        // 设置素材的位置为图元的matched_rect的中心点
        material.targetPosition = Vec3toMeta(figure_element.matched_rect.rect_center);

        TMaterialMatchingConfigs._storey_height = MatchingPostProcesser.storey_height;

        // 贴顶墙处理
        if (TMaterialMatchingConfigs._align_top_modelLoc_list.has(material.modelLoc) 
        || (material.modelLoc.indexOf("灯") >= 0 && material.modelLoc.indexOf("落地灯") < 0)) {
            // 获取灯具对应位置的吊顶 实现灯具吸附到吊顶上
            let furnitureEntity = figure_element.rect._attached_elements["Entity"];
            let targetRoomEntity = furnitureEntity?.roomEntity;
            let zvalToTop = 1;
            if(targetRoomEntity )
            {
                targetRoomEntity.room_ceiling_entity.ceiling_layer_entities.forEach((layerEntity:any) => {
                    if(layerEntity.rect.containsPoint(figure_element.rect.rect_center))
                    {
                        if(layerEntity.children && layerEntity.children.length > 0)
                        {
                            zvalToTop = layerEntity.children[0].zvalToTop;
                        }else{
                            zvalToTop = layerEntity.zvalToTop;
                        }
                    }
                });
            }

            // 设置灯具的z坐标值为：层高 - 吊顶高度 - 灯具高度
            material.targetPosition.z = TMaterialMatchingConfigs._storey_height - zvalToTop - material.height;
        }
        // 对于落地的图元，做贴地板的处理，设置目标放置z值为：地板厚度
        else if (TMaterialMatchingConfigs._grounded_modelLoc_list.has(material.modelLoc)) {
            material.targetPosition.z = room._room_entity.floor_thickness;
        }
        // 对于浴室柜，做离地或落地处理：
        // 1. 如果浴室柜的offLandRule为离地，则设置目标放置z值为：浴室柜离地高度
        // 2. 如果浴室柜的offLandRule为落地，则设置目标放置z值为：地板厚度
        else if (material.modelLoc == "浴室柜") {
            if (material.offLandRule == "离地") {
                // 离地处理，设置目标放置z值为：浴室柜离地高度
                material.targetPosition.z = TMaterialMatchingConfigs._bathroom_cabinet_default_offland_height;
            } else {
                // 落地处理，设置目标放置z值为：地板厚度
                material.targetPosition.z = room._room_entity.floor_thickness;
            }
        }
        // 对于马桶，做离地或落地处理：
        // 1. 如果马桶的offLandRule为离地，则设置目标放置z值为：150
        // 2. 如果马桶的offLandRule为落地，则设置目标放置z值为：地板厚度
        else if (material.offLandRule && material.modelLoc == "马桶") {
            if (material.offLandRule == "离地") {
                material.targetPosition.z = 150;
            } else if (material.offLandRule == "落地") {
                material.targetPosition.z = room._room_entity.floor_thickness;
            }
        }
        // 对于高度大于1000的电视柜，电视柜上的电视图元的素材做清空处理。
        else if (material.modelLoc === "电视柜") {
            // 将电视置为空
            if (material.height > 1000) // 认为是电视柜
            {
                let tv_cabinet_element: TFigureElement = material.figureElement;
                let tv_furniture_element: TFigureElement = null;
                for (let furniture of room._furniture_list) {
                    if (furniture.haveMatchedMaterial() && furniture.modelLoc === "电视") {
                        tv_furniture_element = furniture;
                    }
                }

                if (tv_furniture_element && tv_cabinet_element.rect.containsPoint(tv_furniture_element.rect.rect_center, 100)) {
                    tv_furniture_element._matched_material.deleted_model_id = tv_furniture_element._matched_material.modelId;
                    tv_furniture_element._matched_material.modelId = null;
                    //console.log("让电视的modelId设置为空");
                }
            }
            material.targetPosition.z = room._room_entity.floor_thickness + (material.pz > 0 ? material.pz : 0);
        }
        // 跟上述一样的处理逻辑：对于高度大于1000的电视柜，电视柜上的电视图元的素材做清空处理。
        else if (compareNames([material.modelLoc], ["电视"], false) && material.modelId != null) {
            let tv_furniture_element: TFigureElement = material.figureElement;
            let tv_cabinet_element: TFigureElement = null;
            for (let furniture of room._furniture_list) {
                if (furniture.haveMatchedMaterial() && furniture.modelLoc === "电视柜" && furniture._matched_material.height > 1000) {
                    tv_cabinet_element = furniture;
                    break;
                }
            }
            if (tv_cabinet_element != null) {
                if (tv_cabinet_element.rect.containsPoint(tv_furniture_element.rect.rect_center, 100)) {
                    material.deleted_model_id = material.modelId;
                    material.modelId = null;
                    //console.log("让电视的modelId设置为空");
                }
            }
            else {
                material.targetPosition.z = figure_element.rect.rect_center_3d.z + (material.pz > 0 ? material.pz : 0);
            }
        }
        else {
            // 对于其他图元，使用图元的rect的z值作为素材目标放置z值
            material.targetPosition.z = figure_element.rect.rect_center_3d.z;
        }
        figure_element.rect.zval = material.targetPosition.z;
        figure_element.matched_rect.zval = material.targetPosition.z;



    }

    public static adjust3dPreviewMaterialsAndFigureElements(room: TRoom, allMatchedMaterial: I_MaterialMatchingItem[]) {
        if (!allMatchedMaterial) return;

        for (let material of allMatchedMaterial) {
            let feIndex = material["index"];
            let figure_element = material.figureElement;
            if (figure_element == null || !figure_element) {
                console.error("Error: invalid index value " + feIndex)
                continue;
            }
            figure_element._3dpreview_matched_material = material;

            if (!material.modelId) {
                // console.error("Error: invalid material id, " + JSON.stringify(material))
                continue;
            }

            MatchingPostProcesser.modifyMaterialPositionSize(figure_element, material);

            if (compareNames([material?.figureElement?.category], ["吊顶"])) {
                material.figureElement.matched_rect = material.figureElement.rect.clone();
            } else {
                MatchingPostProcesser.syncMatchedRectAndModifyMaterialPositionZ(material, room);
            }
        }
    }
}