import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useEffect, useRef, useState } from "react";
import { BuildingService, I_BuildRecord } from "@/Apps/LayoutAI/Services/Basic/BuildingService";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { DistrictPopup } from "./districtPopup";
import Icon from '@/components/Icon/icon';

/**
 * @description 主页
 */
export enum HouseSerachPopupEvents{
    setIsVisible = "setIsVisible"
}
const HouseSerachPopup: React.FC = () => {
    const { t } = useTranslation()
    const { styles } = useStyles();

    const inputRef = useRef(null);
    const [buildingRecords, setBuildingRecords] = useState<I_BuildRecord[]>([]);
    const [candidateBuildingNames,setCandidateBuildingName] = useState<{buildingName:string,roomCount:number}[]>([]);
    const [inputText,setInputText] = useState<string>("");
    const [isVisible,setIsVisible] = useState<boolean>(false);
    const [isShowDistrict,setIsShowDistrict] = useState<boolean>(false);
    const [city,setCity] = useState<{name:string,code:string}>({name:"广州市",code:"440100"}); /*[i18n:ignore]*/
    const onKeywordInputChange =async (ev: any)=>{
        let val = (ev.target.value);
        let res = await BuildingService.getBuildingList(val,city.code);

        setCandidateBuildingName(res||[]);
    }

    const onCityChanged = async (city:{name:string,code:string})=>{
        let res = await BuildingService.search("",city.code);
        let records = res?.records || [];
        setBuildingRecords(records|| []);
    }
    const onSearch = async () => {  

        if(inputRef.current)
        {
            
            let val = inputRef.current.value;
            let res = await BuildingService.search(val,city.code);
            let records = res?.records || [];
            setBuildingRecords(records|| []);
        }
    }


    const ipParse = async ()=>{
        let data  = await BuildingService.ipParse();
        if(data && data.city)
        {
            setCity(data.city);
            onCityChanged(data.city);
        }
    }
    
    useEffect(()=>{
        LayoutAI_App.on(HouseSerachPopupEvents.setIsVisible,(t:boolean)=>{
            setIsVisible(t);
        });
        ipParse();

    },[]);
    

    

    if(!isVisible)
    {
        return (<></>);
    }
    return (
        <div className={styles.root}>
        <Icon
          iconClass="iconclose1"
          className='iconicon'
          onClick={() => {
           setIsVisible(false);
          }}
        ></Icon>
            <div className={styles.row}>
                 <div className={styles.hx_logo}></div>
            </div>
            <div className={styles.row}>
                <div className="houseSearch">
                    <div>
                        <div className="style_trigger__1c72413f">
                            <DistrictPopup is_visible={isShowDistrict} onSelected={(city)=>{setCity(city);onCityChanged(city); setIsShowDistrict(false)}}></DistrictPopup>
                            <span className="style_city_label__1c72413f" onClick={()=>setIsShowDistrict(!isShowDistrict)} >{city.name}</span>
                            <span role="img" aria-label="caret-down" className="anticon anticon-caret-down">
                                <svg viewBox="0 0 1024 1024" focusable="false" data-icon="caret-down" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"></path></svg>
                            </span>
                        </div>
                    </div>
                    <div className="line"></div>
                    {candidateBuildingNames && candidateBuildingNames.length>0 && 
                    <div style={{position:"absolute"}}>
                        <div className="candidate_buiding_names">
                            <div className="select-operate"><div>你是不是想找</div></div>
                            {candidateBuildingNames.map((data,index)=>
                            <div key={"candidate"+index} className="select_item" onClick={async ()=>{
                                if(inputRef && inputRef.current)
                                {
                                    inputRef.current.value = data.buildingName;
                                    let res = await BuildingService.search(data.buildingName,city.code);
                                    let records = res?.records || [];
                                   setBuildingRecords(records|| []);
                                }
                                setCandidateBuildingName([]);
                            }}>
                                <div className="select-item_left"><b>{data.buildingName}</b></div>
                                <div className="select-item_right"><span>{data.roomCount}{t("个结果")}</span></div>
                            </div>)}
                        </div>
        
                    </div>
                    }
                    <input  placeholder={t("输入楼盘、小区名称查找户型")} className="ant-input search-input" type="text" defaultValue={inputText} ref={inputRef} onChange={onKeywordInputChange}></input>
                    <div className="search-btn" onClick={onSearch}>{t("搜户型")}</div>
                </div>
            </div>
            <div className={styles.row+" buildingContent"}>
                {buildingRecords.map((record,index)=><div  className="house-card" key={"build_result_"+index} onClick={()=>{
                    if(LayoutAI_App.instance)
                    {
                        LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId,{id:record.id,name:record.buildingName});
                        // LayoutAI_App.emit(EventName.LayoutSchemeOpened,{id:null, name:name});
                        setIsVisible(false);
                    }
                }}>
                    <img src={record.imagePath} alt={record.buildingName}/>
                    <div className="house-card__info">
                        <div className="name">{record.buildingName}</div>
                        <div className="detail-wrap">
                            <div className="houseType">{record.roomTypeName} | {record.area+"m²"}  </div>
                            <div className="location">{record.provinceName} / {record.cityName}</div>
                        </div>
                    </div>
                </div>)}
            </div>

        </div>
    );
};

export default observer(HouseSerachPopup);
