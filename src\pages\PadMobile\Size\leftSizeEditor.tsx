import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useEffect, useState, useCallback } from "react";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { Slider, Input, Checkbox } from "@svg/antd"; // 确保你有这个组件
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import IconFont from "@/components/IconFont/iconFont";
import { mobileModel } from "react-device-detect";

/**
 * @description 尺寸
 */

interface Properties {
    width: { defaultValue: number; onChange: (value: number) => void };
    length: { defaultValue: number; onChange: (value: number) => void };
    height: { defaultValue: number; onChange: (value: number) => void };
    pos_z: { defaultValue: number; onChange: (value: number) => void };
}

const LeftSizeEditor: React.FC<{mobileType?:number}> = (props:{mobileType?:number}) => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const store = useStore();
    const [length, setLength] = useState<number>(0);
    const [width, setWidth] = useState<number>(0);
    const [height, setHeight] = useState<number>(0);
    const [pos_z, setPos_z] = useState<number>(0);
    const [properties, setProperties] = useState<Properties | null>(null);
    const [geometric, setGeometric] = useState<boolean>(false);
    // 隐藏蒙层
    const hideOverlay = () => {
        store.homeStore.setSizeInfo({
            type: '',
            visible: false,
        });
    };
    useEffect(() => {
        if (LayoutAI_App.instance) {
            LayoutAI_App.on(EventName.AttributeHandle, (params) => {
                if (params.mode === 'init') {
                    setWidth(Math.round(params?.properties?.width?.defaultValue));
                    setLength(Math.round(params?.properties?.length?.defaultValue));
                    setHeight(Math.round(params?.properties?.height?.defaultValue));
                    setPos_z(Math.round(params?.properties?.pos_z?.defaultValue));
                    setProperties(params?.properties);
                    store.homeStore.setAttribute(params);
                }
            });
        }
    }, []);

    //
    const handleChange = useCallback((setter: React.Dispatch<React.SetStateAction<number>>, property: keyof Properties) => {
        return (value: number) => {
            setter(value);
            properties?.[property]?.onChange(value);
            if (geometric) {
                // 计算缩放比例
                let lengthRatio = 1;
                let widthRatio = 1;
                let heightRatio = 1;
    
                if (property === 'length') {
                    lengthRatio = value / length; // 计算新的长度比例
                    setWidth(Math.round(width * lengthRatio)); // 更新宽度
                    setHeight(Math.round(height * lengthRatio)); // 更新高度
                    properties?.['width']?.onChange(width * lengthRatio);
                    properties?.['height']?.onChange(height * lengthRatio);
                } else if (property === 'width') {
                    widthRatio = value / width; // 计算新的宽度比例
                    setLength(Math.round(length * widthRatio)); // 更新长度
                    setHeight(Math.round(height * widthRatio)); // 更新高度
                    properties?.['length']?.onChange(length * widthRatio);
                    properties?.['height']?.onChange(height * widthRatio);
                } else if (property === 'height') {
                    heightRatio = value / height; // 计算新的高度比例
                    setLength(Math.round(length * heightRatio)); // 更新长度
                    setWidth(Math.round(width * heightRatio)); // 更新宽度
                    properties?.['length']?.onChange(length * heightRatio);
                    properties?.['width']?.onChange(width * heightRatio);
                }
            }
        };
    }, [properties,geometric,length,width,height]);


    const verticalDIv = () => {
        return(
            <>
                <div className={styles.sliderContainer}>
                                {t('宽度')}
                                <Slider
                                    className={styles.slider}
                                    value={length}
                                    onChange={handleChange(setLength, 'length')}
                                    min={0}
                                    max={6000}
                                    step={1}
                                />
                                <Input
                                    className={styles.input}
                                    value={length}
                                    suffix={'mm'}
                                    onChange={(e) => handleChange(setLength, 'length')(Number(e.target.value))}
                                />
                </div>
                <div className={styles.sliderContainer}>
                    {t('深度')}
                    <Slider
                        className={styles.slider}
                        value={width}
                        onChange={handleChange(setWidth, 'width')}
                        min={0}
                        max={6000}
                        step={1}
                    />
                    <Input
                        className={styles.input}
                        value={width}
                        suffix={'mm'}
                        onChange={(e) => handleChange(setWidth, 'width')(Number(e.target.value))}
                    />
                </div>
                <div className={styles.sliderContainer}>
                    {t('高度')}
                    <Slider
                        className={styles.slider}
                        value={height}
                        onChange={handleChange(setHeight, 'height')}
                        min={0}
                        max={2800}
                        step={1}
                    />
                    <Input
                        className={styles.input}
                        value={height}
                        suffix={'mm'}
                        onChange={(e) => handleChange(setHeight, 'height')(Number(e.target.value))}
                    />
                </div>
                <div className={styles.sliderContainer}>
                    {t('投影面积(宽*高)')}
                    <label  className={styles.input} style={{textAlign:"right"}}>
                        {(length*height/1000/1000).toFixed(2)}m²
                    </label>
                </div> 
            </>
        )
    }

    const horizontalDiv = () => {
        return(
            <>
                <div className={styles.sliderContainer}>
                    {t('宽度')}
                    <Input
                        className={styles.input}
                        value={length}
                        suffix={'mm'}
                        onChange={(e) => handleChange(setLength, 'length')(Number(e.target.value))}
                    />
                </div>
                <Slider
                    className={styles.slider}
                    value={length}
                    onChange={handleChange(setLength, 'length')}
                    min={0}
                    max={6000}
                    step={1}
                />
                <div className={styles.sliderContainer}>
                    {t('深度')}
                   
                    <Input
                        className={styles.input}
                        value={width}
                        suffix={'mm'}
                        onChange={(e) => handleChange(setWidth, 'width')(Number(e.target.value))}
                    />
                </div>
                <Slider
                        className={styles.slider}
                        value={width}
                        onChange={handleChange(setWidth, 'width')}
                        min={0}
                        max={6000}
                        step={1}
                    />
                <div className={styles.sliderContainer}>
                    {t('高度')}
                    <Input
                        className={styles.input}
                        value={height}
                        suffix={'mm'}
                        onChange={(e) => handleChange(setHeight, 'height')(Number(e.target.value))}
                    />
                </div>
                <Slider
                        className={styles.slider}
                        value={height}
                        onChange={handleChange(setHeight, 'height')}
                        min={0}
                        max={2800}
                        step={1}
                    />
                <div className={styles.sliderContainer}>
                    {t('投影面积(宽*高)')}
                    <label  className={styles.input}>
                        {(length*height/1000/1000).toFixed(2)}m²
                    </label>
                </div>  
            </>
        )
    }

    return (
        <div className={styles.container+" leftSizeEditor"} onClick={(e) => e.stopPropagation()}>
            {
                store.homeStore.sizeInfo.type === 'size' ? 
                <>
                    <div className={styles.title}>
                        <div>
                            {t('尺寸调整')}
                            {store.homeStore.IsLandscape && <div className={styles.geometricCheck}>
                                <input type="checkbox"  onChange={(e) => {
                                setGeometric(e.target.checked);
                            }}></input>
                                <span>&nbsp;{t("等比缩放")}</span>
                            </div>}
  
                        </div>
                        {!store.homeStore.IsLandscape &&                            
                        <button className={styles.resetBtn} onClick={() =>{
                            if(geometric){
                                setGeometric(false);
                            }else{
                                setGeometric(true);
                            }
                        }}><IconFont type="icon-suoding1" /></button>}

                        <button className={styles.resetBtn} onClick={() =>{
                            LayoutAI_App.DispatchEvent(LayoutAI_Events.ResetSize, null);
                        }}>{ store.homeStore.IsLandscape ? t('恢复默认') : <IconFont type="icon-reset" />}</button>
               
                        </div>
  
                    { store.homeStore.IsLandscape ? verticalDIv() : horizontalDiv()}
                </> :
                <>
                    <div className={styles.title}>
                        <div>{t('离地')}</div>
                        
                        <button className={styles.resetBtn} onClick={() =>{
                            LayoutAI_App.DispatchEvent(LayoutAI_Events.ResetSize, null);
                        }}>{ store.homeStore.IsLandscape ? t('恢复默认') : <IconFont type="icon-reset" />}</button>
                    </div>
                    <div className={styles.sliderContainer}>
                        {t('离地')}
                        {store.homeStore.IsLandscape && 
                        <Slider
                            className={styles.slider}
                            value={pos_z}
                            onChange={handleChange(setPos_z, 'pos_z')}
                            min={0}
                            max={2800}
                            step={1}
                        />}
                        <Input
                            className={styles.input}
                            value={pos_z}
                            suffix={'mm'}
                            onChange={(e) => handleChange(setPos_z, 'pos_z')(Number(e.target.value))}
                        />
                    </div>
                    {!store.homeStore.IsLandscape && 
                    <Slider
                        className={styles.slider}
                        value={pos_z}
                        onChange={handleChange(setPos_z, 'pos_z')}
                        min={0}
                        max={2800}
                        step={1}
                    />}
                </>
            }
            
        </div>
    );
};

export default observer(LeftSizeEditor);