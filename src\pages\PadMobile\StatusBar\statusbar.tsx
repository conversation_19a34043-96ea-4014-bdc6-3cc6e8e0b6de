import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useEffect, useState, useRef } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { Button, message, Modal, Select } from "@svg/antd";
import Icon from '@/components/Icon/icon'
import { LayoutPopEvents } from "../layoutPopup/layoutPopup";
import { EventName } from "@/Apps/EventSystem";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { useStore } from '@/models';
import { IRoomEntityType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import IconFont from "@/components/IconFont/iconFont";
import { useNavigate } from 'react-router-dom';
import { _from, mini_APP } from "@/config";
import { uploadImageToOss } from "@/Apps/LayoutAI/Utils/file_utils";
import { AIGCService } from "@/Apps/LayoutAI/Services/AIGC/AIGCService";
import { AILightService } from "@/Apps/LayoutAI/Scene3D/light/AILightService";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
/**
 * @description 状态栏
 */
interface I_Btn {
    id: string;
    icon: string;
    label: string;
    eventName?: keyof typeof EventName;
    onClick?: () => void;
}

interface ConfigType {
    designStyle: Array<{
        text: string;
        value: string;
        cover: string;
    }>;
    rooms: Array<{
        text: string;
        value: string;
        inspire: string;
    }>;
}

const StatusBar: React.FC = () => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const store = useStore();
    const object_id = "StatusBar";
    const [btnList, setBtnList] = useState<I_Btn[]>([]);
    const [activeComponent, setActiveComponent] = useState<string | null>('null'); // 新增状态
    const layoutContainer: TLayoutEntityContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const [commandBar, setCommandBar] = useState<any[]>([]);
    const [_key, setKey] = useState<number>(0);
    const [combinatName, setCombinatName] = useState<string>('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isCircle, setIsCircle] = useState(false);
    const [isMove, setIsMove] = useState(false);
    const [config, setConfig] = useState<ConfigType>({
        designStyle: [],
        rooms: []
    });
    const navigate = useNavigate();
    const whole_bar_list = [
        {
            id: "Layout",
            label: t("布局"),
            icon: "icon-a-TypebujuStateDefault"
        },
        {
            id: "Matching",
            label: t("风格"),
            icon: "icon-a-TypefenggeStateDefault"
        },
        store.homeStore.viewMode === '2D' ? {
            id: "material",
            label: t("素材"),
            icon: "icon-a-TypesucaiStateDefault"
        } : null,
        {
            id: "attribute",
            label: t("属性"),
            icon: "icon-a-TypeshuxingStateDefault"
        },
        store.homeStore.viewMode === '3D_FirstPerson' ?   /*[i18n:ignore]*/
            {
                id: "view",
                label: t("视角"),
                icon: "icon-smarttemplate"
            } : null,
        store.homeStore.viewMode === '2D'
            ? {
                id: "aidraw",
                label: t("AI绘图"),
                icon: "icon-AIchutu"
            } : null,
        // {
        //     id: 'ailight',
        //     label: t('AI灯光'),
        //     icon: 'icon-AIchutu',
        // },
        mini_APP ? {
            id: "similar",
            label: t("相似匹配"),
            icon: "icona-Typexuanzebujian"
        } : null,
        mini_APP ? {
            id: "create",
            label: t("新建"),
            icon: "iconfile"
        } : null,

    ].filter(Boolean);


    let manyou_bar_list = [
        {
            id: "Layout",
            label: t("布局"),
            icon: "icon-a-TypebujuStateDefault"
        },
        {
            id: "Matching",
            label: t("风格"),
            icon: "icon-a-TypefenggeStateDefault"
        },
        {
            id: "submitDrawing",
            label: t("提交绘图"),
            icon: "icon-xuanranRender"
        },
        {
            id: "atlas",
            label: t("图册"),
            icon: "icon-tuku"
        }
    ].filter(Boolean);


    let commandBarData = [
        {
            id: "attribute",
            label: t("属性"),
            icon: "icon-a-TypeshuxingStateDefault"
        },
        {
            id: 'rotate',
            icon: 'icon-rotate',
            label: t('旋转'),
            disabled: false,
            divider: false,
        },
        {
            id: 'flip',
            icon: 'icon-horizontalflip_line',
            label: t('左右镜像'),
            disabled: false,
            divider: false,
        },
        {
            id: 'flip_vertical',
            icon: 'icon-verflip_line',
            label: t('上下镜像'),
            disabled: false,
            divider: false,
        },
        {
            id: 'copy',
            icon: 'icon-niantie',
            label: t('复制'),
            disabled: false,
            divider: false,
        },
        store.homeStore.selectEntity?.type === 'Furniture' && !store.homeStore.selectEntity?.figure_element.haveMatchedMaterial() && {
            id: 'size',
            icon: 'icon-chicun',
            label: t('尺寸'),
            disabled: false,
            divider: false,
        },
        store.homeStore.selectEntity?.type === 'Furniture' && !store.homeStore.selectEntity?.figure_element.haveMatchedMaterial() && {
            id: 'pos_z',
            icon: 'icon-lidi',
            label: t('离地'),
            disabled: false,
            divider: false,
        },
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        }
    ];
    commandBarData = commandBarData.filter(Boolean);

    const doorData = [
        {
            id: 'rotate',
            icon: 'icon-rotate',
            label: t('旋转'),
            disabled: false,
            divider: false,
        },
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        },
    ]
    const baywindowData = [
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        },
    ]
    const wallData = [
        {
            id: 'splitWall',
            icon: "iconsplit",
            label: t('拆分墙'),
        },
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        },
    ]
    const BaseGroup = [
        {
            id: 'rotate',
            icon: 'icon-rotate',
            label: t('旋转'),
            disabled: false,
            divider: false,
        },
        {
            id: 'ungroupTemplate',
            icon: 'icon-jiezu-2',
            label: t('解组'),
            disabled: false,
            divider: false,
        },
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        },
    ]
    // const defaultData = [
    // {
    //     id: 'delete',
    //     icon: 'icon-delete',
    //     label: t('删除'),
    //     disabled: false,
    //     divider: true,
    // },
    // ]

    let roomAreaData = [
        {
            id: "material",
            label: t("清除布局"),
            icon: "icon-shanchubuju",
            EventName: 'ClearLayout',
        },
        ...(layoutContainer._drawing_layer_mode !== 'SingleRoom' ? [
            {
                id: "LayoutLock",
                label: TSeriesFurnisher.instance.current_rooms[0]?.layoutLock ? t("解锁布局") : t("锁定布局"),
                icon: TSeriesFurnisher.instance.current_rooms[0]?.layoutLock ? "icon-jiesuobuju" : "icon-suodingbuju",
                EventName: 'clockLayout',
                onClick: () => {
                    TSeriesFurnisher.instance.current_rooms[0].layoutLock = !TSeriesFurnisher.instance.current_rooms[0].layoutLock;
                    LayoutAI_App.emit(EventName.RoomMaterialsUpdated, true);
                    LayoutAI_App.instance.update();
                }
            }] : []),
        ...(layoutContainer._drawing_layer_mode !== 'SingleRoom' ? [{
            id: "Matching",
            label: TSeriesFurnisher.instance.current_rooms[0]?.locked ? t("解锁风格") : t("锁定风格"),
            icon: TSeriesFurnisher.instance.current_rooms[0]?.locked ? "icon-jiesuofengge" : "icon-suodingfengge",
            EventName: 'clockStyle',
            onClick: () => {
                TSeriesFurnisher.instance.current_rooms[0].locked = !TSeriesFurnisher.instance.current_rooms[0].locked;
                LayoutAI_App.emit(EventName.RoomMaterialsUpdated, true);
                LayoutAI_App.instance.update();
            }
        }] : []),
        {
            id: "clearSeries",
            label: t("清除风格"),
            icon: "icon-qingchufengge",
            onClick: () => {
                TSeriesFurnisher.instance.deleteSeriesSample();
            }
        },
        {
            id: "searchMaterial",
            label: t("查看素材"),
            icon: "icon-sucai",
            onClick: () => {
                LayoutAI_App.emit(LayoutPopEvents.showPopup, 'searchMaterial');
            }
        },
        !store.homeStore.isSingleRoom && store.homeStore.viewMode === '2D'
            ? {
                id: "focusSpace",
                label: t("专注空间"),
                icon: "icon-zhuanzhukongjian",
                EventName: "SingleRoomLayout",
                onClick: () => {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.SingleRoomLayout, store.homeStore.selectEntity);
                    store.homeStore.setIsSingleRoom(true);
                }
            }
            : null,

        store.homeStore.isSingleRoom && store.homeStore.viewMode === '2D'
            ? {
                id: "exit",
                label: "",
                icon: "icon-a-tianchongFace-1",
                onClick: () => {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
                    store.homeStore.setIsSingleRoom(false);
                }
            }
            : null,
    ]
    roomAreaData = roomAreaData.filter(item => item !== null);




    useEffect(() => {
        LayoutAI_App.on_M(EventName.SelectingTarget, object_id, (params) => {
            let Entity: TBaseEntity = params || null;
            store.homeStore.setSelectEntity(params);
            if (!params) {
                store.homeStore.setShowReplace(false);
            }
            let entity_type: IRoomEntityType = Entity?.type || null;
            let entity_label = params?.ex_prop?.label || null;
            if (entity_type === "Furniture") {
                let groupData: any = [];
                groupData = [...commandBarData];

                if (Entity?.matched_rect) {
                    groupData.splice(groupData.length - 1, 0, {
                        id: 'replace',
                        icon: 'icon-change_logo',
                        label: t('替换'),
                        disabled: false,
                        divider: false,
                    });
                }
                setCommandBar(groupData);
            }
            else if (entity_type === "Group") {
                let groupData: any = [];
                groupData = commandBarData.concat([
                    {
                        id: 'replace',
                        icon: 'icon-change_logo ',
                        label: t('替换'),
                        disabled: false,
                        divider: false,
                    },
                ])
                setCommandBar(groupData);
            }
            else if (entity_type === "BaseGroup") {
                let groupData: any = [];
                groupData = [...BaseGroup];
                if (Entity?.matched_rect) {
                    groupData.splice(groupData.length - 1, 0, {
                        id: 'replace',
                        icon: 'icon-change_logo',
                        label: t('替换'),
                        disabled: false,
                        divider: false,
                    });
                }
                setCommandBar(BaseGroup);
            }
            else if (entity_type === "Door" || entity_type === "Window") {
                if (entity_label === "baywindow") {
                    setCommandBar(baywindowData);
                    return;
                }
                setCommandBar(doorData);
            }
            else if (entity_type === "Wall") {
                setCommandBar(wallData);
            } else if (entity_type === "StructureEntity") {
                setCommandBar([
                    {
                        id: 'delete',
                        icon: 'icon-delete',
                        label: t('删除'),
                        disabled: false,
                        divider: true,
                    },
                ]);
            }
        });
        fetchConfig();
    }, []);

    useEffect(() => {
        if (store.homeStore.viewMode === '3D_FirstPerson') /*[i18n:ignore]*/ {
            setBtnList(manyou_bar_list);
        }
        else {
            setBtnList(whole_bar_list);
        }
    }, [store.homeStore.viewMode]);

    useEffect(() => {

        // 漫游模式下，点击非家具，弹出替换弹窗
        if (store.homeStore.viewMode === '3D_FirstPerson' && store.homeStore.selectEntity?.type !== 'Furniture') /*[i18n:ignore]*/ {
            LayoutAI_App.emit(LayoutPopEvents.showPopup, '');
            setIsVisible(true);
            return;
        }

        // 漫游模式下，点击家具，弹出替换弹窗
        if (store.homeStore.viewMode === '3D_FirstPerson' && store.homeStore.selectEntity?.type === 'Furniture') /*[i18n:ignore]*/ {
            LayoutAI_App.emit(LayoutPopEvents.showPopup, 'replace');
            setIsVisible(false);
            return;
        }

        // 正常模式下，点击对应模块，弹出对应弹窗
        let entity_type: IRoomEntityType = store.homeStore.selectEntity?.type || null;
        handleComponentChange(entity_type);

    }, [store.homeStore.selectEntity?.type])

    const [isVisible, setIsVisible] = useState<boolean>(true); // 控制组件可见性
    const handleComponentChange = (id: string) => {
        setIsVisible(false);
        setTimeout(() => {
            setActiveComponent(id || 'null');
            setIsVisible(true);
        }, 300);
    };


    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const [position, setPosition] = useState({ top: screenHeight - 80, left: screenWidth / 2 });
    const [isDragging, setIsDragging] = useState(false);
    const statusBarRef = useRef<HTMLDivElement>(null);
    const initialPosition = useRef({ top: 0, left: 0 });
    const initialTouch = useRef({ x: 0, y: 0 });
    const [columflex, setColumflex] = useState(false);
    const handleTouchStart = (e: React.TouchEvent) => {
        setIsDragging(true);
        initialPosition.current = { top: position.top, left: position.left }; // 记录初始位置
        initialTouch.current = { x: e.touches[0].clientX, y: e.touches[0].clientY }; // 记录初始触摸点
    };

    const updatePosition = (top: number, left: number, isCircle: boolean, columflexState: boolean = false) => {
        setPosition({ top, left });
        setIsCircle(isCircle);
        setColumflex(columflexState);
    };
    const handleTouchMove = (e: React.TouchEvent) => {
        e.preventDefault();
        if (isDragging) {
            const touch = e.touches[0];
            const newTop = touch.clientY;
            const newLeft = touch.clientX;
            let limit = !LayoutAI_App.instance._is_landscape ? 150 : 100;
            // 判断当前区域
            if (newLeft < limit && newTop > limit && newTop < screenHeight - limit) {
                updatePosition(newTop - 370 / 2, newLeft, false, true); // 左侧区域
            } else if (newLeft > screenWidth - limit && newTop > limit && newTop < screenHeight - limit) {
                updatePosition(newTop - 370 / 2, newLeft, false, true); // 右侧区域
            } else if (newTop < limit && newLeft > limit && newLeft < screenWidth - limit) {
                updatePosition(newTop, newLeft, false, false); // 上侧区域
            } else if (newTop > screenHeight - limit && newLeft > limit && newLeft < screenWidth - limit) {
                updatePosition(newTop, newLeft, false, false); // 下侧区域
            } else if (newLeft < limit && newTop < limit) {
                updatePosition(newTop, newLeft, true); // 左上角
            } else if (newLeft > screenWidth - limit && newTop < limit) {
                updatePosition(newTop, newLeft, true); // 右上角
            } else if (newLeft < limit && newTop > screenHeight - limit) {
                updatePosition(newTop, newLeft, true); // 左下角
            } else if (newLeft > screenWidth - limit && newTop > screenHeight - limit) {
                updatePosition(newTop, newLeft, true); // 右下角
            } else {
                updatePosition(newTop, newLeft, true); // 中间区域
            }
            setIsMove(true);
        }
    };
    function isPointInTriangle(pt: any, v1: any, v2: any, v3: any) {
        const area = (v1: any, v2: any, v3: any) => Math.abs((v1.x * (v2.y - v3.y) + v2.x * (v3.y - v1.y) + v3.x * (v1.y - v2.y)) / 2);

        const A = area(v1, v2, v3); // 原三角形面积
        const A1 = area(pt, v2, v3); // 点与两个顶点构成的小三角形面积
        const A2 = area(v1, pt, v3);
        const A3 = area(v1, v2, pt);

        return A === A1 + A2 + A3; // 判断面积之和是否等于原三角形面积
    }

    const handleTouchEnd = (e: TouchEvent) => {
        if (!isMove) {
            return;
        }
        const touch = e.changedTouches[0];
        const newLeft = touch.clientX;
        const newTop = touch.clientY;

        // 计算屏幕中心点
        const centerX = screenWidth / 2;
        const centerY = screenHeight / 2;
        let limit = !LayoutAI_App.instance._is_landscape ? 150 : 100;
        // 判断区域
        if (newLeft < limit && newTop < limit) {
            updatePosition(20, 50, true); // 左上角
        } else if (newLeft > screenWidth - limit && newTop < limit) {
            updatePosition(20, screenWidth - 50, true); // 右上角
        } else if (newLeft < limit && newTop > screenHeight - limit) {
            updatePosition(screenHeight - 80, 50, true); // 左下角
        } else if (newLeft > screenWidth - limit && newTop > screenHeight - limit) {
            updatePosition(screenHeight - 80, screenWidth - 50, true); // 右下角
        } else if (isPointInTriangle({ x: newLeft, y: newTop }, { x: 0, y: 0 }, { x: screenWidth, y: 0 }, { x: centerX, y: centerY })) {
            updatePosition(30, screenWidth / 2, false); // 上三角
        } else if (isPointInTriangle({ x: newLeft, y: newTop }, { x: 0, y: screenHeight }, { x: screenWidth, y: screenHeight }, { x: centerX, y: centerY })) {
            updatePosition(screenHeight - 80, screenWidth / 2, false); // 下三角
        } else if (isPointInTriangle({ x: newLeft, y: newTop }, { x: 0, y: 0 }, { x: 0, y: screenHeight }, { x: centerX, y: centerY })) {
            updatePosition((screenHeight - 400) / 2, 50, false, true); // 左三角
        } else if (isPointInTriangle({ x: newLeft, y: newTop }, { x: screenWidth, y: 0 }, { x: screenWidth, y: screenHeight }, { x: centerX, y: centerY })) {
            updatePosition((screenHeight - 400) / 2, screenWidth - 50, false, true); // 右三角
        } else {
            updatePosition(screenHeight - 80, screenWidth / 2, false, false);
        }
        setIsMove(false);
    };

    const commonStyle = () => {
        return {
            position: 'fixed',
            top: position.top,
            left: position.left,
            maxWidth: isCircle ? '64px' : (columflex ? '68px' : '550px'),
            maxHeight: isCircle ? '64px' : (columflex ? '550px' : '64px'),
            minWidth: '64px',
            minHeight: '64px',
            background: '#FFFFFF',
            boxShadow: '0px 6px 20px 0px #0000001E',
            cursor: 'grab',
            transition: `${columflex ? 'max-height' : 'max-width'} 0.5s, opacity 0.3s, borderRadius 0.5s`,
            overflow: 'hidden',
            transformOrigin: 'center',
            padding: `${columflex ? '0px 8px' : '12px 0 8px 0px'}`,
            flexDirection: columflex ? 'column' : 'row',
        };
    }

    const leftRightRadius = () => {
        return <div style={{ borderTopLeftRadius: '50%', width: !columflex ? 24 : 64, height: !columflex ? 64 : 24, backgroundColor: '#fff' }}></div>
    }

    const circleIcon = () => {
        return isCircle && <IconFont type="icon-a-TypegongjuStateDefault" style={{ fontSize: '31px', color: '#282828', margin: '-3px 14px 0px 17px' }} onClick={showToolBar}></IconFont>
    }

    const showToolBar = () => {
        updatePosition(screenHeight - 80, screenWidth / 2, false, false);
    }



    useEffect(() => {
        const statusBarElement = statusBarRef.current;
        if (statusBarElement) {
            statusBarElement.addEventListener('touchend', handleTouchEnd);
        }

        return () => {
            if (statusBarElement) {
                statusBarElement.removeEventListener('touchend', handleTouchEnd);
            }
        };
    }, [isDragging, isMove]);

    useEffect(() => {
        showToolBar();
    }, [store.homeStore.IsLandscape]);

    useEffect(() => {
        if (store.homeStore.zIndexOf3DViewer === 4) {
            renderComponent();
        }
    }, [store.homeStore.zIndexOf3DViewer]);

    const fetchConfig = async () => {
        try {
            const response = await fetch('https://3vj-render.3vjia.com/config/3d/aidraw.json');
            const data = await response.json();
            setConfig(data);
            console.log('获取到配置数据:', config);
        } catch (error) {
            console.error('获取配置失败:', error);
        }
    };

    const submitAiDraw = async () => {
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let canvasElement = document.createElement("canvas");
        let current_canvas = ((LayoutAI_App.instance as TAppManagerBase).scene3D as Scene3D).renderer.domElement as HTMLCanvasElement;
        canvasElement.width = current_canvas.width;
        canvasElement.height = current_canvas.height;
        let ctx = canvasElement.getContext("2d");
        ctx.drawImage(current_canvas, 0, 0, current_canvas.width, current_canvas.height);
        const imgUrl = await uploadImageToOss(canvasElement.toDataURL());
        // 获取选中房间对应的配置 - 使用模糊匹配
        const selectedRoomConfig = config.rooms?.find((room: any) => {
            // 如果选中房间的名称包含配置中的房间名称，则认为匹配
            return store.homeStore.guideMapCurrentRoom?.name?.includes(room.text) || room.text?.includes(store.homeStore.guideMapCurrentRoom?.name || '');
        });
        const params = {
            schemeId: container._layout_scheme_id,
            diffuseImage: imgUrl,
            imageNum: 1,
            stylizationImage: "",
            imageWidth: 1920,
            imageHeight: 1080,
            inspiration: selectedRoomConfig?.inspire || store.homeStore.guideMapCurrentRoom?.name,
            stylized: "",
            rooms: selectedRoomConfig?.value || store.homeStore.guideMapCurrentRoom?.name,
            designStyle: "modern",
            layoutId: container.hxId,
            layoutName: container._layout_scheme_name,
            aiModel: store.homeStore.guideMapCurrentRoom.furnitureList.length === 0 ? 3 : 0,
        };

        const res = await AIGCService.instance.aiDrawImage(params);
        if (res.success) {
            message.success(t('提交成功'));
        } else {
            message.error(t('提交失败'));
        }
    }


    const renderComponent = () => {
        switch (activeComponent) {
            case "null":
                return (
                    <div ref={statusBarRef} onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} style={commonStyle() as any} className={`${styles.root} ${isVisible ? styles.show : styles.show}`}>
                        {!columflex && !isCircle && <div className="topLine"></div>}
                        {circleIcon()}
                        {leftRightRadius()}
                        <>
                            {
                                btnList.map((btn, index) => {
                                    return (
                                        <div style={{ margin: columflex ? '8px 0' : '0 8px' }} className={styles.btnInfo} key={index}
                                            onClick={async () => {
                                                if (btn.id === 'aidraw') {
                                                    if (layoutContainer._room_entities.length == 0) {
                                                        message.warning(t('请先创建方案'));
                                                    } else {
                                                        LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                                                        setTimeout(() => {
                                                            navigate('/aidraw?from=mobilehome');
                                                        }, 500);
                                                    }
                                                } else if (btn.id === 'similar') {
                                                    store.homeStore.setShowDreamerPopup(true);
                                                } else if (btn.id === 'create') {
                                                    LayoutAI_App.emit(EventName.OpenHouseSearching, true);
                                                    return;
                                                } else if (btn.id === 'submitDrawing') {
                                                    if (!layoutContainer._layout_scheme_id) {
                                                        LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                                                        setTimeout(async () => {
                                                            await submitAiDraw();
                                                        }, 2000);
                                                        return;
                                                    }
                                                    await submitAiDraw();
                                                    return;
                                                } else if (btn.id === 'atlas') {
                                                    if (layoutContainer._room_entities.length == 0) {
                                                        message.warning(t('请先创建方案'));
                                                    } else {
                                                        LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                                                        store.homeStore.setShowAtlas(true);
                                                    }

                                                    return;
                                                }
                                                else {
                                                    LayoutAI_App.emit(LayoutPopEvents.showPopup, btn.id);
                                                    setKey(Math.floor(Math.random() * 10000));
                                                }
                                            }}
                                        >
                                            <div>
                                                <IconFont
                                                    type={btn.icon}
                                                    style={{
                                                        fontSize: '20px',
                                                        color: '#282828'
                                                    }}></IconFont>
                                            </div>
                                            <div>{btn.label}</div>
                                        </div>
                                    )
                                })
                            }
                        </>
                        {leftRightRadius()}
                    </div>
                );
            case "Furniture":
            case "BaseGroup":
            case "Door":
            case "Window":
                return <div ref={statusBarRef} onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} style={commonStyle() as any} className={`${styles.root} ${isVisible ? styles.show : styles.hide}`}>
                    {!columflex && !isCircle && <div className="topLine"></div>}
                    {leftRightRadius()}
                    {circleIcon()}                    {
                        commandBar.map((btn, index) => {
                            return (
                                <div className={styles.btnInfo} key={index}
                                    onClick={() => {
                                        switch (btn.id) {
                                            case 'rotate':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.RotateFurniture);
                                                break;
                                            case 'flip':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.FlipFurniture);
                                                break;
                                            case 'flip_vertical':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.FlipFurnitureVertical);
                                                break;
                                            case 'delete':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteFurniture);
                                                break;
                                            case 'deleteRuler':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteRuler);
                                                break;
                                            case 'copy':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.CopyFurniture);
                                                break;
                                            case 'combination':
                                                setIsModalOpen(true);
                                                break;
                                            case 'ungroupTemplate':
                                                LayoutAI_App.DispatchEvent(LayoutAI_Events.HandleUnGroupTemplate, {});
                                                store.homeStore.setKey(Date.now());
                                                break;
                                            case 'replace':
                                                LayoutAI_App.emit(LayoutPopEvents.showPopup, btn.id);
                                                setIsVisible(false);
                                                break;
                                            case 'size':
                                                store.homeStore.setSizeInfo({
                                                    type: 'size',
                                                    visible: true,
                                                });
                                                break;
                                            case 'pos_z':
                                                store.homeStore.setSizeInfo({
                                                    type: 'pos_z',
                                                    visible: true,
                                                });
                                                break;
                                            default:
                                                break;
                                        }
                                    }}
                                >
                                    {
                                        btn.divider && <div className='divider'></div>
                                    }
                                    <>
                                        <div>
                                            <IconFont
                                                type={btn.icon}
                                                style={{
                                                    fontSize: '20px',
                                                    color: '#282828'
                                                }}></IconFont>
                                        </div>
                                        <div>{btn.label}</div>
                                    </>

                                </div>
                            )
                        })
                    }
                    <>
                        <div>
                            <IconFont
                                type={'icon-a-tianchongFace-1'}
                                onClick={() => {
                                    LayoutAI_App.DispatchEvent(LayoutAI_Events.cleanSelect, null);
                                }}
                                style={{
                                    fontSize: '20px',
                                    color: '#BCBEC2'
                                }}></IconFont>
                        </div>
                    </>
                    {leftRightRadius()}
                </div>; // 替换为实际组件
            case "RoomArea":
                return (
                    <div ref={statusBarRef} onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} className={`${styles.root} ${isVisible ? styles.show : styles.hide}`} style={commonStyle() as any}>
                        {!columflex && !isCircle && <div className="topLine"></div>}
                        {circleIcon()}
                        {leftRightRadius()}
                        <>
                            {
                                roomAreaData.map((btn, index) => {
                                    return (
                                        <div className={styles.btnInfo} key={index} style={{ margin: columflex ? '4px 0' : '0 8px' }}
                                            onClick={() => {
                                                if (btn.onClick) {
                                                    btn.onClick();
                                                    setKey(Math.floor(Math.random() * 10000));
                                                } else {
                                                    if (btn?.EventName) {
                                                        setKey(Math.floor(Math.random() * 10000));
                                                        LayoutAI_App.DispatchEvent(btn?.EventName, store.homeStore.selectEntity);
                                                    }
                                                }
                                            }}
                                        >
                                            <div>
                                                <Icon
                                                    iconClass={btn.icon}
                                                    style={{
                                                        fontSize: '20px',
                                                        color: '#282828'
                                                    }}></Icon>
                                            </div>
                                            <div>{btn.label}</div>
                                        </div>
                                    )
                                })
                            }
                        </>
                        {leftRightRadius()}
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div className="statusBar" key={_key}>
            {renderComponent()}
            <Modal
                title={t("创建组合")}
                maskClosable={false}
                centered
                open={isModalOpen}
                footer={false}
                width={300}
                onCancel={() => {
                    setIsModalOpen(false);
                }}>

                <Select
                    placeholder={t('请选择组合类型')}
                    style={{ width: 240, margin: '10 auto' }}
                    onChange={(e: any) => {
                        setCombinatName(e)
                    }}
                    options={[
                        { value: '沙发组合', label: t('沙发组合') },
                        { value: '餐桌椅组合', label: t('餐桌椅组合') },
                        { value: '床具组合', label: t('床具组合') },
                        { value: '桌几组合', label: t('桌几组合') },
                        { value: '岛台组合', label: t('岛台组合') },
                        { value: '榻榻米组合', label: t('榻榻米组合') }
                    ]}
                />
                <div style={{ textAlign: 'center', marginTop: '10px' }}>
                    <Button onClick={() => {
                        setIsModalOpen(false);
                        LayoutAI_App.DispatchEvent(LayoutAI_Events.CreateCombination, combinatName);
                        message.success(t('组合创建成功'));
                    }} type='primary'>{t('确定')}</Button>
                </div>
            </Modal>
        </div>
    );

};

export default observer(StatusBar);
