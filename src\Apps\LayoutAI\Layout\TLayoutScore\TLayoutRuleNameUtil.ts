export class TLayoutRuleNameUtil
{
    private static _instance: TLayoutRuleNameUtil;

    private _bathRoomRuleNames: {[key:string]:string};

    private _livingRoomRuleNames: {[key:string]:string};

    private _entranceRoomRuleNames: {[key:string]:string};

    private _livingRoomSplitByPathRuleNames: {[key: string]:string};

    constructor()
    {
        this._bathRoomRuleNames = {
            toiletAreaSize: "马桶区大小",
            toiletFaceDoor: "马桶对门",
            toiletFrontAreaSize: "马桶前侧活动区域",
            toiletSize: "马桶尺寸",
            bathtubWithOther: "浴缸与其他障碍物检查",
            washHandSize: "洗手台/浴室柜最小尺寸检查",
            washHandFrontAreaSize: "洗手台/浴室柜前方活动区域",
            showerAreaSize: "淋浴区尺寸检查",
            figureOverlay: "素材互相干涉",
            figureLayonDoor: "布局挡门",
            flowMinDistance: "过道最短距离",
            IShowerRoomOpenDoor: "一字形淋浴房开口方向",
        };

        this._livingRoomRuleNames = {
            figureOverlay: "素材互相干涉",
            figureLayonDoor: "布局挡门",
            figureLayonWindow: "家具挡窗",
            cabinetFrontAreaLen: "柜体前方距离",
            diningTableFlowMinDistance: "餐桌椅子侧离障碍物距离",
            sofaAndTableMinDistance: "沙发与茶几的距离",
            sofaAndTVMinDistance: "沙发与电视的距离",
            sofaLayonOtherDistance: "沙发不靠墙后背检查",
            livingDiningFlowDistance: "客厅区和餐厅区的过道检查",
            flowMainFluency: "主动线检查",
            flowSubSofaAndTVMinDistance: "沙发到电视柜的次动线检查",
            flowSubSofaAndBalconyMinDistance: "沙发到阳台的次动线检查",
            flowSubDiningTableAndCabinetMinDistance: "餐桌到餐边柜的次动线检查",
            livingSpaceAreaRatio: "客厅面积占比",
            livingAreaLenAndWidthRatio: "客厅区有效区域长宽比",
            diningSpaceUsageRatio: "餐厅区空间利用率",
            livingRoomInTakeRatio: "客餐厅收纳面积比例",
            livingSpaceInTakeRatio: "客厅区收纳面积比例",
            diningSpaceInTakeRatio: "餐厅区收纳面积比例",
            livingSpaceUsage: "客厅空间利用率",
            livingAndDiningSpacePositon: "客厅和餐厅位置关系",
            livingAndDiningWholeness: "客餐厅分区的完整性",
            livingAndDiningSpaceMinArea: "客厅和餐厅区域最小尺寸",
            entranceCabinetLayout: "玄关柜布局",
            diningFunction: "餐厅区功能丰富度",
            diningTabelCenter: "餐桌中心点位置",
            livingFunction: "客厅区功能丰富度",
            sofaFaceentranceDoor: "沙发直对入户门",
            sofaCenter: "沙发中心点检查",
            livingAndDiningInWholeRoomAreaRatio: "区域面积利用率",

            diningTableNearKitchen: "餐桌靠近厨房",
        };

        this._entranceRoomRuleNames = {
            flowMainFluency: "主动线检查",
        };

        this._livingRoomSplitByPathRuleNames = {
            necessaryArea: "必备区域: (客厅区, 餐厅区)",
            livingAndDiningAreaRatio: "客厅区与餐厅区面积比",
            livingLenAndWidthRatio: "客厅区长宽比",
            diningLenAndWidthRatio: "餐厅区长宽比",
            spacePosition: "分区位置相对正确",
            minDiningLenAndDepth: "餐厅区长宽长度检查",
            minLivingLenAndDepth: "客厅区长宽长度检查",
            livingRoomSpaceUsage: "空间利用率",

            diningTableSpaceAreaLenAndDepth: "就餐区最小最大尺寸检查",
            livingSofaSpaceAreaLenAndDepth: "沙发活动区最小最大尺寸检查",

            livingRoomFunctionRichness: "家具丰富度",

            subFlowToMain: "主家具次动线",
        }
    }

    public static get instance(): TLayoutRuleNameUtil
    {
        if(!this._instance)
        {
            this._instance = new TLayoutRuleNameUtil();
        }
        return this._instance;
    }

    public get bathRoomRuleNames()
    {
        return this._bathRoomRuleNames;
    }
    
    public get livingRoomRuleNames()
    {
        return this._livingRoomRuleNames;
    }

    public get entranceRoomRuleNames()
    {
        return this._entranceRoomRuleNames;
    }

    public get livingRoomSplitByPathRuleNames()
    {
        return this._livingRoomSplitByPathRuleNames;
    }
}