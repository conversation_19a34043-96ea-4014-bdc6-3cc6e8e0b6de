import React, { useEffect, useState } from "react";
import useStyles from './style';
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TExtDrawingEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TExtDrawingEntity";


interface ViewCameraListProps {
    popupType?: string;
}
export const ViewCameraList :React.FC<ViewCameraListProps> = ({popupType})=>{

    const { styles } = useStyles();

    const [viewCameras, setViewCameras] = useState<TViewCameraEntity[]>([]);
    const generateViewCameras = ()=>{
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        TViewCameraEntity.updateViewCameraEntities(container);

        let viewCameras = container._ext_drawing_entities.filter((entity)=>entity.realType==="ViewCamera");

        viewCameras.forEach((view:TExtDrawingEntity)=>{
            if(view.realType==="ViewCamera")
            {
                // updateViewImg方法TViewCameraEntity有，TExtDrawingEntity没有
                (view as TViewCameraEntity).updateViewImg(container.painter,300,300);
            }
        });
        setViewCameras(viewCameras as any);
    }
    const bindViewCamera = (entity:TViewCameraEntity)=>{
        let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;

        if(scene3d.active_controls)
        {
            scene3d.active_controls.bindViewEntity(entity);

            
        }
    }

    useEffect(()=>{
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let viewCameras = container._ext_drawing_entities.filter((entity)=>entity.realType==="ViewCamera");
        setViewCameras(viewCameras as any);
        generateViewCameras();
    },[popupType]);
    return (<div className={styles.root}>
        {/* <div className="container_box" onClick={()=>generateViewCameras()}>
            <div>{t("生成视角")}</div>
        </div> */}
        <div className={styles.listContainer}>
            {viewCameras.map((entity,index)=><div className="container_box" key={"view_box_"+index} onClick={()=>bindViewCamera(entity)}>
                <img src={entity._view_img.src} />
            {index+1} : {entity.name}
            </div>)}
        </div>

    </div>)
}