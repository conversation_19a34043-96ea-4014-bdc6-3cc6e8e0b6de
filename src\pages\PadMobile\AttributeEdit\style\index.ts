import { createStyles } from '@svg/antd/es/theme/utils'

export default createStyles(({ css }: any) => {
  return {
    root: css`
      height: 300px;
      @media screen and (orientation: landscape) {
        height: calc(var(--vh, 1vh) * 85);
        width: 224px;
        margin-top: -40px;
        background: #fff;
        position: absolute;
      }
    `,
    roomListBar: css`
      height: 40px;
    `,
    name:css`
      position: fixed;
      top: 50px;
      right: 20px;
      z-index: 1200
    `,
    attributeInfo: css`
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      @media screen and (orientation: landscape) {
        flex-direction: column;
      }
      .svg-input-number
      {
        padding-right: 4px;
        @media screen and (max-width: 450px) { // 手机宽度
          width: 65px;
          font-size: 12px;
        }
      }
      .left
      {
        width: 46%;
        .title{
          margin: 12px 0 6px 0px;
        }
        .houseInfo
        {
          .input
          {
            width: 50px;
            margin-right: 4px;
            @media screen and (max-width: 450px) { // 手机宽度
              width: 45px;
              margin-top: 10px;
            }
          }

          span{
            margin-right: 13px;
            font-size: 14px;
          }
        }
        .leftInfo
        {
          display: flex;
        }

      }
      .right{
        width: 46%;
        .title{
          margin: 12px 0 6px 0px;
        }
        .rightInfo
        {
          display: flex;
          
        }
      }
      .title{
        @media screen and (orientation: landscape) {
          font-size: 14px;
          margin: 16px 0 16px 0px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      .houseInfo{
        @media screen and (orientation: landscape) {
          display: flex;
          flex: 0 0 50%;
          font-size: 14px;
          flex-wrap: wrap;
          align-items: center;
          div{
            display: flex;
            align-items: center;
            margin-right: 8px;
            margin-bottom: 12px;
            align-items: center;
            .input{
              width: 64px;
            }
            span{
              margin-left: 4px;
            }
          }
        }
      }
    `
  }
})