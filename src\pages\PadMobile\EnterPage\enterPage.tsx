import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { Button, Dropdown, message, Popover, Segmented  } from "@svg/antd";
import { useEffect, useRef, useState } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { is_dreamer_mini_App, mode_type } from "@/config";
import { If, Then, Else } from 'react-if';
import IconFont from "@/components/IconFont/iconFont";
import SelectHouse from "@/pages/Trial/SelectHouse/SelectHouse";
import { workDomainMap } from "@/config";
import { CSSTransition } from 'react-transition-group';
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample";
import { getKgSchemeList } from "@/Apps/AI2Design/Services";
import { FileOpenResult, openFileInput } from "@/Apps/LayoutAI/Utils/file_utils";
import { SdkService } from "@/services/SdkService";
import { Icon } from "@svg/antd-cloud-design";;
import { PERMISSIONS } from "@/config/permissions";
import { Permissions } from "@/Apps/LayoutAI/setting/Permissions";
import { checkIsMobile } from "@layoutai/basic_data";
import StartDesignPage from "../StartDesignPage/startDesignPage.tsx";
import { AI2DesignBasicModes } from "@/Apps/AI2Design/AI2DesignManager.ts";
import Useravatar from "@/components/Useravatar/Useravatar.tsx";
/**
 * @description 主页
 */
export enum LightMainEvents {
    showLight3DViewer = "showLight3DViewer"
}
interface Params {
    orderBy: string;
    ruleType: number;
    pageSize: number;
    pageIndex: number;
    schemeKeyWord: string;
    ruleKeyWord: string;
    spaceName: any;
    schemeStyleId: string;
    ruleStyleId: string;
    queryType: number;
}

const EnterPage: React.FC = () => {
    const roomRef = useRef(null);
    const { t } = useTranslation();
    const { styles } = useStyles();
    const [seriesSelected, setSeriesSelected] = useState<TSeriesSample>(null);
    const [recordList, setRecordList] = useState<any>([]);
    let store = useStore();
    // -1 ==> “开始设计”页； 0 ==> “找户型”页； 1 ==> “选需求”页
    const [step, setStep] = useState<number>(store.userStore.isHaiEr ? -1 : 0);
    const [demandList, setDemandList] = useState<any[]>( [
        {
            label: '居住人口',
            multiple: false,
            tabList: [
                {label: '单身独居',selected: false },
                {label: '二人世界',selected: false },
                {label: '三口之家',selected: false },
                {label: '四口之家',selected: false },
                {label: '多代同堂',selected: false },
            ]
        },
        {
            label: '房屋类型',
            multiple: false,
            tabList: [
                { label: '毛坯房', selected: false },
                { label: '精装修', selected: false },
                { label: '旧房改造', selected: false }
            ]
        },
        {
            label: '功能需求',
            multiple: true,
            tabList: [
                { label: '聚会', selected: false },
                { label: '品茗', selected: false },
                { label: '健身', selected: false },
                { label: '绿植', selected: false },
                { label: '收纳', selected: false },
                { label: '梳妆', selected: false },
                { label: '休闲', selected: false },
                { label: '西厨', selected: false },
                { label: '宠物', selected: false },
                { label: '办公', selected: false },
                { label: '适老', selected: false },
                { label: '孩童', selected: false },
            ]
        },
        {
            label: '装修预算',
            multiple: false,
            tabList: [
                { label: '10万以下', selected: false },
                { label: '10-20万', selected: false },
                { label: '20-50万', selected: false },
                { label: '50万以上', selected: false }
            ]
        }
    ]);

    const [prams, setPrams] = useState<Params>({
        orderBy: 'sort asc',
        ruleType: store.userStore.userInfo?.isFactory ? 2 : 1,
        pageSize: 50,
        pageIndex: 1,
        schemeKeyWord: '',
        ruleKeyWord: '',
        spaceName: null,
        schemeStyleId: '',
        ruleStyleId: '',
        queryType: 2,
    });


    /**
     * @description 获取左侧列表数据
     */
    const getRoomList = async () => {
        const res = await getKgSchemeList(prams);
        if (res?.result) {
        const data = Array.isArray(recordList) && recordList.length > 0 && (prams.pageIndex > 1)
            ? [...recordList, ...(res?.result || [])]
            : res?.result || [];
            setRecordList(data);

            if(step === 1 && !seriesSelected)
            {
                setSeriesSelected(data[0]);
            }
        }
        else {
            setRecordList([]);
        }
    }

    const onOpenMyScheme = ()=>{
        LayoutAI_App.emit(EventName.OpenMySchemeList, null);
        }
    
        const onUploadHxImg = async()=>{
        let fileResult: FileOpenResult = await openFileInput('image/*').catch(():null => null);
        if(fileResult.content)
        {
            store.homeStore.setImgBase64(fileResult.content);
            message.success(t('上传户型图成功'));
            setStep(1);
        } else 
        {
            message.warning(t('上传户型图失败'));
        }
    }

    useEffect(() => {
        LayoutAI_App.on_M(EventName.xmlSchemeLoaded, 'enterPage', (data: any) => {
            if(data.mode === 'Finish' && seriesSelected)
            {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.SeriesSampleSelected, { series: seriesSelected, scope: { soft: true, hard: true, cabinet: true, remaining: false } });
                let funcRequire = '';
                demandList.forEach((item: any) => {
                    item.tabList.forEach((tab: any) => {
                        if(tab.selected)
                        {
                            funcRequire += tab.label + ',';
                        }
                    });
                });
                
                funcRequire = funcRequire.slice(0, -1);
                (LayoutAI_App.instance as TAppManagerBase).layout_container._funcRequire = funcRequire;
                setSeriesSelected(null);
                store.trialStore.setHouseData(null);
                // 只有进入的第一次有用，后续有需求需要重写进入这个页面就再看看
                LayoutAI_App.off(EventName.xmlSchemeLoaded);     
            }
            store.homeStore.setRoomEntites((LayoutAI_App.instance as TAppManagerBase).layout_container._room_entities);
        });
    }, [seriesSelected,demandList]);
    const handleTabClick = (clickedTab: any, label: string) => {
        let newTabList = demandList.map((item) => {
            if (item.label === label) { // 只更新当前 label 的 tabList
                return {
                    ...item,
                    tabList: item.tabList.map((tabItem: any) => {
                        if (item.multiple) {
                            // 如果是多选，切换选中状态
                            return {
                                ...tabItem,
                                selected: tabItem.label === clickedTab.label ? !tabItem.selected : tabItem.selected
                            };
                        } else {
                            // 如果是单选，确保只有当前选中的 tabItem 被选中
                            return {
                                ...tabItem,
                                selected: tabItem.label === clickedTab.label
                            };
                        }
                    })
                };
            }
            return item; // 其他 label 不变
        });
        setDemandList(newTabList);
    };

    const backClick = () => {
        if(step === 0 && store.userStore.isHaiEr)
        {
            setStep(-1);
            return;
        } else if(step === 0)
        {
            SdkService.exitSDK();
            window.parent.postMessage({
                origin: 'layoutai.api',
                type: 'canClose',
                data: {
                    canClose: true
                }
            }, '*');
            // store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: 'null'});
            window.location.href = workDomainMap;
        }
        if(step === 1){
            setStep(0);
        }
    }



    const selectHX_header = () => {
        return (
            
            <div className={styles.hxHeader}>
                <div className='title'>
                    <div 
                        className={'back'} 
                        onClick={backClick} 
                        style={{
                            // 首次进入的时候不展示返回按钮
                            display: ((checkIsMobile() && (is_dreamer_mini_App || !store.homeStore.isFirstEnter) && step === 0) || store.userStore.isHaiEr) ? 'block' : 'none'}}>
                        <IconFont type="icon-line_left" />
                    </div>
                    <If condition={step === 0}>
                        <Then>
                            <span>{t('找户型')}</span>
                        </Then>
                    </If>
                    <If condition={step === 1}>
                        <Then>
                            <span>{t('选需求')}</span>
                        </Then>
                    </If>
                </div>

                <div style={{display: store.userStore.isHaiEr ? 'none' : 'flex', position: 'fixed', right: '20px', top: '20px', alignItems: 'center'}}>
                    <div style={{marginRight: '10px'}}>
                        <Button type="primary" className='mySchemeButton' color="orange" variant="filled" onClick={() => {store.homeStore.setShowMySchemeList(true); setSeriesSelected(null);}}>
                            <Icon iconClass="iconwenjianjia" style={{fontSize: '12px'}}/>
                            <div style={{color: 'rgba(0, 0, 0, 0.8)'}}>我的方案</div>
                        </Button>
                    </div>

                    <div style={{marginRight: '10px'}}>

                        <Button type="primary" className='myAtlasButton' color="purple" variant="filled" onClick={() => {store.homeStore.setShowAtlas(true);}}>
                            <Icon iconClass="icontuce1" style={{fontSize: '12px'}}/>
                            <div style={{color: 'rgba(0, 0, 0, 0.8)'}}>我的图册</div>
                        </Button>
                    </div>
     
                    <If condition={!is_dreamer_mini_App}>
                        <Then>
                            <Popover placement="bottomRight" trigger="click" content={<Useravatar></Useravatar>}>
                                <div>
                                    <img
                                        src={store.userStore.userInfo?.headerPic || "https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/project/3d-design/headimg.png"}
                                        style={{ width: '36px', height: '36px', borderRadius: '50%' }}
                                    />
                                </div>
                            </Popover>
                        </Then>
                    </If>
                </div>
            </div>
        )
    }
    useEffect(() => {
        // if(mode_type === 'HouseId' || mode_type === 'CopyingBase64')
        if(mode_type === 'HouseId')
        {
            setStep(1);
        }

        /**
         * @description 海尔项目点击户型库，直接跳转到画布加载户型
         */
        LayoutAI_App.on(EventName.webLoadByBuidlingId, (data: any) => {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.Init, null);    
            store.homeStore.setShowEnterPage({show: false, source: ''});
       
            LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, 
            {
                id: data.building_id, 
                name:data?.layoutSchemeName,
                customer_info:{
                    cntactMan: data?.cntactMan,
                    mobile: data?.mobile
                },
                imagePath: '',
                auto_layout: true,
                auto_save: true
            });
        });

        /**
         * @description 海尔点击上传临摹图
         */
        LayoutAI_App.on(EventName.webLoadImitateImageFile, (data: any) => {
            if(!data?.base64_str)
            {
                message.warning('未识别到户型图');
                return;
            }
            LayoutAI_App.DispatchEvent(LayoutAI_Events.Init, null);
            store.homeStore.setShowEnterPage({show: false, source: ''});
            LayoutAI_App.DispatchEvent(LayoutAI_Events.LoadImitateImageFile, {
                imagePath: data.base64_str,
                name:data?.layoutSchemeName,
                customer_info:{
                    cntactMan: data?.cntactMan,
                    mobile: data?.mobile
                },
                auto_save: true,
                auto_identify: true
            });
        });


        LayoutAI_App.on(EventName.webLoadImitateDwgFile, (data: any) => {
            if(!data?.base64_str)
            {
                message.warning('未识别到DWG文件');
                return;
            }
            LayoutAI_App.DispatchEvent(LayoutAI_Events.Init, null);
            store.homeStore.setShowEnterPage({show: false, source: ''});
            LayoutAI_App.DispatchEvent(LayoutAI_Events.LoadImitateDwgFile, {
                dwgPath: data.base64_str,
                name:data?.layoutSchemeName,
                customer_info:{
                    cntactMan: data?.cntactMan,
                    mobile: data?.mobile
                },
                auto_save: true,
                auto_identify: true
            });
        });


        LayoutAI_App.on(EventName.webFreeHxEdit, (data: any) => {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.Init, null);
            store.homeStore.setShowEnterPage({show: false, source: ''});
            if (LayoutAI_App.instance) {
                if ((LayoutAI_App.instance as TAppManagerBase)?.layout_container?._drawing_layer_mode == 'SingleRoom') {
                  LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
                }
            }
            LayoutAI_App.instance._current_handler_mode = AI2DesignBasicModes.HouseDesignMode;
            LayoutAI_App.RunCommand(AI2DesignBasicModes.HouseDesignMode);
            store.homeStore.setDesignMode(AI2DesignBasicModes.HouseDesignMode);
        });

    }, []);

    useEffect(() => {
        if(step === 1)
        {
            getRoomList();
        }
    }, [prams, step]);

    /**
     * @description 海尔项目点击户型直接跳转到画布加载户型
     */
    useEffect(() => {
        // 海尔项目特殊
        if(step === 0 && store.userStore.isHaiEr && store.trialStore?.houseData?.num && store.trialStore?.houseData.id)
        {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.Init, null); 
            store.homeStore.setShowEnterPage({show: false, source: ''});           
            LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, 
            {
                id: store.trialStore.houseData.id, 
                name:store.trialStore.houseData.buildingName,
                customer_info:{
                },
                imagePath: '',
                auto_layout: true,
                auto_save: true
            });
            return;
        }
        if(step === 0 && store.trialStore?.houseData?.id)
        {
            setStep(1);
            store.homeStore.setImgBase64(null);
        }
    }, [store.trialStore?.houseData?.num,store.userStore.isHaiEr]);

    /**
     * @description 底部按钮
     */
    const bottom = () => {
        return (
            <>
                {/* <If condition={step === 0}>
                </If> */}
                <If condition={step === 1}>
                    <Then>
                        <div className={styles.bottom}>
                            <div className={'bottomLeft'}>
                                <div className={'rotate'}
                                    onClick={() => {
                                        setSeriesSelected(null);
                                        setDemandList(demandList.map((item) => {
                                            return {
                                                ...item,
                                                tabList: item.tabList.map((tab:any) => ({...tab, selected: false}))
                                            };
                                        }));
                                    }}
                                >
                                    <IconFont type="icon-rotate" style={{marginRight: '5px'}} />
                                    {t('重置选项')}
                                </div>
                            </div>
                            <div className={'bottomRight'}>
                                <Button style={{marginRight: '10px'}} onClick={() => setStep(0)} color="default" variant="filled">
                                    {t('上一步')}
                                </Button>
                                <Button
                                    style={{background: 'linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%)',color: '#fff'}}
                                    onClick={() => {
                                
                                        if(!seriesSelected)
                                        {
                                            message.warning(t('请选择风格偏好'));
                                            return;
                                        }
                                        setStep(0);
                                        store.homeStore.setShowEnterPage({show: false, source: ''});
                                        
                                        if(store.homeStore.img_base64)
                                        {
                                            LayoutAI_App.DispatchEvent(LayoutAI_Events.LoadImitateImageFile, {imagePath: store.homeStore.img_base64, auto_identify: true});
                                        } else if(store.trialStore.houseData.id)
                                        {
                                            LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: store.trialStore.houseData.id, name:''});
                                        }
                                    }} color="default" variant="filled" 
                                >
                                    {t('下一步')}
                                </Button>
                            </div>
                        </div>
                    </Then>
                </If>
            </>
        )
    }

    /**
     * @description 选择户型
     */
    const selectHx = () => {
        return (
            <div className={styles.selectHx}>
                <div className={styles.hxRoot}>
                    <SelectHouse />
                </div>

                {/* <div className='right_btns'>
                    <div className='btn' onClick={onOpenMyScheme}>
                    <img src={'./static/icons/myPlan.svg'} alt="" /><span>我的方案</span>
                    </div>

                </div> */}
            </div>
        )
    }

    /**
     * @description 选择需求
     */
    const selectDemand = () => {
        return (
            <div className={styles.selectDemand}>
                {
                    demandList.map((item,index) => {
                        return (
                            <div className={'demandItem'}>
                                <div className={'demandLabel'} key={index}>{item.label}</div>
                                <div className={'tabRoot'}>
                                    {item.tabList.map((tab: any,index: number) => {
                                    return (
                                            <div 
                                                onClick={() => handleTabClick(tab,item.label)} 
                                                key={index} 
                                                className={'demandtab' + (tab.selected ? ' selected' : '')}>
                                                    {tab.label}
                                            </div>
                                        )
                                    })}
                                </div>
                            </div>
                        )
                    })
                }    
                <div className={styles.styleTitle}>
                    <div className={'demandLabel'}>
                        {t('风格偏好')}<span style={{color: '#959598', fontSize: '12px'}}>（{t('必选')}）</span>
                    </div>
                    <Segmented 
                        onChange={async(value) => {
                            setPrams(prev => ({
                                ...prev,
                                pageIndex: 1,
                                ruleType: value === '平台' ? 1 : 2
                            }));
                            setSeriesSelected(null);
                        }} 
                        defaultValue={store.userStore.userInfo?.isFactory ? '企业' : '平台'}
                        options={Permissions.instance.hasPermission(PERMISSIONS.SERIES.SHOW_PLATFORM_SERIES) ? [ '平台', '企业'] : ['企业']} />
                </div>
                <div className={`${styles.container_listInfo}`} ref={roomRef}>
                    <>
                    {recordList?.map?.((item: TSeriesSample, id: any) => (
                        <div
                            key={"series_"+id}
                            className={styles.container_list}
                            onClick={() => {
                                setSeriesSelected(item);
                            }}
                        >
                        <div 
                            style={{ border: seriesSelected?.ruleId === item.ruleId ? '2px solid #9242FB' : '2px solid #fff',borderRadius: '8px',overflow: 'hidden' }}
                        >
                            <img src={`${item.thumbnail}?x-oss-process=image/resize,m_fixed,h_218,w_318`} alt="" />
                            <div className={styles.container_title} title={item.seedSchemeName || item.ruleName}>{t(item.seedSchemeName) || t(item.ruleName)}</div>
                        </div>
                        </div>
                    ))}
                    </>
                </div>

            </div>
        )
    }

    // 作为props传递给子组件，用于改变step的值以达到切换到户型页的目的
    const toSelectHX = () => {
        setStep(0);
    };

    return (
        <div className={styles.enterPage}>

            {step === -1 && (
                <StartDesignPage toSelectHX={toSelectHX} step={step}/>
            )}

            {selectHX_header()}
            <div className='upload_hx' onClick={onUploadHxImg} style={{display: (step === 0 && !store.userStore.isHaiEr) ? 'block' : 'none'}}>
                <img style={{marginTop: '20px', width: '50px', height: 'auto'}} src={'https://3vj-fe.3vjia.com/layoutai/icons/upload.svg'} alt="" />
                <div  className='upload_title'>
                    上传户型
                </div>
            </div>
            <CSSTransition
                in={step === 0}
                timeout={300}
                classNames={step === 1 ? "slide-reverse" : "slide"}
                mountOnEnter
                appear
                style={{display: step === 0 ? 'block' : 'none'}}
            >
                {selectHx()}
            </CSSTransition>
            <CSSTransition
                in={step === 1}
                timeout={300}
                classNames={step === 0 ? "slide" : "slide-reverse"}
                mountOnEnter
                appear
                style={{display: step === 1 ? 'block' : 'none'}}
            >
                {selectDemand()}
            </CSSTransition>
            {bottom()}
        </div>
    );
};

export default observer(EnterPage);
